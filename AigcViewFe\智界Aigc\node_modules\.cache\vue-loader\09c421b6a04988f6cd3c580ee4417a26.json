{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue?vue&type=template&id=496f8674&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue", "mtime": 1753987025534}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"workflow-center\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">AI工作流中心</h1>\n      <p class=\"simple-subtitle\">发现和使用优质AI智能体，提升您的创作效率，让每个想法都能完美实现</p>\n    </div>\n\n  <!-- Tab导航 -->\n  <div class=\"workflow-tabs\">\n    <div class=\"tabs-container\">\n      <div class=\"tab-nav\">\n        <div\n          class=\"tab-item\"\n          :class=\"{ active: activeTab === 'market' }\"\n          @click=\"switchTab('market')\"\n        >\n          <a-icon type=\"shop\" />\n          <span>智能体市场</span>\n        </div>\n        <div\n          class=\"tab-item\"\n          :class=\"{ active: activeTab === 'creator' }\"\n          @click=\"switchTab('creator')\"\n        >\n          <a-icon type=\"build\" />\n          <span>创作者中心</span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Tab内容 -->\n  <div class=\"workflow-content\">\n    <div class=\"content-container\">\n      <!-- 智能体市场 -->\n      <div v-show=\"activeTab === 'market'\" class=\"tab-content\">\n        <AgentMarket />\n      </div>\n\n      <!-- 创作者中心 -->\n      <div v-show=\"activeTab === 'creator'\" class=\"tab-content\">\n        <CreatorCenter />\n      </div>\n    </div>\n  </div>\n  </div>\n</WebsitePage>\n", null]}