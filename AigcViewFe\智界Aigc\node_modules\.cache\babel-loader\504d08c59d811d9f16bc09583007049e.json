{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue", "mtime": 1753968167936}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getAction, httpAction } from '@/api/manage';\nimport { FormTypes, getRefPromise, VALIDATE_NO_PASSED, validateFormModelAndTables } from '@/utils/JEditableTableUtil';\nimport { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin';\nimport { validateDuplicateValue } from '@/utils/util';\nimport JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred';\nexport default {\n  name: 'AigcAgentForm',\n  mixins: [JEditableTableModelMixin],\n  components: {\n    JImageUploadDeferred: JImageUploadDeferred\n  },\n  data: function data() {\n    return {\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 6\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 16\n        }\n      },\n      labelCol2: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 3\n        }\n      },\n      wrapperCol2: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 20\n        }\n      },\n      model: {\n        authorType: '1',\n        // 默认作者类型：官方\n        auditStatus: '2' // 默认审核状态：审核通过\n\n      },\n      // 新增时子表默认添加几行空数据\n      addDefaultRowNum: 1,\n      validatorRules: {\n        authorType: [{\n          required: true,\n          message: '请输入作者类型!'\n        }],\n        agentId: [{\n          required: true,\n          message: '请输入智能体ID!'\n        }],\n        agentName: [{\n          required: true,\n          message: '请输入智能体名称!'\n        }],\n        agentDescription: [{\n          required: true,\n          message: '请输入智能体描述!'\n        }],\n        agentAvatar: [{\n          required: true,\n          message: '请输入智能体头像!'\n        }],\n        price: [{\n          required: true,\n          message: '请输入价格（元）!'\n        }],\n        auditStatus: [{\n          required: true,\n          message: '请输入审核状态!'\n        }]\n      },\n      refKeys: ['aigcWorkflow'],\n      tableKeys: ['aigcWorkflow'],\n      activeKey: 'aigcWorkflow',\n      // 工作流表\n      aigcWorkflowTable: {\n        loading: false,\n        dataSource: [],\n        columns: [{\n          title: '智能体ID',\n          key: 'agentId',\n          type: FormTypes.hidden,\n          defaultValue: ''\n        }, {\n          title: '工作流ID',\n          key: 'workflowId',\n          type: FormTypes.input,\n          width: \"200px\",\n          placeholder: '请输入${title}',\n          defaultValue: ''\n        }, {\n          title: '工作流名称',\n          key: 'workflowName',\n          type: FormTypes.input,\n          width: \"200px\",\n          placeholder: '请输入${title}',\n          defaultValue: ''\n        }, {\n          title: '工作流描述',\n          key: 'workflowDescription',\n          type: FormTypes.input,\n          width: \"200px\",\n          placeholder: '请输入${title}',\n          defaultValue: ''\n        }, {\n          title: '工作流压缩包',\n          key: 'workflowPackage',\n          type: FormTypes.file,\n          token: true,\n          responseName: \"message\",\n          width: \"200px\",\n          placeholder: '请选择文件',\n          defaultValue: ''\n        }]\n      },\n      url: {\n        add: \"/aigc_agent/aigcAgent/add\",\n        edit: \"/aigc_agent/aigcAgent/edit\",\n        queryById: \"/aigc_agent/aigcAgent/queryById\",\n        aigcWorkflow: {\n          list: '/aigc_agent/aigcAgent/queryAigcWorkflowByMainId'\n        }\n      }\n    };\n  },\n  props: {\n    //表单禁用\n    disabled: {\n      type: Boolean,\n      default: false,\n      required: false\n    }\n  },\n  computed: {\n    formDisabled: function formDisabled() {\n      return this.disabled;\n    }\n  },\n  created: function created() {},\n  methods: {\n    addBefore: function addBefore() {\n      this.aigcWorkflowTable.dataSource = [];\n    },\n    getAllTable: function getAllTable() {\n      var _this = this;\n\n      var values = this.tableKeys.map(function (key) {\n        return getRefPromise(_this, key);\n      });\n      return Promise.all(values);\n    },\n\n    /** 调用完edit()方法之后会自动调用此方法 */\n    editAfter: function editAfter() {\n      this.$nextTick(function () {}); // 加载子表数据\n\n      if (this.model.id) {\n        var params = {\n          id: this.model.id\n        };\n        this.requestSubTableData(this.url.aigcWorkflow.list, params, this.aigcWorkflowTable);\n      }\n    },\n    //校验所有一对一子表表单\n    validateSubForm: function validateSubForm(allValues) {\n      var _this2 = this;\n\n      return new Promise(function (resolve, reject) {\n        Promise.all([]).then(function () {\n          resolve(allValues);\n        }).catch(function (e) {\n          if (e.error === VALIDATE_NO_PASSED) {\n            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n            _this2.activeKey = e.index == null ? _this2.activeKey : _this2.refKeys[e.index];\n          } else {\n            console.error(e);\n          }\n        });\n      });\n    },\n\n    /** 整理成formData */\n    classifyIntoFormData: function classifyIntoFormData(allValues) {\n      var main = Object.assign(this.model, allValues.formValue);\n      return _objectSpread(_objectSpread({}, main), {}, {\n        // 展开\n        aigcWorkflowList: allValues.tablesValue[0].values\n      });\n    },\n    validateError: function validateError(msg) {\n      this.$message.error(msg);\n    },\n    // 视频上传前验证\n    beforeVideoUpload: function beforeVideoUpload(file) {\n      // 检查文件类型\n      var isVideo = file.type.indexOf('video/') === 0;\n\n      if (!isVideo) {\n        this.$message.error('只能上传视频文件!');\n        return false;\n      } // 检查文件大小（100MB）\n\n\n      var isLt100M = file.size / 1024 / 1024 < 100;\n\n      if (!isLt100M) {\n        this.$message.error('视频大小不能超过 100MB!');\n        return false;\n      }\n\n      console.log('🎯 视频上传验证通过:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB');\n      return true;\n    },\n    // 🔥 重写handleOk方法，集成延迟上传逻辑\n    handleOk: function () {\n      var _handleOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var _this3 = this;\n\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                console.log('🎯 AigcAgentForm: 开始保存智能体...');\n                /** 先处理头像上传，更新model值 */\n\n                _context2.prev = 1;\n                _context2.next = 4;\n                return this.uploadPendingImages();\n\n              case 4:\n                _context2.next = 11;\n                break;\n\n              case 6:\n                _context2.prev = 6;\n                _context2.t0 = _context2[\"catch\"](1);\n                console.error('🎯 AigcAgentForm: 头像上传失败:', _context2.t0);\n                this.$message.error('头像上传失败: ' + (_context2.t0.message || '未知错误'));\n                return _context2.abrupt(\"return\");\n\n              case 11:\n                /** 触发表单验证 */\n                this.getAllTable().then(function (tables) {\n                  // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n                  var originalAvatar = _this3.model.agentAvatar;\n\n                  if (!_this3.model.agentAvatar && _this3.$refs.avatarUpload && _this3.$refs.avatarUpload.hasPendingFiles()) {\n                    console.log('🎯 AigcAgentForm: 检测到待上传头像，临时设置头像值以通过验证');\n                    _this3.model.agentAvatar = 'pending_upload'; // 临时值\n                  }\n                  /** 一次性验证主表和所有的次表 */\n\n\n                  return validateFormModelAndTables(_this3.$refs.form, _this3.model, tables).then(function (result) {\n                    // 恢复原始值\n                    _this3.model.agentAvatar = originalAvatar;\n                    return result;\n                  }).catch(function (error) {\n                    // 恢复原始值\n                    _this3.model.agentAvatar = originalAvatar;\n                    throw error;\n                  });\n                }).then(function (allValues) {\n                  /** 一次性验证一对一的所有子表 */\n                  return _this3.validateSubForm(allValues);\n                }).then( /*#__PURE__*/function () {\n                  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(allValues) {\n                    var formData, result;\n                    return _regeneratorRuntime.wrap(function _callee$(_context) {\n                      while (1) {\n                        switch (_context.prev = _context.next) {\n                          case 0:\n                            _context.prev = 0;\n                            // 整理表单数据\n                            formData = _this3.classifyIntoFormData(allValues); // 发起保存请求\n\n                            _context.next = 4;\n                            return _this3.request(formData);\n\n                          case 4:\n                            result = _context.sent;\n                            _context.next = 7;\n                            return _this3.confirmDeleteOriginalFiles();\n\n                          case 7:\n                            return _context.abrupt(\"return\", result);\n\n                          case 10:\n                            _context.prev = 10;\n                            _context.t0 = _context[\"catch\"](0);\n                            console.error('🎯 AigcAgentForm: 保存失败:', _context.t0);\n                            throw _context.t0;\n\n                          case 14:\n                          case \"end\":\n                            return _context.stop();\n                        }\n                      }\n                    }, _callee, null, [[0, 10]]);\n                  }));\n\n                  return function (_x) {\n                    return _ref.apply(this, arguments);\n                  };\n                }()).catch(function (e) {\n                  if (e.error === VALIDATE_NO_PASSED) {\n                    // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n                    _this3.activeKey = e.index == null ? _this3.activeKey : _this3.refKeys[e.index];\n                  } else {\n                    console.error(e);\n                  }\n                });\n\n              case 12:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[1, 6]]);\n      }));\n\n      function handleOk() {\n        return _handleOk.apply(this, arguments);\n      }\n\n      return handleOk;\n    }(),\n    // 🔥 上传待上传的头像\n    uploadPendingImages: function () {\n      var _uploadPendingImages = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var uploadedFiles, finalValue, currentValue;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                console.log('🎯 AigcAgentForm: 开始检查待上传头像...');\n\n                if (!this.$refs.avatarUpload) {\n                  _context3.next = 23;\n                  break;\n                }\n\n                if (!this.$refs.avatarUpload.hasPendingFiles()) {\n                  _context3.next = 20;\n                  break;\n                }\n\n                console.log('🎯 AigcAgentForm: 发现待上传的头像，开始上传...');\n                _context3.prev = 4;\n                _context3.next = 7;\n                return this.$refs.avatarUpload.performUpload();\n\n              case 7:\n                uploadedFiles = _context3.sent;\n                console.log('🎯 AigcAgentForm: 头像上传结果:', uploadedFiles); // 上传完成后，获取最终的值\n\n                finalValue = this.$refs.avatarUpload.getCurrentValue();\n                this.model.agentAvatar = finalValue;\n                console.log('🎯 AigcAgentForm: 头像最终值:', this.model.agentAvatar);\n                _context3.next = 18;\n                break;\n\n              case 14:\n                _context3.prev = 14;\n                _context3.t0 = _context3[\"catch\"](4);\n                console.error('🎯 AigcAgentForm: 头像上传失败:', _context3.t0);\n                throw _context3.t0;\n\n              case 18:\n                _context3.next = 23;\n                break;\n\n              case 20:\n                // 没有待上传文件，检查是否有删除操作\n                currentValue = this.$refs.avatarUpload.getCurrentValue();\n                this.model.agentAvatar = currentValue;\n                console.log('🎯 AigcAgentForm: 头像无新上传，当前值:', this.model.agentAvatar);\n\n              case 23:\n                console.log('🎯 AigcAgentForm: 头像处理完成');\n\n              case 24:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[4, 14]]);\n      }));\n\n      function uploadPendingImages() {\n        return _uploadPendingImages.apply(this, arguments);\n      }\n\n      return uploadPendingImages;\n    }(),\n    // 🔥 确认删除被替换的原始头像文件\n    confirmDeleteOriginalFiles: function () {\n      var _confirmDeleteOriginalFiles = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!this.$refs.avatarUpload) {\n                  _context4.next = 10;\n                  break;\n                }\n\n                _context4.prev = 1;\n                _context4.next = 4;\n                return this.$refs.avatarUpload.confirmDeleteOriginalFiles();\n\n              case 4:\n                console.log('🎯 AigcAgentForm: 原始头像文件清理完成');\n                _context4.next = 10;\n                break;\n\n              case 7:\n                _context4.prev = 7;\n                _context4.t0 = _context4[\"catch\"](1);\n                console.warn('🎯 AigcAgentForm: 原始头像文件清理失败:', _context4.t0); // 删除失败不影响主流程\n\n              case 10:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[1, 7]]);\n      }));\n\n      function confirmDeleteOriginalFiles() {\n        return _confirmDeleteOriginalFiles.apply(this, arguments);\n      }\n\n      return confirmDeleteOriginalFiles;\n    }(),\n    // 🔥 关闭表单时回滚头像变更\n    handleClose: function handleClose() {\n      console.log('🎯 AigcAgentForm: 表单关闭，回滚头像变更');\n\n      if (this.$refs.avatarUpload) {\n        this.$refs.avatarUpload.rollbackChanges();\n      } // 发出关闭事件\n\n\n      this.$emit('close');\n    }\n  }\n};", {"version": 3, "sources": ["AigcAgentForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,SAAA,SAAA,EAAA,UAAA,QAAA,cAAA;AACA,SAAA,SAAA,EAAA,aAAA,EAAA,kBAAA,EAAA,0BAAA,QAAA,4BAAA;AACA,SAAA,wBAAA,QAAA,mCAAA;AACA,SAAA,sBAAA,QAAA,cAAA;AACA,OAAA,oBAAA,MAAA,yCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,MAAA,EAAA,CAAA,wBAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,oBAAA,EAAA;AADA,GAHA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA;AAEA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA;AAFA,OADA;AAKA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA;AAEA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA;AAFA,OALA;AASA,MAAA,SAAA,EAAA;AACA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA;AAEA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA;AAFA,OATA;AAaA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA;AAEA,QAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA;AAFA,OAbA;AAiBA,MAAA,KAAA,EAAA;AACA,QAAA,UAAA,EAAA,GADA;AACA;AACA,QAAA,WAAA,EAAA,GAFA,CAEA;;AAFA,OAjBA;AAqBA;AACA,MAAA,gBAAA,EAAA,CAtBA;AAuBA,MAAA,cAAA,EAAA;AACA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,gBAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAnBA,OAvBA;AA8CA,MAAA,OAAA,EAAA,CAAA,cAAA,CA9CA;AA+CA,MAAA,SAAA,EAAA,CAAA,cAAA,CA/CA;AAgDA,MAAA,SAAA,EAAA,cAhDA;AAiDA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,UAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,SAAA,CAAA,MAHA;AAIA,UAAA,YAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,YAFA;AAGA,UAAA,IAAA,EAAA,SAAA,CAAA,KAHA;AAIA,UAAA,KAAA,EAAA,OAJA;AAKA,UAAA,WAAA,EAAA,aALA;AAMA,UAAA,YAAA,EAAA;AANA,SAPA,EAeA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,IAAA,EAAA,SAAA,CAAA,KAHA;AAIA,UAAA,KAAA,EAAA,OAJA;AAKA,UAAA,WAAA,EAAA,aALA;AAMA,UAAA,YAAA,EAAA;AANA,SAfA,EAuBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,qBAFA;AAGA,UAAA,IAAA,EAAA,SAAA,CAAA,KAHA;AAIA,UAAA,KAAA,EAAA,OAJA;AAKA,UAAA,WAAA,EAAA,aALA;AAMA,UAAA,YAAA,EAAA;AANA,SAvBA,EA+BA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,iBAFA;AAGA,UAAA,IAAA,EAAA,SAAA,CAAA,IAHA;AAIA,UAAA,KAAA,EAAA,IAJA;AAKA,UAAA,YAAA,EAAA,SALA;AAMA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,WAAA,EAAA,OAPA;AAQA,UAAA,YAAA,EAAA;AARA,SA/BA;AAHA,OAlDA;AAgGA,MAAA,GAAA,EAAA;AACA,QAAA,GAAA,EAAA,2BADA;AAEA,QAAA,IAAA,EAAA,4BAFA;AAGA,QAAA,SAAA,EAAA,iCAHA;AAIA,QAAA,YAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA;AAJA;AAhGA,KAAA;AAyGA,GAhHA;AAiHA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAGA,MAAA,QAAA,EAAA;AAHA;AAFA,GAjHA;AAyHA,EAAA,QAAA,EAAA;AACA,IAAA,YADA,0BACA;AACA,aAAA,KAAA,QAAA;AACA;AAHA,GAzHA;AA8HA,EAAA,OA9HA,qBA8HA,CACA,CA/HA;AAgIA,EAAA,OAAA,EAAA;AACA,IAAA,SADA,uBACA;AACA,WAAA,iBAAA,CAAA,UAAA,GAAA,EAAA;AACA,KAHA;AAIA,IAAA,WAJA,yBAIA;AAAA;;AACA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,UAAA,GAAA;AAAA,eAAA,aAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,aAAA,OAAA,CAAA,GAAA,CAAA,MAAA,CAAA;AACA,KAPA;;AAQA;AACA,IAAA,SATA,uBASA;AACA,WAAA,SAAA,CAAA,YAAA,CACA,CADA,EADA,CAGA;;AACA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA;AACA,YAAA,MAAA,GAAA;AAAA,UAAA,EAAA,EAAA,KAAA,KAAA,CAAA;AAAA,SAAA;AACA,aAAA,mBAAA,CAAA,KAAA,GAAA,CAAA,YAAA,CAAA,IAAA,EAAA,MAAA,EAAA,KAAA,iBAAA;AACA;AACA,KAjBA;AAkBA;AACA,IAAA,eAnBA,2BAmBA,SAnBA,EAmBA;AAAA;;AACA,aAAA,IAAA,OAAA,CAAA,UAAA,OAAA,EAAA,MAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,EAAA,EACA,IADA,CACA,YAAA;AACA,UAAA,OAAA,CAAA,SAAA,CAAA;AACA,SAHA,EAGA,KAHA,CAGA,UAAA,CAAA,EAAA;AACA,cAAA,CAAA,CAAA,KAAA,KAAA,kBAAA,EAAA;AACA;AACA,YAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA,KAAA,IAAA,IAAA,GAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,KAAA,CAAA;AACA,WAHA,MAGA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA;AACA,SAVA;AAWA,OAZA,CAAA;AAaA,KAjCA;;AAkCA;AACA,IAAA,oBAnCA,gCAmCA,SAnCA,EAmCA;AACA,UAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,KAAA,EAAA,SAAA,CAAA,SAAA,CAAA;AACA,6CACA,IADA;AACA;AACA,QAAA,gBAAA,EAAA,SAAA,CAAA,WAAA,CAAA,CAAA,EAAA;AAFA;AAIA,KAzCA;AA0CA,IAAA,aA1CA,yBA0CA,GA1CA,EA0CA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,GAAA;AACA,KA5CA;AA8CA;AACA,IAAA,iBA/CA,6BA+CA,IA/CA,EA+CA;AACA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,QAAA,MAAA,CAAA;;AACA,UAAA,CAAA,OAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AACA,eAAA,KAAA;AACA,OANA,CAQA;;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,GAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,iBAAA;AACA,eAAA,KAAA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,IAAA,CAAA,IAAA,EAAA,KAAA,EAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,GAAA,IAAA,EAAA,OAAA,CAAA,CAAA,IAAA,IAAA;AACA,aAAA,IAAA;AACA,KAhEA;AAkEA;AACA,IAAA,QAnEA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAoEA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA;AAEA;;AAtEA;AAAA;AAAA,uBAyEA,KAAA,mBAAA,EAzEA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA2EA,gBAAA,OAAA,CAAA,KAAA,CAAA,2BAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,cAAA,aAAA,OAAA,IAAA,MAAA,CAAA;AA5EA;;AAAA;AAgFA;AACA,qBAAA,WAAA,GAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA;AACA,sBAAA,cAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,sBAAA,CAAA,MAAA,CAAA,KAAA,CAAA,WAAA,IAAA,MAAA,CAAA,KAAA,CAAA,YAAA,IAAA,MAAA,CAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EAAA,EAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,yCAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,gBAAA,CAFA,CAEA;AACA;AAEA;;;AACA,yBAAA,0BAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,cAAA;AACA,2BAAA,MAAA;AACA,mBAJA,EAIA,KAJA,CAIA,UAAA,KAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,WAAA,GAAA,cAAA;AACA,0BAAA,KAAA;AACA,mBARA,CAAA;AASA,iBAlBA,EAkBA,IAlBA,CAkBA,UAAA,SAAA,EAAA;AACA;AACA,yBAAA,MAAA,CAAA,eAAA,CAAA,SAAA,CAAA;AACA,iBArBA,EAqBA,IArBA;AAAA,sFAqBA,iBAAA,SAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA,4BAAA,QAHA,GAGA,MAAA,CAAA,oBAAA,CAAA,SAAA,CAHA,EAKA;;AALA;AAAA,mCAMA,MAAA,CAAA,OAAA,CAAA,QAAA,CANA;;AAAA;AAMA,4BAAA,MANA;AAAA;AAAA,mCASA,MAAA,CAAA,0BAAA,EATA;;AAAA;AAAA,6DAWA,MAXA;;AAAA;AAAA;AAAA;AAaA,4BAAA,OAAA,CAAA,KAAA,CAAA,yBAAA;AAbA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBArBA;;AAAA;AAAA;AAAA;AAAA,qBAqCA,KArCA,CAqCA,UAAA,CAAA,EAAA;AACA,sBAAA,CAAA,CAAA,KAAA,KAAA,kBAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA,KAAA,IAAA,IAAA,GAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,KAAA,CAAA;AACA,mBAHA,MAGA;AACA,oBAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA;AACA,iBA5CA;;AAjFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAgIA;AACA,IAAA,mBAjIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkIA,gBAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;;AAlIA,qBAoIA,KAAA,KAAA,CAAA,YApIA;AAAA;AAAA;AAAA;;AAAA,qBAqIA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EArIA;AAAA;AAAA;AAAA;;AAsIA,gBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA;AAtIA;AAAA;AAAA,uBAwIA,KAAA,KAAA,CAAA,YAAA,CAAA,aAAA,EAxIA;;AAAA;AAwIA,gBAAA,aAxIA;AAyIA,gBAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,aAAA,EAzIA,CA2IA;;AACA,gBAAA,UA5IA,GA4IA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EA5IA;AA6IA,qBAAA,KAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,KAAA,KAAA,CAAA,WAAA;AA9IA;AAAA;;AAAA;AAAA;AAAA;AAgJA,gBAAA,OAAA,CAAA,KAAA,CAAA,2BAAA;AAhJA;;AAAA;AAAA;AAAA;;AAAA;AAoJA;AACA,gBAAA,YArJA,GAqJA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EArJA;AAsJA,qBAAA,KAAA,CAAA,WAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,KAAA,CAAA,WAAA;;AAvJA;AA2JA,gBAAA,OAAA,CAAA,GAAA,CAAA,0BAAA;;AA3JA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8JA;AACA,IAAA,0BA/JA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAgKA,KAAA,KAAA,CAAA,YAhKA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAkKA,KAAA,KAAA,CAAA,YAAA,CAAA,0BAAA,EAlKA;;AAAA;AAmKA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA;AAnKA;AAAA;;AAAA;AAAA;AAAA;AAqKA,gBAAA,OAAA,CAAA,IAAA,CAAA,+BAAA,gBArKA,CAsKA;;AAtKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2KA;AACA,IAAA,WA5KA,yBA4KA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;;AAEA,UAAA,KAAA,KAAA,CAAA,YAAA,EAAA;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,eAAA;AACA,OALA,CAOA;;;AACA,WAAA,KAAA,CAAA,OAAA;AACA;AArLA;AAhIA,CAAA", "sourcesContent": ["<template>\n  <a-spin :spinning=\"confirmLoading\">\n    <j-form-container :disabled=\"formDisabled\">\n      <!-- 主表单区域 -->\n      <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\n        <a-row>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"作者类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"authorType\">\n              <j-dict-select-tag type=\"list\" v-model=\"model.authorType\" dictCode=\"author_type\" placeholder=\"请选择作者类型\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体ID\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentId\">\n              <a-input v-model=\"model.agentId\" placeholder=\"请输入智能体ID\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体名称\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentName\">\n              <a-input v-model=\"model.agentName\" placeholder=\"请输入智能体名称\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"24\">\n            <a-form-model-item label=\"智能体描述\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"agentDescription\">\n              <a-textarea v-model=\"model.agentDescription\" rows=\"4\" placeholder=\"请输入智能体描述\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体头像\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentAvatar\">\n              <j-image-upload-deferred\n                ref=\"avatarUpload\"\n                v-model=\"model.agentAvatar\"\n                :isMultiple=\"false\"\n                bizPath=\"agent-avatar\"\n                text=\"上传头像\">\n              </j-image-upload-deferred>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"展示视频\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"demoVideo\">\n              <j-upload v-model=\"model.demoVideo\" :beforeUpload=\"beforeVideoUpload\" text=\"上传视频(最大100MB)\"></j-upload>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"体验链接\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"experienceLink\">\n              <a-input v-model=\"model.experienceLink\" placeholder=\"请输入体验链接\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"价格（元）\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"price\">\n              <a-input-number v-model=\"model.price\" placeholder=\"请输入价格（元）\" style=\"width: 100%\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"审核状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"auditStatus\">\n              <j-dict-select-tag type=\"list\" v-model=\"model.auditStatus\" dictCode=\"audit_status\" placeholder=\"请选择审核状态\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"24\">\n            <a-form-model-item label=\"审核备注\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"auditRemark\">\n              <a-textarea v-model=\"model.auditRemark\" rows=\"4\" placeholder=\"请输入审核备注\" />\n            </a-form-model-item>\n          </a-col>\n        </a-row>\n      </a-form-model>\n    </j-form-container>\n      <!-- 子表单区域 -->\n    <a-tabs v-model=\"activeKey\" @change=\"handleChangeTabs\">\n      <a-tab-pane tab=\"工作流表\" :key=\"refKeys[0]\" :forceRender=\"true\">\n        <j-editable-table\n          :ref=\"refKeys[0]\"\n          :loading=\"aigcWorkflowTable.loading\"\n          :columns=\"aigcWorkflowTable.columns\"\n          :dataSource=\"aigcWorkflowTable.dataSource\"\n          :maxHeight=\"300\"\n          :disabled=\"formDisabled\"\n          :rowNumber=\"true\"\n          :rowSelection=\"true\"\n          :actionButton=\"true\"/>\n      </a-tab-pane>\n    </a-tabs>\n  </a-spin>\n</template>\n\n<script>\n\n  import { getAction, httpAction } from '@/api/manage'\n  import { FormTypes,getRefPromise,VALIDATE_NO_PASSED,validateFormModelAndTables } from '@/utils/JEditableTableUtil'\n  import { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin'\n  import { validateDuplicateValue } from '@/utils/util'\n  import JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'\n\n  export default {\n    name: 'AigcAgentForm',\n    mixins: [JEditableTableModelMixin],\n    components: {\n      JImageUploadDeferred\n    },\n    data() {\n      return {\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 6 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n        labelCol2: {\n          xs: { span: 24 },\n          sm: { span: 3 },\n        },\n        wrapperCol2: {\n          xs: { span: 24 },\n          sm: { span: 20 },\n        },\n        model:{\n          authorType: '1', // 默认作者类型：官方\n          auditStatus: '2' // 默认审核状态：审核通过\n        },\n        // 新增时子表默认添加几行空数据\n        addDefaultRowNum: 1,\n        validatorRules: {\n           authorType: [\n              { required: true, message: '请输入作者类型!'},\n           ],\n           agentId: [\n              { required: true, message: '请输入智能体ID!'},\n           ],\n           agentName: [\n              { required: true, message: '请输入智能体名称!'},\n           ],\n           agentDescription: [\n              { required: true, message: '请输入智能体描述!'},\n           ],\n           agentAvatar: [\n              { required: true, message: '请输入智能体头像!'},\n           ],\n           price: [\n              { required: true, message: '请输入价格（元）!'},\n           ],\n           auditStatus: [\n              { required: true, message: '请输入审核状态!'},\n           ],\n        },\n        refKeys: ['aigcWorkflow', ],\n        tableKeys:['aigcWorkflow', ],\n        activeKey: 'aigcWorkflow',\n        // 工作流表\n        aigcWorkflowTable: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '智能体ID',\n              key: 'agentId',\n              type: FormTypes.hidden,\n              defaultValue: ''\n            },\n            {\n              title: '工作流ID',\n              key: 'workflowId',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流名称',\n              key: 'workflowName',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流描述',\n              key: 'workflowDescription',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流压缩包',\n              key: 'workflowPackage',\n              type: FormTypes.file,\n              token:true,\n              responseName:\"message\",\n              width:\"200px\",\n              placeholder: '请选择文件',\n              defaultValue:'',\n            },\n          ]\n        },\n        url: {\n          add: \"/aigc_agent/aigcAgent/add\",\n          edit: \"/aigc_agent/aigcAgent/edit\",\n          queryById: \"/aigc_agent/aigcAgent/queryById\",\n          aigcWorkflow: {\n            list: '/aigc_agent/aigcAgent/queryAigcWorkflowByMainId'\n          },\n        }\n      }\n    },\n    props: {\n      //表单禁用\n      disabled: {\n        type: Boolean,\n        default: false,\n        required: false\n      }\n    },\n    computed: {\n      formDisabled(){\n        return this.disabled\n      },\n    },\n    created () {\n    },\n    methods: {\n      addBefore(){\n        this.aigcWorkflowTable.dataSource=[]\n      },\n      getAllTable() {\n        let values = this.tableKeys.map(key => getRefPromise(this, key))\n        return Promise.all(values)\n      },\n      /** 调用完edit()方法之后会自动调用此方法 */\n      editAfter() {\n        this.$nextTick(() => {\n        })\n        // 加载子表数据\n        if (this.model.id) {\n          let params = { id: this.model.id }\n          this.requestSubTableData(this.url.aigcWorkflow.list, params, this.aigcWorkflowTable)\n        }\n      },\n      //校验所有一对一子表表单\n      validateSubForm(allValues){\n          return new Promise((resolve,reject)=>{\n            Promise.all([\n            ]).then(() => {\n              resolve(allValues)\n            }).catch(e => {\n              if (e.error === VALIDATE_NO_PASSED) {\n                // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n                this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n              } else {\n                console.error(e)\n              }\n            })\n          })\n      },\n      /** 整理成formData */\n      classifyIntoFormData(allValues) {\n        let main = Object.assign(this.model, allValues.formValue)\n        return {\n          ...main, // 展开\n          aigcWorkflowList: allValues.tablesValue[0].values,\n        }\n      },\n      validateError(msg){\n        this.$message.error(msg)\n      },\n\n      // 视频上传前验证\n      beforeVideoUpload(file) {\n        // 检查文件类型\n        const isVideo = file.type.indexOf('video/') === 0\n        if (!isVideo) {\n          this.$message.error('只能上传视频文件!')\n          return false\n        }\n\n        // 检查文件大小（100MB）\n        const isLt100M = file.size / 1024 / 1024 < 100\n        if (!isLt100M) {\n          this.$message.error('视频大小不能超过 100MB!')\n          return false\n        }\n\n        console.log('🎯 视频上传验证通过:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB')\n        return true\n      },\n\n      // 🔥 重写handleOk方法，集成延迟上传逻辑\n      async handleOk() {\n        console.log('🎯 AigcAgentForm: 开始保存智能体...')\n\n        /** 先处理头像上传，更新model值 */\n        try {\n          // 🔥 先上传待上传的头像，更新model.agentAvatar\n          await this.uploadPendingImages()\n        } catch (error) {\n          console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n          this.$message.error('头像上传失败: ' + (error.message || '未知错误'))\n          return\n        }\n\n        /** 触发表单验证 */\n        this.getAllTable().then(tables => {\n          // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n          const originalAvatar = this.model.agentAvatar\n          if (!this.model.agentAvatar && this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {\n            console.log('🎯 AigcAgentForm: 检测到待上传头像，临时设置头像值以通过验证')\n            this.model.agentAvatar = 'pending_upload' // 临时值\n          }\n\n          /** 一次性验证主表和所有的次表 */\n          return validateFormModelAndTables(this.$refs.form, this.model, tables).then(result => {\n            // 恢复原始值\n            this.model.agentAvatar = originalAvatar\n            return result\n          }).catch(error => {\n            // 恢复原始值\n            this.model.agentAvatar = originalAvatar\n            throw error\n          })\n        }).then(allValues => {\n          /** 一次性验证一对一的所有子表 */\n          return this.validateSubForm(allValues)\n        }).then(async (allValues) => {\n          try {\n            // 整理表单数据\n            let formData = this.classifyIntoFormData(allValues)\n\n            // 发起保存请求\n            const result = await this.request(formData)\n\n            // 🔥 保存成功后，确认删除被替换的原始头像文件\n            await this.confirmDeleteOriginalFiles()\n\n            return result\n          } catch (error) {\n            console.error('🎯 AigcAgentForm: 保存失败:', error)\n            throw error\n          }\n        }).catch(e => {\n          if (e.error === VALIDATE_NO_PASSED) {\n            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n            this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n          } else {\n            console.error(e)\n          }\n        })\n      },\n\n      // 🔥 上传待上传的头像\n      async uploadPendingImages() {\n        console.log('🎯 AigcAgentForm: 开始检查待上传头像...')\n\n        if (this.$refs.avatarUpload) {\n          if (this.$refs.avatarUpload.hasPendingFiles()) {\n            console.log('🎯 AigcAgentForm: 发现待上传的头像，开始上传...')\n            try {\n              const uploadedFiles = await this.$refs.avatarUpload.performUpload()\n              console.log('🎯 AigcAgentForm: 头像上传结果:', uploadedFiles)\n\n              // 上传完成后，获取最终的值\n              const finalValue = this.$refs.avatarUpload.getCurrentValue()\n              this.model.agentAvatar = finalValue\n              console.log('🎯 AigcAgentForm: 头像最终值:', this.model.agentAvatar)\n            } catch (error) {\n              console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n              throw error\n            }\n          } else {\n            // 没有待上传文件，检查是否有删除操作\n            const currentValue = this.$refs.avatarUpload.getCurrentValue()\n            this.model.agentAvatar = currentValue\n            console.log('🎯 AigcAgentForm: 头像无新上传，当前值:', this.model.agentAvatar)\n          }\n        }\n\n        console.log('🎯 AigcAgentForm: 头像处理完成')\n      },\n\n      // 🔥 确认删除被替换的原始头像文件\n      async confirmDeleteOriginalFiles() {\n        if (this.$refs.avatarUpload) {\n          try {\n            await this.$refs.avatarUpload.confirmDeleteOriginalFiles()\n            console.log('🎯 AigcAgentForm: 原始头像文件清理完成')\n          } catch (error) {\n            console.warn('🎯 AigcAgentForm: 原始头像文件清理失败:', error)\n            // 删除失败不影响主流程\n          }\n        }\n      },\n\n      // 🔥 关闭表单时回滚头像变更\n      handleClose() {\n        console.log('🎯 AigcAgentForm: 表单关闭，回滚头像变更')\n\n        if (this.$refs.avatarUpload) {\n          this.$refs.avatarUpload.rollbackChanges()\n        }\n\n        // 发出关闭事件\n        this.$emit('close')\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n</style>"], "sourceRoot": "src/views/aigcview/agent/modules"}]}