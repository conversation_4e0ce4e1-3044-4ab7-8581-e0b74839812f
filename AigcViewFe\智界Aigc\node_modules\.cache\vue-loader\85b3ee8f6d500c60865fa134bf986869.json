{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue?vue&type=template&id=409c6ff1&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue", "mtime": 1753973678761}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\"div\", { staticClass: \"market-container\" }, [\n      _c(\"div\", { staticClass: \"simple-header\" }, [\n        _c(\"h1\", { staticClass: \"simple-title\" }, [_vm._v(\"AI插件中心\")]),\n        _c(\"p\", { staticClass: \"simple-subtitle\" }, [\n          _vm._v(\"发现优质AI插件，提升创作效率，让每个想法都能完美实现\")\n        ])\n      ]),\n      _c(\"section\", { staticClass: \"search-section\" }, [\n        _c(\"div\", { staticClass: \"container\" }, [\n          _c(\"div\", { staticClass: \"search-layout\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"search-input-group\" },\n              [\n                _c(\"div\", { staticClass: \"custom-search-input\" }, [\n                  _c(\n                    \"div\",\n                    { staticClass: \"search-icon\" },\n                    [_c(\"a-icon\", { attrs: { type: \"search\" } })],\n                    1\n                  ),\n                  _c(\"input\", {\n                    directives: [\n                      {\n                        name: \"model\",\n                        rawName: \"v-model\",\n                        value: _vm.searchKeyword,\n                        expression: \"searchKeyword\"\n                      }\n                    ],\n                    staticClass: \"search-input-native\",\n                    attrs: { placeholder: \"搜索插件名称、描述...\" },\n                    domProps: { value: _vm.searchKeyword },\n                    on: {\n                      input: [\n                        function($event) {\n                          if ($event.target.composing) {\n                            return\n                          }\n                          _vm.searchKeyword = $event.target.value\n                        },\n                        _vm.handleSearchInput\n                      ],\n                      keyup: function($event) {\n                        if (\n                          !$event.type.indexOf(\"key\") &&\n                          _vm._k(\n                            $event.keyCode,\n                            \"enter\",\n                            13,\n                            $event.key,\n                            \"Enter\"\n                          )\n                        ) {\n                          return null\n                        }\n                        return _vm.handleSearch($event)\n                      }\n                    }\n                  }),\n                  _vm.searchKeyword\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass: \"clear-search-icon\",\n                          attrs: { title: \"清空搜索\" },\n                          on: { click: _vm.clearSearchInput }\n                        },\n                        [_c(\"a-icon\", { attrs: { type: \"close-circle\" } })],\n                        1\n                      )\n                    : _vm._e()\n                ]),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"clear-filters-btn\",\n                    attrs: { size: \"large\", disabled: !_vm.hasActiveFilters },\n                    on: { click: _vm.clearAllFilters }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"clear\" } }),\n                    _vm._v(\"\\n              清空所有筛选\\n            \")\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ])\n        ])\n      ]),\n      _c(\"section\", { staticClass: \"main-content\" }, [\n        _c(\"div\", { staticClass: \"container\" }, [\n          _c(\"div\", { staticClass: \"content-layout\" }, [\n            _c(\"aside\", { staticClass: \"sidebar\" }, [\n              _c(\"div\", { staticClass: \"filter-panel\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"filter-section\" },\n                  [\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"filter-header\",\n                        on: {\n                          click: function($event) {\n                            return _vm.toggleSection(\"category\")\n                          }\n                        }\n                      },\n                      [\n                        _c(\n                          \"h3\",\n                          { staticClass: \"filter-title\" },\n                          [\n                            _c(\"a-icon\", {\n                              staticClass: \"filter-icon\",\n                              attrs: { type: \"appstore\" }\n                            }),\n                            _vm._v(\n                              \"\\n                    插件分类\\n                    \"\n                            ),\n                            _vm.currentFilters.category\n                              ? _c(\"span\", { staticClass: \"filter-badge\" }, [\n                                  _vm._v(\"1\")\n                                ])\n                              : _vm._e()\n                          ],\n                          1\n                        ),\n                        _c(\"a-icon\", {\n                          staticClass: \"collapse-icon\",\n                          attrs: {\n                            type: _vm.collapsedSections.category ? \"down\" : \"up\"\n                          }\n                        })\n                      ],\n                      1\n                    ),\n                    _c(\"a-collapse-transition\", [\n                      _c(\n                        \"div\",\n                        {\n                          directives: [\n                            {\n                              name: \"show\",\n                              rawName: \"v-show\",\n                              value: !_vm.collapsedSections.category,\n                              expression: \"!collapsedSections.category\"\n                            }\n                          ],\n                          staticClass: \"filter-content\"\n                        },\n                        [\n                          _c(\n                            \"div\",\n                            { staticClass: \"category-grid\" },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"category-tag\",\n                                  class: {\n                                    active: _vm.currentFilters.category === \"\"\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.selectCategory(\"\")\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                    _vm._v(\"🌟\")\n                                  ]),\n                                  _c(\"span\", { staticClass: \"tag-text\" }, [\n                                    _vm._v(\"全部\")\n                                  ]),\n                                  _c(\"span\", { staticClass: \"tag-count\" }, [\n                                    _vm._v(_vm._s(_vm.totalPlugins))\n                                  ])\n                                ]\n                              ),\n                              _vm._l(_vm.categories, function(category) {\n                                return _c(\n                                  \"div\",\n                                  {\n                                    key: category.value,\n                                    staticClass: \"category-tag\",\n                                    class: {\n                                      active:\n                                        _vm.currentFilters.category ===\n                                        category.value\n                                    },\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.selectCategory(\n                                          category.value\n                                        )\n                                      }\n                                    }\n                                  },\n                                  [\n                                    _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                      _vm._v(\n                                        _vm._s(\n                                          _vm.getCategoryIcon(category.value)\n                                        )\n                                      )\n                                    ]),\n                                    _c(\"span\", { staticClass: \"tag-text\" }, [\n                                      _vm._v(_vm._s(category.text))\n                                    ]),\n                                    _c(\"span\", { staticClass: \"tag-count\" }, [\n                                      _vm._v(\n                                        _vm._s(\n                                          _vm.categoryCounts[category.value] ||\n                                            0\n                                        )\n                                      )\n                                    ])\n                                  ]\n                                )\n                              })\n                            ],\n                            2\n                          )\n                        ]\n                      )\n                    ])\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"filter-section\" },\n                  [\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"filter-header\",\n                        on: {\n                          click: function($event) {\n                            return _vm.toggleSection(\"price\")\n                          }\n                        }\n                      },\n                      [\n                        _c(\n                          \"h3\",\n                          { staticClass: \"filter-title\" },\n                          [\n                            _c(\"a-icon\", {\n                              staticClass: \"filter-icon\",\n                              attrs: { type: \"dollar\" }\n                            }),\n                            _vm._v(\n                              \"\\n                    价格范围\\n                    \"\n                            ),\n                            _vm.currentFilters.priceRange\n                              ? _c(\"span\", { staticClass: \"filter-badge\" }, [\n                                  _vm._v(\"1\")\n                                ])\n                              : _vm._e()\n                          ],\n                          1\n                        ),\n                        _c(\"a-icon\", {\n                          staticClass: \"collapse-icon\",\n                          attrs: {\n                            type: _vm.collapsedSections.price ? \"down\" : \"up\"\n                          }\n                        })\n                      ],\n                      1\n                    ),\n                    _c(\"a-collapse-transition\", [\n                      _c(\n                        \"div\",\n                        {\n                          directives: [\n                            {\n                              name: \"show\",\n                              rawName: \"v-show\",\n                              value: !_vm.collapsedSections.price,\n                              expression: \"!collapsedSections.price\"\n                            }\n                          ],\n                          staticClass: \"filter-content\"\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"price-grid\" }, [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"price-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.priceRange === \"\" &&\n                                    !_vm.showCustomPrice\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectPriceRange(\"\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"💰\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"全部\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"price-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.priceRange === \"0-1\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectPriceRange(\"0-1\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"🪙\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"¥0-1\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"price-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.priceRange === \"1-5\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectPriceRange(\"1-5\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"💵\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"¥1-5\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"price-tag\",\n                                class: {\n                                  active: _vm.currentFilters.priceRange === \"5+\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectPriceRange(\"5+\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"💎\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"¥5+\")\n                                ])\n                              ]\n                            )\n                          ]),\n                          _c(\"div\", { staticClass: \"custom-price-range\" }, [\n                            _c(\"div\", { staticClass: \"custom-price-header\" }, [\n                              _c(\"span\", { staticClass: \"custom-price-icon\" }, [\n                                _vm._v(\"⚙️\")\n                              ]),\n                              _c(\n                                \"span\",\n                                { staticClass: \"custom-price-label\" },\n                                [_vm._v(\"自定义价格\")]\n                              )\n                            ]),\n                            _c(\n                              \"div\",\n                              { staticClass: \"price-inputs\" },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"price-input-row\" },\n                                  [\n                                    _c(\n                                      \"label\",\n                                      { staticClass: \"input-label\" },\n                                      [_vm._v(\"最低价格\")]\n                                    ),\n                                    _c(\"a-input-number\", {\n                                      staticClass: \"price-input\",\n                                      attrs: {\n                                        min: 0,\n                                        max: 999,\n                                        placeholder: \"请输入最低价格\",\n                                        size: \"default\"\n                                      },\n                                      on: { pressEnter: _vm.applyCustomPrice },\n                                      model: {\n                                        value: _vm.customPriceMin,\n                                        callback: function($$v) {\n                                          _vm.customPriceMin = $$v\n                                        },\n                                        expression: \"customPriceMin\"\n                                      }\n                                    })\n                                  ],\n                                  1\n                                ),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"price-input-row\" },\n                                  [\n                                    _c(\n                                      \"label\",\n                                      { staticClass: \"input-label\" },\n                                      [_vm._v(\"最高价格\")]\n                                    ),\n                                    _c(\"a-input-number\", {\n                                      staticClass: \"price-input\",\n                                      attrs: {\n                                        min: _vm.customPriceMin || 0,\n                                        max: 999,\n                                        placeholder: \"请输入最高价格\",\n                                        size: \"default\"\n                                      },\n                                      on: { pressEnter: _vm.applyCustomPrice },\n                                      model: {\n                                        value: _vm.customPriceMax,\n                                        callback: function($$v) {\n                                          _vm.customPriceMax = $$v\n                                        },\n                                        expression: \"customPriceMax\"\n                                      }\n                                    })\n                                  ],\n                                  1\n                                ),\n                                _c(\n                                  \"a-button\",\n                                  {\n                                    staticClass: \"apply-custom-btn\",\n                                    attrs: {\n                                      type: \"primary\",\n                                      disabled:\n                                        !_vm.customPriceMin &&\n                                        !_vm.customPriceMax,\n                                      block: \"\"\n                                    },\n                                    on: { click: _vm.applyCustomPrice }\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n                          确定筛选\\n                        \"\n                                    )\n                                  ]\n                                )\n                              ],\n                              1\n                            )\n                          ])\n                        ]\n                      )\n                    ])\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"filter-section\" },\n                  [\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"filter-header\",\n                        on: {\n                          click: function($event) {\n                            return _vm.toggleSection(\"sort\")\n                          }\n                        }\n                      },\n                      [\n                        _c(\n                          \"h3\",\n                          { staticClass: \"filter-title\" },\n                          [\n                            _c(\"a-icon\", {\n                              staticClass: \"filter-icon\",\n                              attrs: { type: \"sort-ascending\" }\n                            }),\n                            _vm._v(\n                              \"\\n                    排序方式\\n                    \"\n                            ),\n                            _vm.currentFilters.sortType !== \"default\"\n                              ? _c(\"span\", { staticClass: \"filter-badge\" }, [\n                                  _vm._v(\"1\")\n                                ])\n                              : _vm._e()\n                          ],\n                          1\n                        ),\n                        _c(\"a-icon\", {\n                          staticClass: \"collapse-icon\",\n                          attrs: {\n                            type: _vm.collapsedSections.sort ? \"down\" : \"up\"\n                          }\n                        })\n                      ],\n                      1\n                    ),\n                    _c(\"a-collapse-transition\", [\n                      _c(\n                        \"div\",\n                        {\n                          directives: [\n                            {\n                              name: \"show\",\n                              rawName: \"v-show\",\n                              value: !_vm.collapsedSections.sort,\n                              expression: \"!collapsedSections.sort\"\n                            }\n                          ],\n                          staticClass: \"filter-content\"\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"sort-grid\" }, [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"sort-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.sortType === \"default\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectSort(\"default\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"🌟\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"默认\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"sort-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.sortType === \"newest\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectSort(\"newest\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"⏰\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"最新\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"sort-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.sortType === \"price-asc\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectSort(\"price-asc\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"📈\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"价格↑\")\n                                ])\n                              ]\n                            ),\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"sort-tag\",\n                                class: {\n                                  active:\n                                    _vm.currentFilters.sortType === \"price-desc\"\n                                },\n                                on: {\n                                  click: function($event) {\n                                    return _vm.selectSort(\"price-desc\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"span\", { staticClass: \"tag-icon\" }, [\n                                  _vm._v(\"📉\")\n                                ]),\n                                _c(\"span\", { staticClass: \"tag-text\" }, [\n                                  _vm._v(\"价格↓\")\n                                ])\n                              ]\n                            )\n                          ])\n                        ]\n                      )\n                    ])\n                  ],\n                  1\n                )\n              ])\n            ]),\n            _c(\"main\", { staticClass: \"main-area\" }, [\n              _c(\"div\", { staticClass: \"results-header\" }, [\n                _c(\"div\", { staticClass: \"header-left\" }, [\n                  _c(\"h2\", { staticClass: \"results-title\" }, [\n                    _vm.currentFilters.category\n                      ? _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              _vm.getCategoryText(_vm.currentFilters.category)\n                            ) + \"插件\"\n                          )\n                        ])\n                      : _vm.currentFilters.keyword\n                      ? _c(\"span\", [_vm._v(\"搜索结果\")])\n                      : _c(\"span\", [_vm._v(\"全部插件\")])\n                  ]),\n                  _vm.currentFilters.keyword ||\n                  _vm.currentFilters.priceRange ||\n                  _vm.currentFilters.sortType !== \"default\"\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"active-filters\" },\n                        [\n                          _vm.currentFilters.keyword\n                            ? _c(\n                                \"a-tag\",\n                                {\n                                  staticClass: \"filter-tag\",\n                                  attrs: { closable: \"\", color: \"green\" },\n                                  on: { close: _vm.clearKeywordFilter }\n                                },\n                                [\n                                  _vm._v(\n                                    '\\n                    \"' +\n                                      _vm._s(_vm.currentFilters.keyword) +\n                                      '\"\\n                  '\n                                  )\n                                ]\n                              )\n                            : _vm._e(),\n                          _vm.currentFilters.priceRange\n                            ? _c(\n                                \"a-tag\",\n                                {\n                                  staticClass: \"filter-tag\",\n                                  attrs: { closable: \"\", color: \"orange\" },\n                                  on: { close: _vm.clearPriceFilter }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                    \" +\n                                      _vm._s(\n                                        _vm.getPriceRangeText(\n                                          _vm.currentFilters.priceRange\n                                        )\n                                      ) +\n                                      \"\\n                  \"\n                                  )\n                                ]\n                              )\n                            : _vm._e(),\n                          _vm.currentFilters.sortType !== \"default\"\n                            ? _c(\n                                \"a-tag\",\n                                {\n                                  staticClass: \"filter-tag\",\n                                  attrs: { closable: \"\", color: \"purple\" },\n                                  on: { close: _vm.clearSortFilter }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                    \" +\n                                      _vm._s(\n                                        _vm.getSortTypeText(\n                                          _vm.currentFilters.sortType\n                                        )\n                                      ) +\n                                      \"\\n                  \"\n                                  )\n                                ]\n                              )\n                            : _vm._e()\n                        ],\n                        1\n                      )\n                    : _vm._e()\n                ]),\n                _c(\"div\", { staticClass: \"header-right\" }, [\n                  _c(\"div\", { staticClass: \"results-count\" }, [\n                    _c(\"span\", { staticClass: \"count-number\" }, [\n                      _vm._v(_vm._s(_vm.filteredPlugins.length))\n                    ]),\n                    _vm._v(\" 个一级插件，共 \"),\n                    _c(\"span\", { staticClass: \"count-number\" }, [\n                      _vm._v(_vm._s(_vm.filteredTotalPlugins))\n                    ]),\n                    _vm._v(\" 个插件\\n                \")\n                  ])\n                ])\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"plugins-grid-wrapper\" },\n                [\n                  _c(\"PluginGrid\", {\n                    attrs: {\n                      plugins: _vm.currentPagePlugins,\n                      loading: _vm.loading,\n                      error: _vm.error\n                    },\n                    on: {\n                      \"plugin-use\": _vm.handlePluginUse,\n                      \"plugin-detail\": _vm.handlePluginDetail,\n                      \"combined-plugin-detail\": _vm.viewCombinedPluginDetails,\n                      retry: _vm.handleRetry,\n                      \"clear-filters\": _vm.clearAllFilters\n                    }\n                  })\n                ],\n                1\n              ),\n              _vm.filteredPlugins.length > _vm.pageSize\n                ? _c(\n                    \"div\",\n                    { staticClass: \"pagination-wrapper\" },\n                    [\n                      _c(\"a-pagination\", {\n                        attrs: {\n                          current: _vm.currentPage,\n                          total: _vm.filteredPlugins.length,\n                          \"page-size\": _vm.pageSize,\n                          \"page-size-options\": [\"8\", \"12\", \"16\", \"24\"],\n                          \"show-size-changer\": true,\n                          \"show-quick-jumper\": true,\n                          \"show-total\": function(total, range) {\n                            return (\n                              \"显示第 \" +\n                              range[0] +\n                              \"-\" +\n                              range[1] +\n                              \" 条，共 \" +\n                              total +\n                              \" 条\"\n                            )\n                          }\n                        },\n                        on: {\n                          change: _vm.handlePageChange,\n                          showSizeChange: _vm.handlePageSizeChange\n                        }\n                      })\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ])\n          ])\n        ])\n      ])\n    ]),\n    _vm.showCombinedModal\n      ? _c(\n          \"div\",\n          {\n            staticClass: \"combined-modal-overlay\",\n            on: { click: _vm.closeCombinedModal }\n          },\n          [\n            _c(\n              \"div\",\n              {\n                staticClass: \"combined-modal-content\",\n                on: {\n                  click: function($event) {\n                    $event.stopPropagation()\n                  }\n                }\n              },\n              [\n                _c(\"div\", { staticClass: \"combined-modal-header\" }, [\n                  _c(\"div\", { staticClass: \"header-content1\" }, [\n                    _c(\"div\", { staticClass: \"header-icon\" }, [_vm._v(\"🔗\")]),\n                    _c(\"div\", { staticClass: \"header-text\" }, [\n                      _c(\"h2\", [_vm._v(\"选择插件\")]),\n                      _c(\"p\", { staticClass: \"header-subtitle\" }, [\n                        _vm._v(\n                          _vm._s(\n                            _vm.selectedCombinedPlugin &&\n                              _vm.selectedCombinedPlugin.combinedName\n                          )\n                        )\n                      ])\n                    ])\n                  ]),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"combined-modal-close\",\n                      on: { click: _vm.closeCombinedModal }\n                    },\n                    [_vm._v(\"×\")]\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"combined-modal-body\" }, [\n                  _vm.combinedModalLoading\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"loading-state\" },\n                        [\n                          _c(\"a-spin\", { attrs: { size: \"large\" } }),\n                          _c(\"p\", [_vm._v(\"正在加载子插件...\")])\n                        ],\n                        1\n                      )\n                    : _vm.combinedSubPlugins.length === 0\n                    ? _c(\"div\", { staticClass: \"empty-state\" }, [\n                        _c(\"p\", [_vm._v(\"暂无子插件\")])\n                      ])\n                    : _c(\n                        \"div\",\n                        { staticClass: \"sub-plugins-grid\" },\n                        _vm._l(_vm.combinedSubPlugins, function(subPlugin) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: subPlugin.id,\n                              staticClass: \"sub-plugin-card\",\n                              on: {\n                                click: function($event) {\n                                  return _vm.selectSubPlugin(subPlugin)\n                                }\n                              }\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"sub-plugin-image\" }, [\n                                _c(\"img\", {\n                                  attrs: {\n                                    src: _vm.getPluginImage(subPlugin),\n                                    alt: subPlugin.plubname\n                                  }\n                                }),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"sub-plugin-category\" },\n                                  [\n                                    _c(\n                                      \"span\",\n                                      { staticClass: \"category-icon\" },\n                                      [_vm._v(\"🔧\")]\n                                    ),\n                                    _c(\"span\", [\n                                      _vm._v(\n                                        _vm._s(\n                                          subPlugin.plubCategory_dictText ||\n                                            \"其他\"\n                                        )\n                                      )\n                                    ])\n                                  ]\n                                ),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"sub-plugin-status\" },\n                                  [_vm._v(\"上架\")]\n                                )\n                              ]),\n                              _c(\"div\", { staticClass: \"sub-plugin-content\" }, [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"sub-plugin-header\" },\n                                  [\n                                    _c(\n                                      \"h3\",\n                                      { staticClass: \"sub-plugin-title\" },\n                                      [_vm._v(_vm._s(subPlugin.plubname))]\n                                    ),\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"sub-plugin-author\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"author-icon\" },\n                                          [_vm._v(\"👤\")]\n                                        ),\n                                        _c(\"span\", [\n                                          _vm._v(\n                                            \"创作者 \" +\n                                              _vm._s(\n                                                subPlugin.plubwrite_dictText ||\n                                                  \"未知\"\n                                              )\n                                          )\n                                        ])\n                                      ]\n                                    )\n                                  ]\n                                ),\n                                _c(\n                                  \"p\",\n                                  { staticClass: \"sub-plugin-description\" },\n                                  [\n                                    _vm._v(\n                                      _vm._s(subPlugin.plubinfo || \"暂无描述\")\n                                    )\n                                  ]\n                                ),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"sub-plugin-footer\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      { staticClass: \"sub-plugin-price\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"price-amount\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(\n                                                _vm.getSubPluginPriceText(\n                                                  subPlugin\n                                                )\n                                              )\n                                            )\n                                          ]\n                                        )\n                                      ]\n                                    ),\n                                    _c(\n                                      \"button\",\n                                      { staticClass: \"sub-plugin-btn\" },\n                                      [\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"btn-icon\" },\n                                          [_vm._v(\"👁\")]\n                                        ),\n                                        _c(\"span\", [_vm._v(\"查看详情\")])\n                                      ]\n                                    )\n                                  ]\n                                )\n                              ])\n                            ]\n                          )\n                        }),\n                        0\n                      )\n                ])\n              ]\n            )\n          ]\n        )\n      : _vm._e(),\n    _vm.showJianYingFloat\n      ? _c(\"div\", { staticClass: \"jianying-float-container\" }, [\n          _c(\n            \"button\",\n            {\n              staticClass: \"jianying-float-btn\",\n              on: { click: _vm.goToJianYingDraft }\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"float-close-btn\",\n                  on: {\n                    click: function($event) {\n                      $event.stopPropagation()\n                      return _vm.hideJianYingFloat($event)\n                    }\n                  }\n                },\n                [_c(\"a-icon\", { attrs: { type: \"close\" } })],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"btn-icon\" },\n                [_c(\"a-icon\", { attrs: { type: \"download\" } })],\n                1\n              ),\n              _c(\"div\", { staticClass: \"btn-text\" }, [\n                _vm._v(\"剪映小助手下载\")\n              ]),\n              _c(\"div\", { staticClass: \"btn-glow\" }),\n              _c(\"div\", { ref: \"particles\", staticClass: \"btn-particles\" }),\n              _c(\"div\", { staticClass: \"jianying-waves\" })\n            ]\n          )\n        ])\n      : _vm._e()\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}