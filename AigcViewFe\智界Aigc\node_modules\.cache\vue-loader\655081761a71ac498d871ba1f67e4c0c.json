{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=template&id=f8301f64&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754040785821}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"agent-detail-modal\",\n      attrs: {\n        visible: _vm.visible,\n        width: 800,\n        footer: null,\n        closable: false,\n        maskClosable: true,\n        bodyStyle: { padding: 0 }\n      },\n      on: { cancel: _vm.handleClose }\n    },\n    [\n      _vm.loading\n        ? _c(\n            \"div\",\n            { staticClass: \"loading-container\" },\n            [\n              _c(\"a-spin\", { attrs: { size: \"large\", tip: \"加载中...\" } }, [\n                _c(\"div\", { staticClass: \"loading-placeholder\" })\n              ])\n            ],\n            1\n          )\n        : _c(\"div\", { staticClass: \"modal-content\" }, [\n            _c(\"div\", { staticClass: \"agent-info-section\" }, [\n              _c(\"div\", { staticClass: \"agent-header\" }, [\n                _c(\"div\", { staticClass: \"agent-avatar\" }, [\n                  _vm.agentDetail.agentAvatar\n                    ? _c(\"img\", {\n                        attrs: {\n                          src: _vm.agentDetail.agentAvatar,\n                          alt: _vm.agentDetail.agentName\n                        },\n                        on: { error: _vm.handleImageError }\n                      })\n                    : _c(\n                        \"div\",\n                        { staticClass: \"avatar-placeholder\" },\n                        [_c(\"a-icon\", { attrs: { type: \"robot\" } })],\n                        1\n                      )\n                ]),\n                _c(\"div\", { staticClass: \"agent-basic-info\" }, [\n                  _c(\"h2\", { staticClass: \"agent-name\" }, [\n                    _vm._v(_vm._s(_vm.agentDetail.agentName))\n                  ]),\n                  _c(\"p\", { staticClass: \"agent-description\" }, [\n                    _vm._v(_vm._s(_vm.agentDetail.agentDescription))\n                  ]),\n                  _c(\"div\", { staticClass: \"creator-info\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"creator-avatar\" },\n                      [\n                        _vm.agentDetail.creatorInfo &&\n                        _vm.agentDetail.creatorInfo.avatar\n                          ? _c(\"img\", {\n                              attrs: {\n                                src: _vm.agentDetail.creatorInfo.avatar,\n                                alt: _vm.agentDetail.creatorInfo.name\n                              },\n                              on: { error: _vm.handleCreatorAvatarError }\n                            })\n                          : _c(\"a-icon\", { attrs: { type: \"user\" } })\n                      ],\n                      1\n                    ),\n                    _c(\"div\", { staticClass: \"creator-details\" }, [\n                      _c(\"span\", { staticClass: \"creator-name\" }, [\n                        _vm._v(_vm._s(_vm.creatorName))\n                      ]),\n                      _c(\"span\", { staticClass: \"creator-type\" }, [\n                        _vm._v(_vm._s(_vm.authorTypeText))\n                      ])\n                    ])\n                  ])\n                ]),\n                _c(\"div\", { staticClass: \"price-section\" }, [\n                  _vm.agentDetail.isFree\n                    ? _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"free-price\" }, [\n                          _vm._v(\"免费\")\n                        ])\n                      ])\n                    : _vm.agentDetail.showDiscountPrice\n                    ? _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"discount-price\" }, [\n                          _vm._v(\"¥\" + _vm._s(_vm.finalPrice))\n                        ]),\n                        _c(\"span\", { staticClass: \"original-price\" }, [\n                          _vm._v(\n                            \"¥\" + _vm._s(_vm.agentDetail.originalPrice || 0)\n                          )\n                        ])\n                      ])\n                    : _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"current-price\" }, [\n                          _vm._v(\n                            \"¥\" + _vm._s(_vm.agentDetail.originalPrice || 0)\n                          )\n                        ])\n                      ])\n                ])\n              ])\n            ]),\n            _vm.agentDetail.demoVideo\n              ? _c(\"div\", { staticClass: \"demo-video-section\" }, [\n                  _c(\"h3\", { staticClass: \"section-title\" }, [\n                    _vm._v(\"演示视频\")\n                  ]),\n                  _c(\"div\", { staticClass: \"video-container\" }, [\n                    _c(\n                      \"video\",\n                      {\n                        staticClass: \"demo-video\",\n                        attrs: {\n                          src: _vm.agentDetail.demoVideo,\n                          controls: \"\",\n                          preload: \"metadata\"\n                        }\n                      },\n                      [_vm._v(\"\\n          您的浏览器不支持视频播放\\n        \")]\n                    )\n                  ])\n                ])\n              : _vm._e(),\n            _c(\"div\", { staticClass: \"workflow-section\" }, [\n              _c(\"h3\", { staticClass: \"section-title\" }, [\n                _vm._v(\"\\n        工作流列表\\n        \"),\n                _c(\"span\", { staticClass: \"workflow-count\" }, [\n                  _vm._v(\"(\" + _vm._s(_vm.workflowList.length) + \"个)\")\n                ])\n              ]),\n              _vm.workflowLoading\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-loading\" },\n                    [_c(\"a-spin\", { attrs: { tip: \"加载工作流中...\" } })],\n                    1\n                  )\n                : _vm.workflowList.length > 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-list\" },\n                    _vm._l(_vm.workflowList, function(workflow, index) {\n                      return _c(\n                        \"div\",\n                        { key: workflow.id, staticClass: \"workflow-item\" },\n                        [\n                          _c(\"div\", { staticClass: \"workflow-info\" }, [\n                            _c(\"div\", { staticClass: \"workflow-sequence\" }, [\n                              _vm._v(_vm._s(index + 1))\n                            ]),\n                            _c(\n                              \"div\",\n                              { staticClass: \"workflow-avatar\" },\n                              [\n                                workflow.agentAvatar ||\n                                _vm.agentDetail.agentAvatar\n                                  ? _c(\"img\", {\n                                      attrs: {\n                                        src:\n                                          workflow.agentAvatar ||\n                                          _vm.agentDetail.agentAvatar,\n                                        alt: workflow.workflowName\n                                      },\n                                      on: {\n                                        error: _vm.handleWorkflowImageError\n                                      }\n                                    })\n                                  : _c(\"a-icon\", { attrs: { type: \"setting\" } })\n                              ],\n                              1\n                            ),\n                            _c(\"div\", { staticClass: \"workflow-details\" }, [\n                              _c(\"h4\", { staticClass: \"workflow-name\" }, [\n                                _vm._v(_vm._s(workflow.workflowName))\n                              ]),\n                              _c(\"p\", { staticClass: \"workflow-description\" }, [\n                                _vm._v(_vm._s(workflow.workflowDescription))\n                              ])\n                            ])\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"workflow-actions\" },\n                            [\n                              !_vm.isPurchased\n                                ? _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: { type: \"default\", disabled: \"\" },\n                                      on: { click: _vm.handleDownloadTip }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { type: \"download\" }\n                                      }),\n                                      _vm._v(\n                                        \"\\n              请先购买\\n            \"\n                                      )\n                                    ],\n                                    1\n                                  )\n                                : _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: {\n                                        type: \"primary\",\n                                        loading:\n                                          _vm.downloadLoading[workflow.id]\n                                      },\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.handleWorkflowDownload(\n                                            workflow\n                                          )\n                                        }\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { type: \"download\" }\n                                      }),\n                                      _vm._v(\n                                        \"\\n              下载\\n            \"\n                                      )\n                                    ],\n                                    1\n                                  )\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    }),\n                    0\n                  )\n                : _c(\n                    \"div\",\n                    { staticClass: \"workflow-empty\" },\n                    [_c(\"a-empty\", { attrs: { description: \"暂无工作流\" } })],\n                    1\n                  )\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"action-buttons\" },\n              [\n                _c(\n                  \"a-button\",\n                  { staticClass: \"close-btn\", on: { click: _vm.handleClose } },\n                  [_vm._v(\"\\n        关闭\\n      \")]\n                ),\n                _vm.isPurchased\n                  ? _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"detail-btn\",\n                        attrs: { type: \"default\" },\n                        on: { click: _vm.handleViewDetail }\n                      },\n                      [_vm._v(\"\\n        查看详情\\n      \")]\n                    )\n                  : _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"detail-btn disabled\",\n                        attrs: { type: \"default\", disabled: \"\" }\n                      },\n                      [_vm._v(\"\\n        查看详情\\n      \")]\n                    ),\n                !_vm.isPurchased\n                  ? _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"purchase-btn\",\n                        attrs: {\n                          type: \"primary\",\n                          loading: _vm.purchaseLoading\n                        },\n                        on: { click: _vm.handlePurchase }\n                      },\n                      [_vm._v(\"\\n        立即购买\\n      \")]\n                    )\n                  : _vm._e(),\n                _vm.agentDetail.experienceLink\n                  ? _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"experience-btn\",\n                        attrs: { type: \"default\" },\n                        on: { click: _vm.handleExperience }\n                      },\n                      [_vm._v(\"\\n        体验智能体\\n      \")]\n                    )\n                  : _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"experience-btn disabled\",\n                        attrs: { type: \"default\", disabled: \"\" }\n                      },\n                      [_vm._v(\"\\n        暂无体验\\n      \")]\n                    )\n              ],\n              1\n            )\n          ])\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}