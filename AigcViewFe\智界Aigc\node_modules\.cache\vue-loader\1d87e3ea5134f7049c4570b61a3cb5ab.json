{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue?vue&type=template&id=a4b5014e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue", "mtime": 1753945012309}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"plugin-detail-page\">\n  <!-- 页面加载状态 -->\n  <div v-if=\"loading\" class=\"loading-container\">\n    <div class=\"loading-content\">\n      <a-spin size=\"large\" tip=\"加载插件详情中...\">\n        <div class=\"loading-skeleton\">\n          <!-- 骨架屏效果 -->\n          <div class=\"skeleton-header\">\n            <div class=\"skeleton-breadcrumb\"></div>\n            <div class=\"skeleton-plugin-info\">\n              <div class=\"skeleton-image\"></div>\n              <div class=\"skeleton-details\">\n                <div class=\"skeleton-title\"></div>\n                <div class=\"skeleton-description\"></div>\n                <div class=\"skeleton-meta\">\n                  <div class=\"skeleton-tag\"></div>\n                  <div class=\"skeleton-tag\"></div>\n                  <div class=\"skeleton-tag\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"skeleton-tabs\">\n            <div class=\"skeleton-tab-bar\">\n              <div class=\"skeleton-tab\"></div>\n              <div class=\"skeleton-tab\"></div>\n              <div class=\"skeleton-tab\"></div>\n              <div class=\"skeleton-tab\"></div>\n            </div>\n            <div class=\"skeleton-tab-content\"></div>\n          </div>\n        </div>\n      </a-spin>\n    </div>\n  </div>\n\n  <!-- 页面内容 -->\n  <div v-else-if=\"pluginDetail\" class=\"plugin-detail-content\">\n    <!-- 优化后的导航栏 -->\n    <div class=\"navigation-bar\">\n      <!-- 左侧导航信息 -->\n      <div class=\"nav-left\">\n        <!-- 返回按钮 -->\n        <a-button\n          type=\"text\"\n          size=\"large\"\n          @click=\"goBack\"\n          class=\"back-button\">\n          <a-icon type=\"arrow-left\" />\n        </a-button>\n\n        <!-- 面包屑路径 -->\n        <div class=\"breadcrumb-path\">\n          <span class=\"path-item\" @click=\"$router.push('/')\">\n            <a-icon type=\"home\" />\n            首页\n          </span>\n          <a-icon type=\"right\" class=\"path-separator\" />\n          <span class=\"path-item\" @click=\"goBackToMarket\">\n            <a-icon type=\"shop\" />\n            商城\n          </span>\n          <a-icon type=\"right\" class=\"path-separator\" />\n          <span class=\"path-current\">\n            {{ pluginDetail.plubname || '插件详情' }}\n          </span>\n        </div>\n      </div>\n\n      <!-- 右侧操作按钮 -->\n      <div class=\"nav-right\">\n        <a-button @click=\"sharePlugin\" class=\"share-button\">\n          <a-icon type=\"share-alt\" />\n          分享\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 插件头部信息区域 -->\n    <div class=\"plugin-header\">\n      <div class=\"plugin-header-content\">\n        <!-- 插件封面图 -->\n        <div class=\"plugin-image\">\n          <img\n            :src=\"getPluginImage(pluginDetail)\"\n            :alt=\"pluginDetail.plubname\"\n            @error=\"handleImageError\"\n          />\n        </div>\n\n        <!-- 插件基础信息 -->\n        <div class=\"plugin-info\">\n          <h1 class=\"plugin-title\">{{ pluginDetail.plubname }}</h1>\n          <p class=\"plugin-description\">{{ pluginDetail.plubinfo }}</p>\n          \n          <div class=\"plugin-meta\">\n            <!-- 第一行：分类和状态 -->\n            <div class=\"meta-row\">\n              <div class=\"meta-item\">\n                <a-tag :color=\"getCategoryColor(pluginDetail.plubCategory)\" size=\"large\">\n                  <a-icon type=\"appstore\" />\n                  {{ categoryText }}\n                </a-tag>\n              </div>\n              <div class=\"meta-item\">\n                <a-tag :color=\"getStatusColor(pluginDetail.status)\" size=\"large\">\n                  <a-icon type=\"check-circle\" />\n                  {{ getStatusText(pluginDetail.status) }}\n                </a-tag>\n              </div>\n            </div>\n\n            <!-- 第二行：创作者和价格 -->\n            <div class=\"meta-row\">\n              <div class=\"meta-item\">\n                <a-icon type=\"user\" class=\"meta-icon\" />\n                <span class=\"meta-label\">创作者：</span>\n                <span class=\"meta-value\">{{ authorInfo.authorname || '未知' }}</span>\n              </div>\n              <div class=\"meta-item price-item\">\n                <a-icon type=\"dollar\" class=\"meta-icon price-icon\" />\n                <span class=\"meta-label\">价格：</span>\n                <span class=\"meta-value price-value\">{{ getDetailPriceText() }}</span>\n              </div>\n              <div v-if=\"hasTutorial\" class=\"meta-item tutorial-item\">\n                <span class=\"tutorial-hint\" @click=\"goToTutorial\">\n                  本插件有教程视频，详细请点此观看\n                </span>\n              </div>\n            </div>\n\n            <!-- 第三行：时间信息 -->\n            <div class=\"meta-row\">\n              <div class=\"meta-item\">\n                <a-icon type=\"calendar\" class=\"meta-icon\" />\n                <span class=\"meta-label\">发布时间：</span>\n                <span class=\"meta-value\">{{ formatDate(pluginDetail.createTime) }}</span>\n              </div>\n              <div class=\"meta-item\">\n                <a-icon type=\"sync\" class=\"meta-icon\" />\n                <span class=\"meta-label\">更新时间：</span>\n                <span class=\"meta-value\">{{ formatDate(pluginDetail.updateTime) }}</span>\n              </div>\n            </div>\n\n            <!-- 统计信息已移除 -->\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Tab切换内容区域 -->\n    <div class=\"plugin-tabs-container\">\n      <!-- 自定义Tab导航 -->\n      <div class=\"custom-tabs-nav\">\n        <div class=\"tabs-nav-wrapper\">\n          <div\n            v-for=\"(tab, index) in tabList\"\n            :key=\"tab.key\"\n            :class=\"['tab-item', { 'active': activeTab === tab.key }]\"\n            @click=\"handleTabClick(tab.key)\"\n          >\n            <i :class=\"tab.icon\"></i>\n            <span>{{ tab.label }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Tab内容区域 -->\n      <div class=\"custom-tabs-content\">\n        <div v-show=\"activeTab === 'intro'\" class=\"tab-pane\">\n          <plugin-introduction\n            :content=\"pluginDetail.plubContent\"\n            :info=\"pluginDetail.plubinfo\"\n            :plugin-name=\"pluginDetail.plubname\"\n            :plugin-detail=\"pluginDetail\"\n          />\n        </div>\n        <div v-show=\"activeTab === 'tutorial'\" class=\"tab-pane\">\n          <plugin-tutorial\n            :tutorial-link=\"pluginDetail.tutorialLink\"\n            :video-file=\"pluginDetail.plubvideo\"\n            :plugin-name=\"pluginDetail.plubname\"\n            :detailed-content=\"pluginDetail.plubContent\"\n          />\n        </div>\n        <div v-show=\"activeTab === 'features'\" class=\"tab-pane\">\n          <plugin-features\n            :plugin-detail=\"pluginDetail\"\n            :category=\"pluginDetail.plubCategory\"\n          />\n        </div>\n        <div v-show=\"activeTab === 'tech'\" class=\"tab-pane\">\n          <plugin-technical\n            :plugin-detail=\"pluginDetail\"\n            :plugin-key=\"pluginDetail.pluginKey\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- 创作者信息区域 -->\n    <div class=\"author-section\">\n      <author-info\n        :author=\"authorInfo\"\n        :plugin-count=\"authorPluginCount\"\n      />\n    </div>\n\n    <!-- 相关推荐区域 -->\n    <div class=\"recommendations-section\">\n      <related-plugins\n        :recommendations=\"recommendations\"\n        :current-category=\"pluginDetail.plubCategory\"\n        :current-plugin-id=\"pluginDetail.id\"\n      />\n    </div>\n  </div>\n\n  <!-- 错误状态 -->\n  <div v-else class=\"error-container\">\n    <a-result\n      :status=\"errorStatus\"\n      :title=\"errorTitle\"\n      :sub-title=\"errorMessage\"\n    >\n      <template #extra>\n        <div class=\"error-actions\">\n          <a-button type=\"primary\" @click=\"retryLoad\" v-if=\"canRetry\">\n            <a-icon type=\"reload\" />\n            重新加载\n          </a-button>\n          <a-button @click=\"$router.push('/market')\">\n            <a-icon type=\"shop\" />\n            返回商城\n          </a-button>\n          <a-button type=\"dashed\" @click=\"goHome\">\n            <a-icon type=\"home\" />\n            返回首页\n          </a-button>\n        </div>\n      </template>\n    </a-result>\n  </div>\n  </div>\n</WebsitePage>\n", null]}