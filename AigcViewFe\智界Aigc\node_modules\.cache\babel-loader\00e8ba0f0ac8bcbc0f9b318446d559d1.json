{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\mixins\\JEditableTableModelMixin.js", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\mixins\\JEditableTableModelMixin.js", "mtime": 1634805070000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import JEditableTable from '@/components/jeecg/JEditableTable';\nimport { VALIDATE_NO_PASSED, getRefPromise, validateFormModelAndTables } from '@/utils/JEditableTableUtil';\nimport { httpAction, getAction } from '@/api/manage';\nexport var JEditableTableModelMixin = {\n  components: {\n    JEditableTable: JEditableTable\n  },\n  data: function data() {\n    return {\n      title: '操作',\n      visible: false,\n      confirmLoading: false,\n      model: {},\n      labelCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 6\n        }\n      },\n      wrapperCol: {\n        xs: {\n          span: 24\n        },\n        sm: {\n          span: 18\n        }\n      }\n    };\n  },\n  methods: {\n    /** 获取所有的editableTable实例 */\n    getAllTable: function getAllTable() {\n      var _this = this;\n\n      if (!(this.refKeys instanceof Array)) {\n        throw this.throwNotArray('refKeys');\n      }\n\n      var values = this.refKeys.map(function (key) {\n        return getRefPromise(_this, key);\n      });\n      return Promise.all(values);\n    },\n\n    /** 遍历所有的JEditableTable实例 */\n    eachAllTable: function eachAllTable(callback) {\n      // 开始遍历\n      this.getAllTable().then(function (tables) {\n        tables.forEach(function (item, index) {\n          if (typeof callback === 'function') {\n            callback(item, index);\n          }\n        });\n      });\n    },\n\n    /** 当点击新增按钮时调用此方法 */\n    add: function add() {\n      var _this2 = this;\n\n      //update-begin-author:lvdandan date:20201113 for:LOWCOD-1049 JEditaTable,子表默认添加一条数据，addDefaultRowNum设置无效 #1930\n      return new Promise(function (resolve) {\n        _this2.tableReset();\n\n        resolve();\n      }).then(function () {\n        if (typeof _this2.addBefore === 'function') _this2.addBefore(); // 默认新增空数据\n\n        var rowNum = _this2.addDefaultRowNum;\n\n        if (typeof rowNum !== 'number') {\n          rowNum = 1;\n          console.warn('由于你没有在 data 中定义 addDefaultRowNum 或 addDefaultRowNum 不是数字，所以默认添加一条空数据，如果不想默认添加空数据，请将定义 addDefaultRowNum 为 0');\n        }\n\n        _this2.eachAllTable(function (item) {\n          item.add(rowNum);\n        });\n\n        if (typeof _this2.addAfter === 'function') _this2.addAfter(_this2.model);\n\n        _this2.edit(_this2.model);\n      }); //update-end-author:lvdandan date:20201113 for:LOWCOD-1049 JEditaTable,子表默认添加一条数据，addDefaultRowNum设置无效 #1930\n    },\n\n    /** 当点击了编辑（修改）按钮时调用此方法 */\n    edit: function edit(record) {\n      if (record && '{}' != JSON.stringify(record) && record.id) {\n        this.tableReset();\n      }\n\n      if (typeof this.editBefore === 'function') this.editBefore(record);\n      this.visible = true;\n      this.activeKey = this.refKeys[0];\n      this.$refs.form.resetFields();\n      this.model = Object.assign({}, record);\n      if (typeof this.editAfter === 'function') this.editAfter(this.model);\n    },\n\n    /** 关闭弹窗，并将所有JEditableTable实例回归到初始状态 */\n    close: function close() {\n      this.visible = false;\n      this.$emit('close');\n    },\n    //清空子表table的数据\n    tableReset: function tableReset() {\n      this.eachAllTable(function (item) {\n        item.clearRow();\n      });\n    },\n\n    /** 查询某个tab的数据 */\n    requestSubTableData: function requestSubTableData(url, params, tab, success) {\n      tab.loading = true;\n      getAction(url, params).then(function (res) {\n        var result = res.result;\n        var dataSource = [];\n\n        if (result) {\n          if (Array.isArray(result)) {\n            dataSource = result;\n          } else if (Array.isArray(result.records)) {\n            dataSource = result.records;\n          }\n        }\n\n        tab.dataSource = dataSource;\n        typeof success === 'function' ? success(res) : '';\n      }).finally(function () {\n        tab.loading = false;\n      });\n    },\n\n    /** 发起请求，自动判断是执行新增还是修改操作 */\n    request: function request(formData) {\n      var _this3 = this;\n\n      var url = this.url.add,\n          method = 'post';\n\n      if (this.model.id) {\n        url = this.url.edit;\n        method = 'put';\n      }\n\n      this.confirmLoading = true;\n      httpAction(url, formData, method).then(function (res) {\n        if (res.success) {\n          _this3.$message.success(res.message);\n\n          _this3.$emit('ok');\n\n          _this3.close();\n        } else {\n          _this3.$message.warning(res.message);\n        }\n      }).finally(function () {\n        _this3.confirmLoading = false;\n      });\n    },\n\n    /* --- handle 事件 --- */\n\n    /** ATab 选项卡切换事件 */\n    handleChangeTabs: function handleChangeTabs(key) {\n      // 自动重置scrollTop状态，防止出现白屏\n      getRefPromise(this, key).then(function (editableTable) {\n        editableTable.resetScrollTop();\n      });\n    },\n\n    /** 关闭按钮点击事件 */\n    handleCancel: function handleCancel() {\n      this.close();\n    },\n\n    /** 确定按钮点击事件 */\n    handleOk: function handleOk() {\n      var _this4 = this;\n\n      /** 触发表单验证 */\n      this.getAllTable().then(function (tables) {\n        /** 一次性验证主表和所有的次表 */\n        return validateFormModelAndTables(_this4.$refs.form, _this4.model, tables);\n      }).then(function (allValues) {\n        /** 一次性验证一对一的所有子表 */\n        return _this4.validateSubForm(allValues);\n      }).then(function (allValues) {\n        if (typeof _this4.classifyIntoFormData !== 'function') {\n          throw _this4.throwNotFunction('classifyIntoFormData');\n        }\n\n        var formData = _this4.classifyIntoFormData(allValues); // 发起请求\n\n\n        return _this4.request(formData);\n      }).catch(function (e) {\n        if (e.error === VALIDATE_NO_PASSED) {\n          // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n          //update--begin--autor:liusq-----date:20210316------for：未通过表单验证跳转tab问题------\n          _this4.activeKey = e.index == null ? _this4.activeKey : e.paneKey ? e.paneKey : _this4.refKeys[e.index]; //update--end--autor:liusq-----date:20210316------for：未通过表单验证跳转tab问题------\n        } else {\n          console.error(e);\n        }\n      });\n    },\n    //校验所有子表表单\n    validateSubForm: function validateSubForm(allValues) {\n      return new Promise(function (resolve) {\n        resolve(allValues);\n      });\n    },\n\n    /* --- throw --- */\n\n    /** not a function */\n    throwNotFunction: function throwNotFunction(name) {\n      return \"\".concat(name, \" \\u672A\\u5B9A\\u4E49\\u6216\\u4E0D\\u662F\\u4E00\\u4E2A\\u51FD\\u6570\");\n    },\n\n    /** not a array */\n    throwNotArray: function throwNotArray(name) {\n      return \"\".concat(name, \" \\u672A\\u5B9A\\u4E49\\u6216\\u4E0D\\u662F\\u4E00\\u4E2A\\u6570\\u7EC4\");\n    }\n  }\n};", {"version": 3, "sources": ["D:/AigcView_zj/AigcViewFe/智界Aigc/src/mixins/JEditableTableModelMixin.js"], "names": ["JEditableTable", "VALIDATE_NO_PASSED", "getRefPromise", "validateFormModelAndTables", "httpAction", "getAction", "JEditableTableModelMixin", "components", "data", "title", "visible", "confirmLoading", "model", "labelCol", "xs", "span", "sm", "wrapperCol", "methods", "getAllTable", "refKeys", "Array", "throwNotArray", "values", "map", "key", "Promise", "all", "eachAllTable", "callback", "then", "tables", "for<PERSON>ach", "item", "index", "add", "resolve", "tableReset", "addBefore", "row<PERSON>um", "addDefaultRowNum", "console", "warn", "addAfter", "edit", "record", "JSON", "stringify", "id", "editBefore", "active<PERSON><PERSON>", "$refs", "form", "resetFields", "Object", "assign", "editAfter", "close", "$emit", "clearRow", "requestSubTableData", "url", "params", "tab", "success", "loading", "res", "result", "dataSource", "isArray", "records", "finally", "request", "formData", "method", "$message", "message", "warning", "handleChangeTabs", "editableTable", "resetScrollTop", "handleCancel", "handleOk", "allValues", "validateSubForm", "classifyIntoFormData", "throwNotFunction", "catch", "e", "error", "paneKey", "name"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,mCAA3B;AACA,SAASC,kBAAT,EAA6BC,aAA7B,EAA2CC,0BAA3C,QAA4E,4BAA5E;AACA,SAASC,UAAT,EAAqBC,SAArB,QAAsC,cAAtC;AAEA,OAAO,IAAMC,wBAAwB,GAAG;AACtCC,EAAAA,UAAU,EAAE;AACVP,IAAAA,cAAc,EAAdA;AADU,GAD0B;AAItCQ,EAAAA,IAJsC,kBAI/B;AACL,WAAO;AACLC,MAAAA,KAAK,EAAE,IADF;AAELC,MAAAA,OAAO,EAAE,KAFJ;AAGLC,MAAAA,cAAc,EAAE,KAHX;AAILC,MAAAA,KAAK,EAAC,EAJD;AAKLC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,EAAE,EAAE;AAAEC,UAAAA,IAAI,EAAE;AAAR,SADI;AAERC,QAAAA,EAAE,EAAE;AAAED,UAAAA,IAAI,EAAE;AAAR;AAFI,OALL;AASLE,MAAAA,UAAU,EAAE;AACVH,QAAAA,EAAE,EAAE;AAAEC,UAAAA,IAAI,EAAE;AAAR,SADM;AAEVC,QAAAA,EAAE,EAAE;AAAED,UAAAA,IAAI,EAAE;AAAR;AAFM;AATP,KAAP;AAcD,GAnBqC;AAoBtCG,EAAAA,OAAO,EAAE;AAEP;AACAC,IAAAA,WAHO,yBAGO;AAAA;;AACZ,UAAI,EAAE,KAAKC,OAAL,YAAwBC,KAA1B,CAAJ,EAAsC;AACpC,cAAM,KAAKC,aAAL,CAAmB,SAAnB,CAAN;AACD;;AACD,UAAIC,MAAM,GAAG,KAAKH,OAAL,CAAaI,GAAb,CAAiB,UAAAC,GAAG;AAAA,eAAIvB,aAAa,CAAC,KAAD,EAAOuB,GAAP,CAAjB;AAAA,OAApB,CAAb;AACA,aAAOC,OAAO,CAACC,GAAR,CAAYJ,MAAZ,CAAP;AACD,KATM;;AAWP;AACAK,IAAAA,YAZO,wBAYMC,QAZN,EAYgB;AACrB;AACA,WAAKV,WAAL,GAAmBW,IAAnB,CAAwB,UAAAC,MAAM,EAAI;AAChCA,QAAAA,MAAM,CAACC,OAAP,CAAe,UAACC,IAAD,EAAOC,KAAP,EAAiB;AAC9B,cAAI,OAAOL,QAAP,KAAoB,UAAxB,EAAoC;AAClCA,YAAAA,QAAQ,CAACI,IAAD,EAAOC,KAAP,CAAR;AACD;AACF,SAJD;AAKD,OAND;AAOD,KArBM;;AAsBP;AACAC,IAAAA,GAvBO,iBAuBD;AAAA;;AACJ;AACA,aAAO,IAAIT,OAAJ,CAAY,UAACU,OAAD,EAAa;AAC9B,QAAA,MAAI,CAACC,UAAL;;AACAD,QAAAA,OAAO;AACR,OAHM,EAGJN,IAHI,CAGC,YAAM;AACZ,YAAI,OAAO,MAAI,CAACQ,SAAZ,KAA0B,UAA9B,EAA0C,MAAI,CAACA,SAAL,GAD9B,CAEZ;;AACA,YAAIC,MAAM,GAAG,MAAI,CAACC,gBAAlB;;AACA,YAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;AAC9BA,UAAAA,MAAM,GAAG,CAAT;AACAE,UAAAA,OAAO,CAACC,IAAR,CAAa,4GAAb;AACD;;AACD,QAAA,MAAI,CAACd,YAAL,CAAkB,UAACK,IAAD,EAAU;AAC1BA,UAAAA,IAAI,CAACE,GAAL,CAASI,MAAT;AACD,SAFD;;AAGA,YAAI,OAAO,MAAI,CAACI,QAAZ,KAAyB,UAA7B,EAAyC,MAAI,CAACA,QAAL,CAAc,MAAI,CAAC/B,KAAnB;;AACzC,QAAA,MAAI,CAACgC,IAAL,CAAU,MAAI,CAAChC,KAAf;AACD,OAhBM,CAAP,CAFI,CAmBJ;AACD,KA3CM;;AA4CP;AACAgC,IAAAA,IA7CO,gBA6CFC,MA7CE,EA6CM;AACX,UAAGA,MAAM,IAAI,QAAMC,IAAI,CAACC,SAAL,CAAeF,MAAf,CAAhB,IAAwCA,MAAM,CAACG,EAAlD,EAAqD;AACnD,aAAKX,UAAL;AACD;;AACD,UAAI,OAAO,KAAKY,UAAZ,KAA2B,UAA/B,EAA2C,KAAKA,UAAL,CAAgBJ,MAAhB;AAC3C,WAAKnC,OAAL,GAAe,IAAf;AACA,WAAKwC,SAAL,GAAiB,KAAK9B,OAAL,CAAa,CAAb,CAAjB;AACA,WAAK+B,KAAL,CAAWC,IAAX,CAAgBC,WAAhB;AACA,WAAKzC,KAAL,GAAa0C,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBV,MAAlB,CAAb;AACA,UAAI,OAAO,KAAKW,SAAZ,KAA0B,UAA9B,EAA0C,KAAKA,SAAL,CAAe,KAAK5C,KAApB;AAC3C,KAvDM;;AAwDP;AACA6C,IAAAA,KAzDO,mBAyDC;AACN,WAAK/C,OAAL,GAAe,KAAf;AACA,WAAKgD,KAAL,CAAW,OAAX;AACD,KA5DM;AA6DP;AACArB,IAAAA,UA9DO,wBA8DK;AACV,WAAKT,YAAL,CAAkB,UAACK,IAAD,EAAU;AAC1BA,QAAAA,IAAI,CAAC0B,QAAL;AACD,OAFD;AAGD,KAlEM;;AAmEP;AACAC,IAAAA,mBApEO,+BAoEaC,GApEb,EAoEkBC,MApElB,EAoE0BC,GApE1B,EAoE+BC,OApE/B,EAoEwC;AAC7CD,MAAAA,GAAG,CAACE,OAAJ,GAAc,IAAd;AACA5D,MAAAA,SAAS,CAACwD,GAAD,EAAMC,MAAN,CAAT,CAAuBhC,IAAvB,CAA4B,UAAAoC,GAAG,EAAI;AAAA,YAC3BC,MAD2B,GAChBD,GADgB,CAC3BC,MAD2B;AAEjC,YAAIC,UAAU,GAAG,EAAjB;;AACA,YAAID,MAAJ,EAAY;AACV,cAAI9C,KAAK,CAACgD,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACzBC,YAAAA,UAAU,GAAGD,MAAb;AACD,WAFD,MAEO,IAAI9C,KAAK,CAACgD,OAAN,CAAcF,MAAM,CAACG,OAArB,CAAJ,EAAmC;AACxCF,YAAAA,UAAU,GAAGD,MAAM,CAACG,OAApB;AACD;AACF;;AACDP,QAAAA,GAAG,CAACK,UAAJ,GAAiBA,UAAjB;AACA,eAAOJ,OAAP,KAAmB,UAAnB,GAAgCA,OAAO,CAACE,GAAD,CAAvC,GAA+C,EAA/C;AACD,OAZD,EAYGK,OAZH,CAYW,YAAM;AACfR,QAAAA,GAAG,CAACE,OAAJ,GAAc,KAAd;AACD,OAdD;AAeD,KArFM;;AAsFP;AACAO,IAAAA,OAvFO,mBAuFCC,QAvFD,EAuFW;AAAA;;AAChB,UAAIZ,GAAG,GAAG,KAAKA,GAAL,CAAS1B,GAAnB;AAAA,UAAwBuC,MAAM,GAAG,MAAjC;;AACA,UAAI,KAAK9D,KAAL,CAAWoC,EAAf,EAAmB;AACjBa,QAAAA,GAAG,GAAG,KAAKA,GAAL,CAASjB,IAAf;AACA8B,QAAAA,MAAM,GAAG,KAAT;AACD;;AACD,WAAK/D,cAAL,GAAsB,IAAtB;AACAP,MAAAA,UAAU,CAACyD,GAAD,EAAMY,QAAN,EAAgBC,MAAhB,CAAV,CAAkC5C,IAAlC,CAAuC,UAACoC,GAAD,EAAS;AAC9C,YAAIA,GAAG,CAACF,OAAR,EAAiB;AACf,UAAA,MAAI,CAACW,QAAL,CAAcX,OAAd,CAAsBE,GAAG,CAACU,OAA1B;;AACA,UAAA,MAAI,CAAClB,KAAL,CAAW,IAAX;;AACA,UAAA,MAAI,CAACD,KAAL;AACD,SAJD,MAIO;AACL,UAAA,MAAI,CAACkB,QAAL,CAAcE,OAAd,CAAsBX,GAAG,CAACU,OAA1B;AACD;AACF,OARD,EAQGL,OARH,CAQW,YAAM;AACf,QAAA,MAAI,CAAC5D,cAAL,GAAsB,KAAtB;AACD,OAVD;AAWD,KAzGM;;AA2GP;;AAEA;AACAmE,IAAAA,gBA9GO,4BA8GUrD,GA9GV,EA8Ge;AACpB;AACAvB,MAAAA,aAAa,CAAC,IAAD,EAAOuB,GAAP,CAAb,CAAyBK,IAAzB,CAA8B,UAAAiD,aAAa,EAAI;AAC7CA,QAAAA,aAAa,CAACC,cAAd;AACD,OAFD;AAGD,KAnHM;;AAoHP;AACAC,IAAAA,YArHO,0BAqHQ;AACb,WAAKxB,KAAL;AACD,KAvHM;;AAwHP;AACAyB,IAAAA,QAzHO,sBAyHI;AAAA;;AACT;AACA,WAAK/D,WAAL,GAAmBW,IAAnB,CAAwB,UAAAC,MAAM,EAAI;AAChC;AACA,eAAO5B,0BAA0B,CAAC,MAAI,CAACgD,KAAL,CAAWC,IAAZ,EAAiB,MAAI,CAACxC,KAAtB,EAA6BmB,MAA7B,CAAjC;AACD,OAHD,EAGGD,IAHH,CAGQ,UAAAqD,SAAS,EAAI;AACnB;AACA,eAAO,MAAI,CAACC,eAAL,CAAqBD,SAArB,CAAP;AACD,OAND,EAMGrD,IANH,CAMQ,UAAAqD,SAAS,EAAI;AACnB,YAAI,OAAO,MAAI,CAACE,oBAAZ,KAAqC,UAAzC,EAAqD;AACnD,gBAAM,MAAI,CAACC,gBAAL,CAAsB,sBAAtB,CAAN;AACD;;AACD,YAAIb,QAAQ,GAAG,MAAI,CAACY,oBAAL,CAA0BF,SAA1B,CAAf,CAJmB,CAKnB;;;AACA,eAAO,MAAI,CAACX,OAAL,CAAaC,QAAb,CAAP;AACD,OAbD,EAaGc,KAbH,CAaS,UAAAC,CAAC,EAAI;AACZ,YAAIA,CAAC,CAACC,KAAF,KAAYxF,kBAAhB,EAAoC;AAClC;AACA;AACA,UAAA,MAAI,CAACiD,SAAL,GAAiBsC,CAAC,CAACtD,KAAF,IAAW,IAAX,GAAkB,MAAI,CAACgB,SAAvB,GAAoCsC,CAAC,CAACE,OAAF,GAAUF,CAAC,CAACE,OAAZ,GAAoB,MAAI,CAACtE,OAAL,CAAaoE,CAAC,CAACtD,KAAf,CAAzE,CAHkC,CAIlC;AACD,SALD,MAKO;AACLO,UAAAA,OAAO,CAACgD,KAAR,CAAcD,CAAd;AACD;AACF,OAtBD;AAuBD,KAlJM;AAmJP;AACAJ,IAAAA,eApJO,2BAoJSD,SApJT,EAoJmB;AACxB,aAAO,IAAIzD,OAAJ,CAAY,UAACU,OAAD,EAAa;AAC9BA,QAAAA,OAAO,CAAC+C,SAAD,CAAP;AACD,OAFM,CAAP;AAGD,KAxJM;;AAyJP;;AAEA;AACAG,IAAAA,gBA5JO,4BA4JUK,IA5JV,EA4JgB;AACrB,uBAAUA,IAAV;AACD,KA9JM;;AAgKP;AACArE,IAAAA,aAjKO,yBAiKOqE,IAjKP,EAiKa;AAClB,uBAAUA,IAAV;AACD;AAnKM;AApB6B,CAAjC", "sourcesContent": ["import JEditableTable from '@/components/jeecg/JEditableTable'\nimport { VALIDATE_NO_PASSED, getRefPromise,validateFormModelAndTables} from '@/utils/JEditableTableUtil'\nimport { httpAction, getAction } from '@/api/manage'\n\nexport const JEditableTableModelMixin = {\n  components: {\n    JEditableTable\n  },\n  data() {\n    return {\n      title: '操作',\n      visible: false,\n      confirmLoading: false,\n      model:{},\n      labelCol: {\n        xs: { span: 24 },\n        sm: { span: 6 }\n      },\n      wrapperCol: {\n        xs: { span: 24 },\n        sm: { span: 18 }\n      }\n    }\n  },\n  methods: {\n\n    /** 获取所有的editableTable实例 */\n    getAllTable() {\n      if (!(this.refKeys instanceof Array)) {\n        throw this.throwNotArray('refKeys')\n      }\n      let values = this.refKeys.map(key => getRefPromise(this, key))\n      return Promise.all(values)\n    },\n\n    /** 遍历所有的JEditableTable实例 */\n    eachAllTable(callback) {\n      // 开始遍历\n      this.getAllTable().then(tables => {\n        tables.forEach((item, index) => {\n          if (typeof callback === 'function') {\n            callback(item, index)\n          }\n        })\n      })\n    },\n    /** 当点击新增按钮时调用此方法 */\n    add() {\n      //update-begin-author:lvdandan date:20201113 for:LOWCOD-1049 JEditaTable,子表默认添加一条数据，addDefaultRowNum设置无效 #1930\n      return new Promise((resolve) => {\n        this.tableReset();\n        resolve();\n      }).then(() => {\n        if (typeof this.addBefore === 'function') this.addBefore()\n        // 默认新增空数据\n        let rowNum = this.addDefaultRowNum\n        if (typeof rowNum !== 'number') {\n          rowNum = 1\n          console.warn('由于你没有在 data 中定义 addDefaultRowNum 或 addDefaultRowNum 不是数字，所以默认添加一条空数据，如果不想默认添加空数据，请将定义 addDefaultRowNum 为 0')\n        }\n        this.eachAllTable((item) => {\n          item.add(rowNum)\n        })\n        if (typeof this.addAfter === 'function') this.addAfter(this.model)\n        this.edit(this.model)\n      })\n      //update-end-author:lvdandan date:20201113 for:LOWCOD-1049 JEditaTable,子表默认添加一条数据，addDefaultRowNum设置无效 #1930\n    },\n    /** 当点击了编辑（修改）按钮时调用此方法 */\n    edit(record) {\n      if(record && '{}'!=JSON.stringify(record)&&record.id){\n        this.tableReset();\n      }\n      if (typeof this.editBefore === 'function') this.editBefore(record)\n      this.visible = true\n      this.activeKey = this.refKeys[0]\n      this.$refs.form.resetFields()\n      this.model = Object.assign({}, record)\n      if (typeof this.editAfter === 'function') this.editAfter(this.model)\n    },\n    /** 关闭弹窗，并将所有JEditableTable实例回归到初始状态 */\n    close() {\n      this.visible = false\n      this.$emit('close')\n    },\n    //清空子表table的数据\n    tableReset(){\n      this.eachAllTable((item) => {\n        item.clearRow()\n      })\n    },\n    /** 查询某个tab的数据 */\n    requestSubTableData(url, params, tab, success) {\n      tab.loading = true\n      getAction(url, params).then(res => {\n        let { result } = res\n        let dataSource = []\n        if (result) {\n          if (Array.isArray(result)) {\n            dataSource = result\n          } else if (Array.isArray(result.records)) {\n            dataSource = result.records\n          }\n        }\n        tab.dataSource = dataSource\n        typeof success === 'function' ? success(res) : ''\n      }).finally(() => {\n        tab.loading = false\n      })\n    },\n    /** 发起请求，自动判断是执行新增还是修改操作 */\n    request(formData) {\n      let url = this.url.add, method = 'post'\n      if (this.model.id) {\n        url = this.url.edit\n        method = 'put'\n      }\n      this.confirmLoading = true\n      httpAction(url, formData, method).then((res) => {\n        if (res.success) {\n          this.$message.success(res.message)\n          this.$emit('ok')\n          this.close()\n        } else {\n          this.$message.warning(res.message)\n        }\n      }).finally(() => {\n        this.confirmLoading = false\n      })\n    },\n\n    /* --- handle 事件 --- */\n\n    /** ATab 选项卡切换事件 */\n    handleChangeTabs(key) {\n      // 自动重置scrollTop状态，防止出现白屏\n      getRefPromise(this, key).then(editableTable => {\n        editableTable.resetScrollTop()\n      })\n    },\n    /** 关闭按钮点击事件 */\n    handleCancel() {\n      this.close()\n    },\n    /** 确定按钮点击事件 */\n    handleOk() {\n      /** 触发表单验证 */\n      this.getAllTable().then(tables => {\n        /** 一次性验证主表和所有的次表 */\n        return validateFormModelAndTables(this.$refs.form,this.model, tables)\n      }).then(allValues => {\n        /** 一次性验证一对一的所有子表 */\n        return this.validateSubForm(allValues)\n      }).then(allValues => {\n        if (typeof this.classifyIntoFormData !== 'function') {\n          throw this.throwNotFunction('classifyIntoFormData')\n        }\n        let formData = this.classifyIntoFormData(allValues)\n        // 发起请求\n        return this.request(formData)\n      }).catch(e => {\n        if (e.error === VALIDATE_NO_PASSED) {\n          // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n          //update--begin--autor:liusq-----date:20210316------for：未通过表单验证跳转tab问题------\n          this.activeKey = e.index == null ? this.activeKey : (e.paneKey?e.paneKey:this.refKeys[e.index])\n          //update--end--autor:liusq-----date:20210316------for：未通过表单验证跳转tab问题------\n        } else {\n          console.error(e)\n        }\n      })\n    },\n    //校验所有子表表单\n    validateSubForm(allValues){\n      return new Promise((resolve) => {\n        resolve(allValues)\n      })\n    },\n    /* --- throw --- */\n\n    /** not a function */\n    throwNotFunction(name) {\n      return `${name} 未定义或不是一个函数`\n    },\n\n    /** not a array */\n    throwNotArray(name) {\n      return `${name} 未定义或不是一个数组`\n    }\n\n  }\n}"]}]}