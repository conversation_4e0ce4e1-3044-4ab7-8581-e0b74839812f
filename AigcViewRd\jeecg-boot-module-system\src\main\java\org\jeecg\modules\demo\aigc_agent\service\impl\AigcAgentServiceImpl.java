package org.jeecg.modules.demo.aigc_agent.service.impl;

import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.mapper.AigcWorkflowMapper;
import org.jeecg.modules.demo.aigc_agent.mapper.AigcAgentMapper;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 智能体表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Service
public class AigcAgentServiceImpl extends ServiceImpl<AigcAgentMapper, AigcAgent> implements IAigcAgentService {

	@Autowired
	private AigcAgentMapper aigcAgentMapper;
	@Autowired
	private AigcWorkflowMapper aigcWorkflowMapper;
	
	@Override
	@Transactional
	public void saveMain(AigcAgent aigcAgent, List<AigcWorkflow> aigcWorkflowList) {
		aigcAgentMapper.insert(aigcAgent);
		if(aigcWorkflowList!=null && aigcWorkflowList.size()>0) {
			for(AigcWorkflow entity:aigcWorkflowList) {
				//外键设置
				entity.setAgentId(aigcAgent.getId());
				aigcWorkflowMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void updateMain(AigcAgent aigcAgent,List<AigcWorkflow> aigcWorkflowList) {
		aigcAgentMapper.updateById(aigcAgent);
		
		//1.先删除子表数据
		aigcWorkflowMapper.deleteByMainId(aigcAgent.getId());
		
		//2.子表数据重新插入
		if(aigcWorkflowList!=null && aigcWorkflowList.size()>0) {
			for(AigcWorkflow entity:aigcWorkflowList) {
				//外键设置
				entity.setAgentId(aigcAgent.getId());
				aigcWorkflowMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional
	public void delMain(String id) {
		aigcWorkflowMapper.deleteByMainId(id);
		aigcAgentMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			aigcWorkflowMapper.deleteByMainId(id.toString());
			aigcAgentMapper.deleteById(id);
		}
	}
	
}
