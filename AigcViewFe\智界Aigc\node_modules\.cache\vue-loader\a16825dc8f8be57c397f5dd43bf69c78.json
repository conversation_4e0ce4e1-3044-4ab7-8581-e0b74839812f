{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue", "mtime": 1753947183493}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./AigcPlubShopList.vue?vue&type=template&id=72112de5&scoped=true&\"\nimport script from \"./AigcPlubShopList.vue?vue&type=script&lang=js&\"\nexport * from \"./AigcPlubShopList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./AigcPlubShopList.vue?vue&type=style&index=0&id=72112de5&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72112de5\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\AigcView_zj\\\\AigcViewFe\\\\智界Aigc\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('72112de5')) {\n      api.createRecord('72112de5', component.options)\n    } else {\n      api.reload('72112de5', component.options)\n    }\n    module.hot.accept(\"./AigcPlubShopList.vue?vue&type=template&id=72112de5&scoped=true&\", function () {\n      api.rerender('72112de5', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/aigcview/plubshop/AigcPlubShopList.vue\"\nexport default component.exports"]}