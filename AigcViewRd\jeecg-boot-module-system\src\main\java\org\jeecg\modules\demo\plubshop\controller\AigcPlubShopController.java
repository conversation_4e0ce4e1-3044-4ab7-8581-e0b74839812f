package org.jeecg.modules.demo.plubshop.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.system.service.ISysUserService;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.demo.plubauthor.service.IAigcPlubAuthorService;
import org.jeecg.modules.demo.plubauthor.entity.AigcPlubAuthor;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import java.util.ArrayList;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.modules.jianyingpro.service.internal.JianyingProTosService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 插件商城
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Api(tags="插件商城")
@RestController
@RequestMapping("/plubshop/aigcPlubShop")
@Slf4j
public class AigcPlubShopController extends JeecgController<AigcPlubShop, IAigcPlubShopService> {
	@Autowired
	private IAigcPlubShopService aigcPlubShopService;

	@Autowired
	private IAigcPlubAuthorService plubAuthorService;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private ISysUserService sysUserService;

	@Autowired
	private IAicgUserProfileService userProfileService;

	@Autowired
	private CommonAPI commonAPI;

	@Autowired
	private JianyingProTosService tosService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcPlubShop
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "插件商城-分页列表查询")
	@ApiOperation(value="插件商城-分页列表查询", notes="插件商城-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcPlubShop aigcPlubShop,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcPlubShop> queryWrapper = QueryGenerator.initQueryWrapper(aigcPlubShop, req.getParameterMap());

		// 获取当前登录用户
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 检查用户角色，非admin用户只能查看自己创建的数据
		if (sysUser != null) {
			List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
			boolean isAdmin = userRoles != null && userRoles.contains("admin");

			if (!isAdmin) {
				// 非admin用户只能看到自己创建的数据
				queryWrapper.eq("create_by", sysUser.getUsername());
				log.info("🔒 非admin用户 {} 只能查看自己的数据", sysUser.getUsername());
			} else {
				log.info("🔓 admin用户 {} 可以查看所有数据", sysUser.getUsername());
			}
		}

		// 🎯 检查是否有前端排序参数，如果没有则添加默认排序
		Map<String, String[]> parameterMap = req.getParameterMap();
		boolean hasFrontendSort = parameterMap.containsKey("column") && parameterMap.containsKey("order");

		if (!hasFrontendSort) {
			// 只有在前端没有指定排序时才添加默认排序：按排序权重升序（数字小的在前），然后按更新时间降序
			queryWrapper.orderByAsc("sort_order").orderByDesc("update_time");
			log.info("🎯 应用默认排序：sort_order ASC, update_time DESC");
		} else {
			log.info("🎯 使用前端排序参数：column={}, order={}",
				parameterMap.get("column")[0], parameterMap.get("order")[0]);
		}

		Page<AigcPlubShop> page = new Page<AigcPlubShop>(pageNo, pageSize);
		IPage<AigcPlubShop> pageList = aigcPlubShopService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcPlubShop
	 * @return
	 */
	@AutoLog(value = "插件商城-添加")
	@ApiOperation(value="插件商城-添加", notes="插件商城-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcPlubShop aigcPlubShop) {
		aigcPlubShopService.save(aigcPlubShop);

		// 更新插件创作者的插件数
		if (aigcPlubShop.getPlubwrite() != null && !aigcPlubShop.getPlubwrite().trim().isEmpty()) {
			plubAuthorService.updateAuthorPluginCount(aigcPlubShop.getPlubwrite());
		}

		// 🔥 如果是组合插件，同步更新其他相同组合插件名的信息
		if (aigcPlubShop.getIsCombined() != null && aigcPlubShop.getIsCombined() == 1
			&& StringUtils.isNotBlank(aigcPlubShop.getCombinedName())) {

			syncCombinedPluginInfo(aigcPlubShop.getCombinedName(),
								  aigcPlubShop.getCombinedDescription(),
								  aigcPlubShop.getCombinedImage(),
								  aigcPlubShop.getPlubwrite(),
								  aigcPlubShop.getSortOrder(),
								  aigcPlubShop.getId());
		}

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcPlubShop
	 * @return
	 */
	@AutoLog(value = "插件商城-编辑")
	@ApiOperation(value="插件商城-编辑", notes="插件商城-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcPlubShop aigcPlubShop) {
		// 获取编辑前的插件信息，检查作者是否变更
		AigcPlubShop oldPlugin = aigcPlubShopService.getById(aigcPlubShop.getId());
		String oldAuthorId = oldPlugin != null ? oldPlugin.getPlubwrite() : null;
		String newAuthorId = aigcPlubShop.getPlubwrite();

		aigcPlubShopService.updateById(aigcPlubShop);

		// 如果作者发生变更，需要更新两个作者的插件数
		if (oldAuthorId != null && !oldAuthorId.equals(newAuthorId)) {
			// 更新原作者的插件数
			plubAuthorService.updateAuthorPluginCount(oldAuthorId);
		}

		// 更新新作者的插件数
		if (newAuthorId != null && !newAuthorId.trim().isEmpty()) {
			plubAuthorService.updateAuthorPluginCount(newAuthorId);
		}

		// 🔥 如果是组合插件，同步更新其他相同组合插件名的信息
		if (aigcPlubShop.getIsCombined() != null && aigcPlubShop.getIsCombined() == 1
			&& StringUtils.isNotBlank(aigcPlubShop.getCombinedName())) {

			syncCombinedPluginInfo(aigcPlubShop.getCombinedName(),
								  aigcPlubShop.getCombinedDescription(),
								  aigcPlubShop.getCombinedImage(),
								  aigcPlubShop.getPlubwrite(),
								  aigcPlubShop.getSortOrder(),
								  aigcPlubShop.getId());
		}

		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "插件商城-通过id删除")
	@ApiOperation(value="插件商城-通过id删除", notes="插件商城-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		// 获取删除前的插件信息，记录作者ID
		AigcPlubShop plugin = aigcPlubShopService.getById(id);
		String authorId = plugin != null ? plugin.getPlubwrite() : null;

		aigcPlubShopService.removeById(id);

		// 更新作者的插件数
		if (authorId != null && !authorId.trim().isEmpty()) {
			plubAuthorService.updateAuthorPluginCount(authorId);
		}

		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "插件商城-批量删除")
	@ApiOperation(value="插件商城-批量删除", notes="插件商城-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		// 获取要删除的插件信息，收集所有涉及的作者ID
		List<String> idList = Arrays.asList(ids.split(","));
		List<AigcPlubShop> plugins = aigcPlubShopService.listByIds(idList);
		Set<String> authorIds = plugins.stream()
			.map(AigcPlubShop::getPlubwrite)
			.filter(authorId -> authorId != null && !authorId.trim().isEmpty())
			.collect(Collectors.toSet());

		this.aigcPlubShopService.removeByIds(idList);

		// 更新所有涉及的作者的插件数
		for (String authorId : authorIds) {
			plubAuthorService.updateAuthorPluginCount(authorId);
		}

		return Result.OK("批量删除成功!");
	}

	/**
	 * 🔥 同步组合插件信息
	 * 当保存组合插件时，同步更新所有相同组合插件名的插件信息
	 */
	private void syncCombinedPluginInfo(String combinedName, String combinedDescription,
	                                   String combinedImage, String plubwrite,
	                                   Integer sortOrder, String excludeId) {
		try {
			if (StringUtils.isEmpty(combinedName)) {
				return;
			}

			QueryWrapper<AigcPlubShop> updateWrapper = new QueryWrapper<>();
			updateWrapper.eq("combined_name", combinedName)
						.eq("is_combined", 1)
						.ne("id", excludeId);

			// 获取需要更新的插件列表
			List<AigcPlubShop> pluginsToUpdate = aigcPlubShopService.list(updateWrapper);

			if (!pluginsToUpdate.isEmpty()) {
				// 批量更新
				for (AigcPlubShop plugin : pluginsToUpdate) {
					// 🔥 组合插件介绍：始终同步（包括空值，实现删除同步）
					plugin.setCombinedDescription(combinedDescription);

					// 🔥 组合插件图片：始终同步（包括空值，实现删除同步）
					plugin.setCombinedImage(combinedImage);

					// 🔥 插件创作者：只在有值时同步（避免误删除）
					if (StringUtils.isNotBlank(plubwrite)) {
						plugin.setPlubwrite(plubwrite);
					}

					// 🔥 排序权重：只在有有效值时同步
					if (sortOrder != null && sortOrder > 0) {
						plugin.setSortOrder(sortOrder);
					}
				}

				boolean success = aigcPlubShopService.updateBatchById(pluginsToUpdate);

				if (success) {
					log.info("🔄 同步更新了 {} 个相同组合插件名\"{}\"的信息", pluginsToUpdate.size(), combinedName);
				} else {
					log.error("❌ 同步更新组合插件信息失败");
				}
			} else {
				log.info("📝 没有找到需要同步的组合插件");
			}
		} catch (Exception e) {
			log.error("❌ 同步组合插件信息异常: {}", e.getMessage(), e);
		}
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "插件商城-通过id查询")
	@ApiOperation(value="插件商城-通过id查询", notes="插件商城-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcPlubShop aigcPlubShop = aigcPlubShopService.getById(id);
		if(aigcPlubShop==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcPlubShop);
	}

	/**
	 * 获取插件详情（包含创作者信息）
	 * 专为官网插件详情页面设计
	 *
	 * @param id 插件ID
	 * @return 插件详情和创作者信息
	 */
	@AutoLog(value = "插件商城-获取插件详情")
	@ApiOperation(value="获取插件详情", notes="获取插件详情，包含创作者信息")
	@GetMapping(value = "/getPluginDetail")
	public Result<?> getPluginDetail(@RequestParam(name="id",required=true) String id) {
		try {
			// 1. 获取插件基础信息
			AigcPlubShop plugin = aigcPlubShopService.getById(id);
			if (plugin == null) {
				return Result.error("插件不存在");
			}

			// 2. 构建返回数据，手动进行字典转换
			Map<String, Object> result = new HashMap<>();

			// 手动转换插件的适用场景字典
			Map<String, Object> pluginMap = new HashMap<>();
			pluginMap.put("id", plugin.getId());
			pluginMap.put("plubname", plugin.getPlubname());
			pluginMap.put("plubimg", plugin.getPlubimg());
			pluginMap.put("plubwrite", plugin.getPlubwrite());
			pluginMap.put("plubinfo", plugin.getPlubinfo());
			pluginMap.put("plubvideo", plugin.getPlubvideo());
			pluginMap.put("tutorialLink", plugin.getTutorialLink());
			// 移除使用次数和总收益字段
			// pluginMap.put("income", plugin.getIncome());
			// pluginMap.put("usernum", plugin.getUsernum());
			pluginMap.put("neednum", plugin.getNeednum());
			pluginMap.put("plubContent", plugin.getPlubContent());
			pluginMap.put("plubCategory", plugin.getPlubCategory());
			pluginMap.put("scenarios", plugin.getScenarios());
			pluginMap.put("status", plugin.getStatus());
			pluginMap.put("sortOrder", plugin.getSortOrder());
			pluginMap.put("pluginKey", plugin.getPluginKey());
			pluginMap.put("createTime", plugin.getCreateTime());
			pluginMap.put("updateTime", plugin.getUpdateTime()); // 添加更新时间

			// 🔥 添加组合插件相关字段
			pluginMap.put("isCombined", plugin.getIsCombined());
			pluginMap.put("combinedName", plugin.getCombinedName());
			pluginMap.put("combinedDescription", plugin.getCombinedDescription());
			pluginMap.put("combinedImage", plugin.getCombinedImage());

			// 手动转换适用场景字典
			if (plugin.getScenarios() != null) {
				String[] scenariosArray = plugin.getScenarios().split(",");
				StringBuilder scenariosTextBuilder = new StringBuilder();
				for (int i = 0; i < scenariosArray.length; i++) {
					String scenarioCode = scenariosArray[i].trim();
					String scenarioText = commonAPI.translateDict("plugin_scenarios", scenarioCode);
					if (i > 0) scenariosTextBuilder.append(",");
					scenariosTextBuilder.append(scenarioText != null ? scenarioText : scenarioCode);
				}
				pluginMap.put("scenarios_dictText", scenariosTextBuilder.toString());
			}

			result.put("plugin", pluginMap);

			// 3. 获取随机推荐插件（最多6个）
			try {
				// 先获取所有符合条件的插件
				QueryWrapper<AigcPlubShop> recommendQuery = new QueryWrapper<>();
				recommendQuery.eq("status", 1); // 只推荐已上架的插件
				recommendQuery.ne("id", id); // 排除当前插件

				List<AigcPlubShop> allRecommendList = aigcPlubShopService.list(recommendQuery);

				// Java代码随机排序并限制数量
				List<AigcPlubShop> recommendList = new ArrayList<>(allRecommendList);
				java.util.Collections.shuffle(recommendList); // 随机打乱

				// 限制最多6个
				int maxSize = Math.min(6, recommendList.size());
				recommendList = recommendList.subList(0, maxSize);

				// 转换推荐插件数据，包含组合插件相关字段
				List<Map<String, Object>> recommendations = new ArrayList<>();
				for (AigcPlubShop recommend : recommendList) {
					Map<String, Object> recommendMap = new HashMap<>();
					recommendMap.put("id", recommend.getId());
					recommendMap.put("plubname", recommend.getPlubname());
					recommendMap.put("plubimg", recommend.getPlubimg());
					recommendMap.put("plubinfo", recommend.getPlubinfo());
					// 移除使用次数和总收益字段
					// recommendMap.put("income", recommend.getIncome());
					// recommendMap.put("usernum", recommend.getUsernum());
					recommendMap.put("neednum", recommend.getNeednum()); // 添加所需点数字段
					recommendMap.put("plubCategory", recommend.getPlubCategory());
					recommendMap.put("status", recommend.getStatus()); // 添加状态字段
					recommendMap.put("createTime", recommend.getCreateTime());

					// 🔥 添加组合插件相关字段（与主接口保持一致）
					recommendMap.put("isCombined", recommend.getIsCombined());
					recommendMap.put("combinedName", recommend.getCombinedName());
					recommendMap.put("combinedDescription", recommend.getCombinedDescription());
					recommendMap.put("combinedImage", recommend.getCombinedImage());

					recommendations.add(recommendMap);
				}

				result.put("recommendations", recommendations);
				log.info("获取随机推荐插件成功，数量: {}", recommendations.size());
			} catch (Exception e) {
				log.warn("获取推荐插件失败: {}", e.getMessage(), e);
				result.put("recommendations", new ArrayList<>());
			}

			// 4. 获取创作者信息（手动进行字典转换）
			if (plugin.getPlubwrite() != null && !plugin.getPlubwrite().trim().isEmpty()) {
				try {
					AigcPlubAuthor author = plubAuthorService.getById(plugin.getPlubwrite());

					if (author != null) {
						// 手动进行字典转换
						Map<String, Object> authorMap = new HashMap<>();
						authorMap.put("id", author.getId());
						authorMap.put("authorname", author.getAuthorname());
						authorMap.put("title", author.getTitle());
						authorMap.put("expertise", author.getExpertise());
						authorMap.put("plubnum", author.getPlubnum());
						authorMap.put("createinfo", author.getCreateinfo());
						authorMap.put("createTime", author.getCreateTime());

						// 🔥 获取创作者头像（根据创建人从扩展表获取用户头像）
						if (author.getCreateBy() != null) {
							try {
								// 1. 先获取用户信息
								SysUser user = sysUserService.getUserByName(author.getCreateBy());
								if (user != null) {
									// 2. 根据用户ID获取扩展表中的头像
									AicgUserProfile userProfile = userProfileService.getByUserId(user.getId());
									if (userProfile != null && userProfile.getAvatar() != null) {
										// 🔧 [FIX] 使用TOS服务生成正确的头像URL（支持CDN优先）
										String avatarUrl = tosService.generateFileUrl(userProfile.getAvatar());
										authorMap.put("avatar", avatarUrl);
										log.debug("获取创作者头像成功: userId={}, avatarPath={}, avatarUrl={}",
											user.getId(), userProfile.getAvatar(), avatarUrl);
									} else {
										// 4. 没有头像时使用TOS默认头像
										String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
										authorMap.put("avatar", defaultAvatarUrl);
										log.debug("使用默认头像: userId={}, defaultUrl={}", user.getId(), defaultAvatarUrl);
									}
								} else {
									// 用户不存在时使用TOS默认头像
									String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
									authorMap.put("avatar", defaultAvatarUrl);
									log.debug("用户不存在，使用默认头像: createBy={}, defaultUrl={}", author.getCreateBy(), defaultAvatarUrl);
								}
							} catch (Exception e) {
								log.warn("获取创作者头像失败，创作者ID: {}", author.getId(), e);
								// 异常时使用TOS默认头像
								String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
								authorMap.put("avatar", defaultAvatarUrl);
							}
						} else {
							// 没有创建人信息时使用TOS默认头像
							String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
							authorMap.put("avatar", defaultAvatarUrl);
							log.debug("没有创建人信息，使用默认头像: authorId={}, defaultUrl={}", author.getId(), defaultAvatarUrl);
						}

						// 手动转换职位字典
						if (author.getTitle() != null) {
							String titleText = commonAPI.translateDict("author_title", author.getTitle());
							authorMap.put("title_dictText", titleText != null ? titleText : author.getTitle());
						}

						// 手动转换专业领域字典
						if (author.getExpertise() != null) {
							String[] expertiseArray = author.getExpertise().split(",");
							StringBuilder expertiseTextBuilder = new StringBuilder();
							for (int i = 0; i < expertiseArray.length; i++) {
								String expertiseCode = expertiseArray[i].trim();
								String expertiseText = commonAPI.translateDict("author_expertise", expertiseCode);
								if (i > 0) expertiseTextBuilder.append(",");
								expertiseTextBuilder.append(expertiseText != null ? expertiseText : expertiseCode);
							}
							authorMap.put("expertise_dictText", expertiseTextBuilder.toString());
						}

						result.put("author", authorMap);
					} else {
						result.put("author", null);
					}
				} catch (Exception e) {
					log.warn("获取创作者信息失败，插件ID: {}, 创作者ID: {}", id, plugin.getPlubwrite(), e);
					result.put("author", null);
				}
			} else {
				result.put("author", null);
			}

			// 注意：推荐插件已在上面的步骤3中处理，这里不需要重复处理

			return Result.OK(result);
		} catch (Exception e) {
			log.error("获取插件详情失败，插件ID: {}", id, e);
			return Result.error("获取插件详情失败");
		}
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aigcPlubShop
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AigcPlubShop aigcPlubShop) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户角色，非admin用户只能导出自己创建的数据
        if (sysUser != null) {
            List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
            boolean isAdmin = userRoles != null && userRoles.contains("admin");

            if (!isAdmin) {
                // 非admin用户只能导出自己创建的数据
                request.setAttribute("create_by", sysUser.getUsername());
                log.info("🔒 非admin用户 {} 只能导出自己的数据", sysUser.getUsername());
            } else {
                log.info("🔓 admin用户 {} 可以导出所有数据", sysUser.getUsername());
            }
        }

        return super.exportXls(request, aigcPlubShop, AigcPlubShop.class, "插件商城");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AigcPlubShop.class);
    }

	/**
	 * 检查插件标识唯一性
	 */
	@AutoLog(value = "插件商城-检查插件标识唯一性")
	@ApiOperation(value="检查插件标识唯一性", notes="检查插件标识唯一性")
	@GetMapping(value = "/checkPluginKey")
	public Result<?> checkPluginKey(@RequestParam String pluginKey,
	                               @RequestParam(required = false) String excludeId) {
		try {
			QueryWrapper<AigcPlubShop> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("plugin_key", pluginKey);

			// 编辑时排除当前记录
			if (StringUtils.isNotEmpty(excludeId)) {
				queryWrapper.ne("id", excludeId);
			}

			long count = aigcPlubShopService.count(queryWrapper);

			Map<String, Object> result = new HashMap<>();
			result.put("exists", count > 0);
			result.put("pluginKey", pluginKey);

			return Result.OK(result);
		} catch (Exception e) {
			log.error("检查插件标识唯一性失败", e);
			return Result.error("检查失败");
		}
	}

	/**
	 * 🔥 验证组合插件名权限并查询信息
	 */
	@AutoLog(value = "插件商城-验证组合插件名权限并查询信息")
	@ApiOperation(value="验证组合插件名权限并查询信息", notes="验证用户是否有权限使用该组合插件名，并返回现有信息")
	@GetMapping(value = "/validateAndQueryCombinedPlugin")
	public Result<?> validateAndQueryCombinedPlugin(@RequestParam String combinedName,
	                                               @RequestParam(required = false) String excludeId) {
		try {
			if (StringUtils.isEmpty(combinedName)) {
				return Result.error("组合插件名不能为空");
			}

			// 获取当前登录用户
			LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
			if (sysUser == null) {
				return Result.error("用户未登录");
			}

			// 检查用户角色
			List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
			boolean isAdmin = userRoles != null && userRoles.contains("admin");

			// 查询现有的组合插件
			QueryWrapper<AigcPlubShop> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("combined_name", combinedName)
					   .eq("is_combined", 1);

			// 编辑时排除当前记录
			if (StringUtils.isNotEmpty(excludeId)) {
				queryWrapper.ne("id", excludeId);
			}

			queryWrapper.orderByDesc("update_time").last("LIMIT 1");

			AigcPlubShop existingPlugin = aigcPlubShopService.getOne(queryWrapper);

			Map<String, Object> result = new HashMap<>();

			if (existingPlugin != null) {
				// 🔥 权限验证：非admin用户不能使用别人的组合插件名
				if (!isAdmin && !sysUser.getUsername().equals(existingPlugin.getCreateBy())) {
					result.put("hasPermission", false);
					result.put("message", "该组合插件名已被其他用户使用，请更换名称");
					result.put("foundExisting", false);
					log.warn("🚫 用户 {} 无权限使用组合插件: {}, 创建者: {}",
						sysUser.getUsername(), combinedName, existingPlugin.getCreateBy());
					return Result.OK(result);
				}

				// 有权限，返回现有信息
				result.put("hasPermission", true);
				result.put("foundExisting", true);
				result.put("combinedName", existingPlugin.getCombinedName());
				result.put("combinedDescription", existingPlugin.getCombinedDescription());
				result.put("combinedImage", existingPlugin.getCombinedImage());
				result.put("plubwrite", existingPlugin.getPlubwrite());
				result.put("sortOrder", existingPlugin.getSortOrder()); // 🔥 新增排序权重字段

				log.info("🔍 用户 {} 有权限使用组合插件: {}, 排序权重: {}",
					sysUser.getUsername(), combinedName, existingPlugin.getSortOrder());
			} else {
				// 没有找到现有的组合插件
				result.put("hasPermission", true);
				result.put("foundExisting", false);
				log.info("🔍 未找到组合插件: {}, 用户可以创建", combinedName);
			}

			return Result.OK(result);
		} catch (Exception e) {
			log.error("验证组合插件权限失败", e);
			return Result.error("验证失败: " + e.getMessage());
		}
	}

	/**
	 * 获取最大排序权重
	 */
	@AutoLog(value = "插件商城-获取最大排序权重")
	@ApiOperation(value="获取最大排序权重", notes="获取最大排序权重")
	@GetMapping(value = "/getMaxSortOrder")
	public Result<?> getMaxSortOrder() {
		try {
			QueryWrapper<AigcPlubShop> queryWrapper = new QueryWrapper<>();
			queryWrapper.orderByDesc("sort_order");
			queryWrapper.last("LIMIT 1");

			AigcPlubShop maxPlugin = aigcPlubShopService.getOne(queryWrapper);
			Integer maxSortOrder = maxPlugin != null ? maxPlugin.getSortOrder() : 0;

			return Result.OK(maxSortOrder);
		} catch (Exception e) {
			log.error("获取最大排序权重失败", e);
			return Result.error("获取失败");
		}
	}

	/**
	 * 检查排序权重冲突
	 */
	@AutoLog(value = "插件商城-检查排序权重冲突")
	@ApiOperation(value="检查排序权重冲突", notes="检查排序权重冲突")
	@GetMapping(value = "/checkSortOrderConflict")
	public Result<?> checkSortOrderConflict(@RequestParam Integer sortOrder,
	                                       @RequestParam(required = false) String excludeId) {
		try {
			log.info("🎯 checkSortOrderConflict - 检查权重: {}, 排除ID: {}", sortOrder, excludeId);

			QueryWrapper<AigcPlubShop> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("sort_order", sortOrder);

			// 编辑时排除当前记录
			if (StringUtils.isNotEmpty(excludeId)) {
				queryWrapper.ne("id", excludeId);
			}

			long count = aigcPlubShopService.count(queryWrapper);

			Map<String, Object> result = new HashMap<>();
			result.put("hasConflict", count > 0);
			result.put("sortOrder", sortOrder);

			log.info("🎯 checkSortOrderConflict - 冲突检查结果: {}", count > 0);

			return Result.OK(result);
		} catch (Exception e) {
			log.error("检查排序权重冲突失败", e);
			return Result.error("检查失败: " + e.getMessage());
		}
	}

	/**
	 * 调整排序权重冲突
	 */
	@AutoLog(value = "插件商城-调整排序权重冲突")
	@ApiOperation(value="调整排序权重冲突", notes="调整排序权重冲突")
	@PostMapping(value = "/adjustSortOrder")
	public Result<?> adjustSortOrder(@RequestBody Map<String, Object> params) {
		try {
			// 安全地获取targetOrder，处理字符串转整数
			Object targetOrderObj = params.get("targetOrder");
			Integer targetOrder = null;
			if (targetOrderObj instanceof Integer) {
				targetOrder = (Integer) targetOrderObj;
			} else if (targetOrderObj instanceof String) {
				try {
					targetOrder = Integer.parseInt((String) targetOrderObj);
				} catch (NumberFormatException e) {
					return Result.error("目标权重格式不正确");
				}
			}

			String excludeId = (String) params.get("excludeId");

			if (targetOrder == null) {
				return Result.error("目标权重不能为空");
			}

			log.info("🎯 adjustSortOrder - 目标权重: {}, 排除ID: {}", targetOrder, excludeId);

			// 将目标权重及后续权重都+1
			String updateSql = "UPDATE aigc_plub_shop SET sort_order = sort_order + 1 WHERE sort_order >= ?";
			List<Object> updateParams = new ArrayList<>();
			updateParams.add(targetOrder);

			// 编辑时排除当前记录
			if (StringUtils.isNotEmpty(excludeId)) {
				updateSql += " AND id != ?";
				updateParams.add(excludeId);
			}

			log.info("🎯 adjustSortOrder - 执行SQL: {}, 参数: {}", updateSql, updateParams);

			int updatedCount = jdbcTemplate.update(updateSql, updateParams.toArray());

			Map<String, Object> result = new HashMap<>();
			result.put("updatedCount", updatedCount);
			result.put("targetOrder", targetOrder);

			log.info("🎯 adjustSortOrder - 调整完成，影响行数: {}", updatedCount);

			return Result.OK(result);
		} catch (Exception e) {
			log.error("调整排序权重冲突失败", e);
			return Result.error("调整失败: " + e.getMessage());
		}
	}

	/**
	 * 🔥 获取插件列表（支持组合插件高级筛选）
	 * 支持组合插件的分类筛选和去重显示逻辑
	 */
	@AutoLog(value = "插件商城-高级筛选查询")
	@ApiOperation(value="插件商城-高级筛选查询", notes="插件商城-高级筛选查询，支持组合插件筛选")
	@GetMapping(value = "/getPluginsWithAdvancedFilter")
	public Result<?> getPluginsWithAdvancedFilter(
			@RequestParam(required = false) String category,
			@RequestParam(required = false) String keyword,
			@RequestParam(defaultValue = "1") Integer pageNo,
			@RequestParam(defaultValue = "12") Integer pageSize) {

		try {
			log.info("🔍 高级筛选查询 - 分类: {}, 关键词: {}, 页码: {}, 页大小: {}",
				category, keyword, pageNo, pageSize);

			List<AigcPlubShop> allPlugins = new ArrayList<>();

			// 🔥 情况1：筛选"组合插件"分类
			if ("combine".equals(category)) {
				allPlugins = getCombinedPluginRepresentatives(keyword);
				log.info("🔗 筛选组合插件，找到 {} 个代表", allPlugins.size());
			}
			// 🔥 情况2：筛选其他具体分类
			else if (StringUtils.isNotBlank(category)) {
				// 普通插件
				List<AigcPlubShop> normalPlugins = getNormalPluginsByCategory(category, keyword);

				// 组合插件（子插件包含该分类的）
				List<AigcPlubShop> combinedPlugins = getCombinedPluginsBySubCategory(category, keyword);

				allPlugins.addAll(normalPlugins);
				allPlugins.addAll(combinedPlugins);

				log.info("🎯 分类筛选 - 普通插件: {}, 组合插件: {}",
					normalPlugins.size(), combinedPlugins.size());
			}
			// 🔥 情况3：不筛选分类（显示所有）
			else {
				// 普通插件
				List<AigcPlubShop> normalPlugins = getAllNormalPlugins(keyword);

				// 组合插件代表
				List<AigcPlubShop> combinedPlugins = getCombinedPluginRepresentatives(keyword);

				allPlugins.addAll(normalPlugins);
				allPlugins.addAll(combinedPlugins);

				log.info("🌟 全部插件 - 普通插件: {}, 组合插件: {}",
					normalPlugins.size(), combinedPlugins.size());
			}

			// 按排序权重和创建时间排序
			allPlugins.sort((a, b) -> {
				// 先按排序权重排序（降序）
				int sortCompare = Integer.compare(
					b.getSortOrder() != null ? b.getSortOrder() : 0,
					a.getSortOrder() != null ? a.getSortOrder() : 0
				);
				if (sortCompare != 0) {
					return sortCompare;
				}
				// 再按创建时间排序（降序）
				return b.getCreateTime().compareTo(a.getCreateTime());
			});

			// 手动分页
			int total = allPlugins.size();
			int startIndex = (pageNo - 1) * pageSize;
			int endIndex = Math.min(startIndex + pageSize, total);

			List<AigcPlubShop> pageData = new ArrayList<>();
			if (startIndex < total) {
				pageData = allPlugins.subList(startIndex, endIndex);
			}

			// 构造分页结果
			Map<String, Object> result = new HashMap<>();
			result.put("records", pageData);
			result.put("total", total);
			result.put("size", pageSize);
			result.put("current", pageNo);
			result.put("pages", (int) Math.ceil((double) total / pageSize));

			log.info("✅ 高级筛选完成 - 总数: {}, 当前页: {}", total, pageData.size());

			return Result.OK(result);

		} catch (Exception e) {
			log.error("❌ 高级筛选查询失败", e);
			return Result.error("查询失败: " + e.getMessage());
		}
	}

	/**
	 * 🔥 获取组合插件代表（按combinedName去重）
	 */
	private List<AigcPlubShop> getCombinedPluginRepresentatives(String keyword) {
		QueryWrapper<AigcPlubShop> wrapper = new QueryWrapper<>();
		wrapper.eq("is_combined", 1)
			   .eq("status", 1);

		if (StringUtils.isNotBlank(keyword)) {
			wrapper.and(w -> w.like("combined_name", keyword)
							.or().like("combined_description", keyword));
		}

		wrapper.orderByAsc("sort_order").orderByDesc("update_time");

		List<AigcPlubShop> allCombined = aigcPlubShopService.list(wrapper);

		// 按combinedName去重，每组取第一个
		Map<String, AigcPlubShop> combinedMap = new java.util.LinkedHashMap<>();
		for (AigcPlubShop plugin : allCombined) {
			String combinedName = plugin.getCombinedName();
			if (StringUtils.isNotBlank(combinedName) && !combinedMap.containsKey(combinedName)) {
				combinedMap.put(combinedName, plugin);
			}
		}

		return new ArrayList<>(combinedMap.values());
	}

	/**
	 * 🔥 根据分类获取普通插件
	 */
	private List<AigcPlubShop> getNormalPluginsByCategory(String category, String keyword) {
		QueryWrapper<AigcPlubShop> wrapper = new QueryWrapper<>();
		wrapper.eq("is_combined", 0)
			   .eq("status", 1)
			   .eq("plub_category", category);

		if (StringUtils.isNotBlank(keyword)) {
			wrapper.and(w -> w.like("plubname", keyword)
							.or().like("plubinfo", keyword));
		}

		wrapper.orderByAsc("sort_order").orderByDesc("update_time");

		return aigcPlubShopService.list(wrapper);
	}

	/**
	 * 🔥 根据子插件分类获取组合插件
	 */
	private List<AigcPlubShop> getCombinedPluginsBySubCategory(String category, String keyword) {
		QueryWrapper<AigcPlubShop> wrapper = new QueryWrapper<>();
		wrapper.eq("is_combined", 1)
			   .eq("status", 1)
			   .eq("plub_category", category); // 子插件的分类匹配

		if (StringUtils.isNotBlank(keyword)) {
			wrapper.and(w -> w.like("combined_name", keyword)
							.or().like("combined_description", keyword));
		}

		wrapper.orderByAsc("sort_order").orderByDesc("update_time");

		List<AigcPlubShop> allCombined = aigcPlubShopService.list(wrapper);

		// 按combinedName去重
		Map<String, AigcPlubShop> combinedMap = new java.util.LinkedHashMap<>();
		for (AigcPlubShop plugin : allCombined) {
			String combinedName = plugin.getCombinedName();
			if (StringUtils.isNotBlank(combinedName) && !combinedMap.containsKey(combinedName)) {
				combinedMap.put(combinedName, plugin);
			}
		}

		return new ArrayList<>(combinedMap.values());
	}

	/**
	 * 🔥 获取所有普通插件
	 */
	private List<AigcPlubShop> getAllNormalPlugins(String keyword) {
		QueryWrapper<AigcPlubShop> wrapper = new QueryWrapper<>();
		wrapper.eq("is_combined", 0)
			   .eq("status", 1);

		if (StringUtils.isNotBlank(keyword)) {
			wrapper.and(w -> w.like("plubname", keyword)
							.or().like("plubinfo", keyword));
		}

		wrapper.orderByAsc("sort_order").orderByDesc("update_time");

		return aigcPlubShopService.list(wrapper);
	}

	/**
	 * 🔥 获取组合插件的子插件列表
	 */
	@AutoLog(value = "插件商城-获取组合插件子插件")
	@ApiOperation(value="插件商城-获取组合插件子插件", notes="根据组合插件名获取所有子插件")
	@GetMapping(value = "/getCombinedPluginChildren")
	public Result<?> getCombinedPluginChildren(@RequestParam String combinedName) {
		try {
			if (StringUtils.isBlank(combinedName)) {
				return Result.error("组合插件名不能为空");
			}

			log.info("🔍 获取组合插件子插件 - 组合插件名: {}", combinedName);

			QueryWrapper<AigcPlubShop> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("combined_name", combinedName)
						.eq("is_combined", 1)
						.eq("status", 1)
						.orderByAsc("sort_order").orderByDesc("update_time");

			List<AigcPlubShop> subPlugins = aigcPlubShopService.list(queryWrapper);

			log.info("✅ 找到 {} 个子插件", subPlugins.size());

			return Result.OK(subPlugins);

		} catch (Exception e) {
			log.error("❌ 获取组合插件子插件失败", e);
			return Result.error("获取失败: " + e.getMessage());
		}
	}

}
