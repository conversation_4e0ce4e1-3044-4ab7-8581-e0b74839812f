package org.jeecg.modules.demo.plubshop.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 插件商城
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aigc_plub_shop")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_plub_shop对象", description="插件商城")
public class AigcPlubShop implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**插件名称*/
	@Excel(name = "插件名称", width = 15)
    @ApiModelProperty(value = "插件名称")
    private java.lang.String plubname;
	/**图片*/
	@Excel(name = "图片", width = 15)
    @ApiModelProperty(value = "图片")
    private java.lang.String plubimg;
	/**插件创作者*/
	@Excel(name = "插件创作者", width = 15, dictTable = "aigc_plub_author", dicText = "authorname", dicCode = "id")
	@Dict(dictTable = "aigc_plub_author", dicText = "authorname", dicCode = "id")
    @ApiModelProperty(value = "插件创作者")
    private java.lang.String plubwrite;
	/**插件介绍*/
	@Excel(name = "插件介绍", width = 15)
    @ApiModelProperty(value = "插件介绍")
    private java.lang.String plubinfo;
	/**插件教程视频*/
	@Excel(name = "插件教程视频", width = 15)
    @ApiModelProperty(value = "插件教程视频")
    private java.lang.String plubvideo;
	/**插件教程链接*/
	@Excel(name = "插件教程链接", width = 15)
    @ApiModelProperty(value = "插件教程链接(外部链接)")
    @TableField("tutorial_link")
    private java.lang.String tutorialLink;
	/**收益金额*/
	@Excel(name = "收益金额", width = 15)
    @ApiModelProperty(value = "收益金额")
    private java.math.BigDecimal income;
	/**调用次数*/
	@Excel(name = "调用次数", width = 15)
    @ApiModelProperty(value = "调用次数")
    private java.lang.Integer usernum;
	/**需要金额*/
	@Excel(name = "需要金额", width = 15)
    @ApiModelProperty(value = "需要金额")
    private java.math.BigDecimal neednum;
	/**SVIP是否免费*/
	@Excel(name = "SVIP是否免费", width = 15, dicCode = "isTrue")
    @Dict(dicCode = "isTrue")
    @ApiModelProperty(value = "SVIP是否免费")
    @TableField("is_svip_free")
    private Integer isSvipFree;
	/**插件详细内容*/
    @ApiModelProperty(value = "插件详细内容(富文本)")
    @TableField("plub_content")
    private java.lang.String plubContent;
	/**插件分类*/
	@Excel(name = "插件分类", width = 15, dicCode = "plugin_category")
	@Dict(dicCode = "plugin_category")
    @ApiModelProperty(value = "插件分类")
    @TableField("plub_category")
    private java.lang.String plubCategory;
	/**适用场景*/
	@Excel(name = "适用场景", width = 20, dicCode = "plugin_scenarios")
	@Dict(dicCode = "plugin_scenarios")
    @ApiModelProperty(value = "适用场景（多选，逗号分隔）")
    private java.lang.String scenarios;
	/**插件状态*/
	@Excel(name = "插件状态", width = 15, dicCode = "plugin_status")
	@Dict(dicCode = "plugin_status")
    @ApiModelProperty(value = "插件状态(0=下架,1=上架,2=审核中,3=已拒绝)")
    private java.lang.Integer status;
	/**插件唯一标识*/
	@Excel(name = "插件唯一标识", width = 15)
    @ApiModelProperty(value = "插件唯一标识")
    @TableField("plugin_key")
    private java.lang.String pluginKey;
	/**是否组合插件*/
	@Excel(name = "是否组合插件", width = 15, dicCode = "isTrue")
	@Dict(dicCode = "isTrue")
    @ApiModelProperty(value = "是否组合插件：1-是，2-否")
    @TableField("is_combined")
    private java.lang.Integer isCombined;
	/**组合插件名*/
	@Excel(name = "组合插件名", width = 15)
    @ApiModelProperty(value = "组合插件名")
    @TableField("combined_name")
    private java.lang.String combinedName;
	/**组合插件介绍*/
	@Excel(name = "组合插件介绍", width = 20)
    @ApiModelProperty(value = "组合插件介绍")
    @TableField("combined_description")
    private java.lang.String combinedDescription;
	/**组合插件图片*/
	@Excel(name = "组合插件图片", width = 15)
    @ApiModelProperty(value = "组合插件图片")
    @TableField("combined_image")
    private java.lang.String combinedImage;
	/**排序权重*/
	@Excel(name = "排序权重", width = 15)
    @ApiModelProperty(value = "排序权重")
    @TableField("sort_order")
    private java.lang.Integer sortOrder;
}
