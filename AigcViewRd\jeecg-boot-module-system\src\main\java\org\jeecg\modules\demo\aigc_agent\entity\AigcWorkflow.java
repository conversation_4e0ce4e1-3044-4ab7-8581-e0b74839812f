package org.jeecg.modules.demo.aigc_agent.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 工作流表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@ApiModel(value="aigc_workflow对象", description="工作流表")
@Data
@TableName("aigc_workflow")
public class AigcWorkflow implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**智能体ID*/
    @ApiModelProperty(value = "智能体ID")
    private java.lang.String agentId;
	/**工作流ID*/
	@Excel(name = "工作流ID", width = 15)
    @ApiModelProperty(value = "工作流ID")
    private java.lang.String workflowId;
	/**工作流名称*/
	@Excel(name = "工作流名称", width = 15)
    @ApiModelProperty(value = "工作流名称")
    private java.lang.String workflowName;
	/**工作流描述*/
	@Excel(name = "工作流描述", width = 15)
    @ApiModelProperty(value = "工作流描述")
    private java.lang.String workflowDescription;
	/**工作流压缩包*/
	@Excel(name = "工作流压缩包", width = 15)
    @ApiModelProperty(value = "工作流压缩包")
    private java.lang.String workflowPackage;
}
