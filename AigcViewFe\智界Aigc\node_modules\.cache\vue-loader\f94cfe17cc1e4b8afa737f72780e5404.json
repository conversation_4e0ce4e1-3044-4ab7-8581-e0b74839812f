{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue?vue&type=template&id=17ce7a1e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue", "mtime": 1753944358622}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"combined-plugin-modal\",\n      attrs: {\n        visible: _vm.visible,\n        title: _vm.modalTitle,\n        width: \"1200px\",\n        footer: null,\n        \"z-index\": 2000,\n        \"mask-closable\": true,\n        \"destroy-on-close\": false,\n        \"get-container\": function() {\n          return _vm.document.body\n        }\n      },\n      on: { cancel: _vm.handleCancel }\n    },\n    [\n      _c(\"div\", { staticClass: \"combined-plugin-header\" }, [\n        _c(\"div\", { staticClass: \"combined-info\" }, [\n          _c(\"div\", { staticClass: \"combined-image\" }, [\n            _c(\"img\", {\n              attrs: {\n                src: _vm.getPluginImage(_vm.combinedPlugin),\n                alt: _vm.combinedPlugin && _vm.combinedPlugin.combinedName\n              },\n              on: { error: _vm.handleImageError }\n            })\n          ]),\n          _c(\"div\", { staticClass: \"combined-details\" }, [\n            _c(\"h2\", { staticClass: \"combined-title\" }, [\n              _vm._v(\n                _vm._s(_vm.combinedPlugin && _vm.combinedPlugin.combinedName)\n              )\n            ]),\n            _c(\"p\", { staticClass: \"combined-description\" }, [\n              _vm._v(\n                _vm._s(\n                  _vm.combinedPlugin && _vm.combinedPlugin.combinedDescription\n                )\n              )\n            ]),\n            _c(\"div\", { staticClass: \"combined-meta\" }, [\n              _c(\n                \"span\",\n                { staticClass: \"meta-item\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"user\" } }),\n                  _vm._v(\n                    \"\\n            创作者：\" +\n                      _vm._s(\n                        (_vm.combinedPlugin &&\n                          _vm.combinedPlugin.plubwrite_dictText) ||\n                          \"未知\"\n                      ) +\n                      \"\\n          \"\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"span\",\n                { staticClass: \"meta-item\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"tag\" } }),\n                  _vm._v(\n                    \"\\n            分类：\" +\n                      _vm._s(\n                        _vm.getCategoryText(\n                          _vm.combinedPlugin && _vm.combinedPlugin.plubCategory\n                        )\n                      ) +\n                      \"\\n          \"\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"span\",\n                { staticClass: \"meta-item\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"link\" } }),\n                  _vm._v(\"\\n            组合插件\\n          \")\n                ],\n                1\n              )\n            ])\n          ])\n        ])\n      ]),\n      _c(\"a-divider\", [\n        _c(\n          \"span\",\n          { staticClass: \"divider-text\" },\n          [\n            _c(\"a-icon\", { attrs: { type: \"appstore\" } }),\n            _vm._v(\n              \"\\n      包含的插件 (\" + _vm._s(_vm.subPlugins.length) + \")\\n    \"\n            )\n          ],\n          1\n        )\n      ]),\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.loading,\n              expression: \"loading\"\n            }\n          ],\n          staticClass: \"sub-plugins-container\"\n        },\n        [\n          _vm.loading\n            ? _c(\n                \"div\",\n                { staticClass: \"loading-container\" },\n                [\n                  _c(\"a-spin\", { attrs: { size: \"large\" } }, [\n                    _c(\"span\", { attrs: { slot: \"tip\" }, slot: \"tip\" }, [\n                      _vm._v(\"正在加载插件列表...\")\n                    ])\n                  ])\n                ],\n                1\n              )\n            : _vm.subPlugins.length === 0\n            ? _c(\n                \"div\",\n                { staticClass: \"empty-container\" },\n                [_c(\"a-empty\", { attrs: { description: \"暂无子插件\" } })],\n                1\n              )\n            : _c(\n                \"div\",\n                { staticClass: \"sub-plugins-grid\" },\n                _vm._l(_vm.subPlugins, function(subPlugin) {\n                  return _c(\n                    \"div\",\n                    {\n                      key: subPlugin.id,\n                      staticClass: \"sub-plugin-card\",\n                      on: {\n                        click: function($event) {\n                          return _vm.selectSubPlugin(subPlugin)\n                        }\n                      }\n                    },\n                    [\n                      _c(\"div\", { staticClass: \"sub-plugin-image\" }, [\n                        _c(\"img\", {\n                          attrs: {\n                            src: _vm.getPluginImage(subPlugin),\n                            alt: subPlugin.plubname\n                          },\n                          on: { error: _vm.handleSubImageError }\n                        }),\n                        _c(\n                          \"div\",\n                          { staticClass: \"plugin-overlay\" },\n                          [\n                            _c(\"a-icon\", {\n                              staticClass: \"view-icon\",\n                              attrs: { type: \"eye\" }\n                            })\n                          ],\n                          1\n                        )\n                      ]),\n                      _c(\"div\", { staticClass: \"sub-plugin-info\" }, [\n                        _c(\n                          \"h4\",\n                          {\n                            staticClass: \"sub-plugin-title\",\n                            attrs: { title: subPlugin.plubname }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(subPlugin.plubname) +\n                                \"\\n          \"\n                            )\n                          ]\n                        ),\n                        _c(\n                          \"p\",\n                          {\n                            staticClass: \"sub-plugin-description\",\n                            attrs: { title: subPlugin.plubinfo }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  _vm.truncateText(subPlugin.plubinfo, 60)\n                                ) +\n                                \"\\n          \"\n                            )\n                          ]\n                        ),\n                        _c(\"div\", { staticClass: \"sub-plugin-meta\" }, [\n                          _c(\"span\", { staticClass: \"category-tag\" }, [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(\n                                  _vm.getCategoryText(subPlugin.plubCategory)\n                                ) +\n                                \"\\n            \"\n                            )\n                          ]),\n                          _c(\"span\", { staticClass: \"price-tag\" }, [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.getSubPluginPriceText(subPlugin)) +\n                                \"\\n            \"\n                            )\n                          ])\n                        ])\n                      ]),\n                      _c(\n                        \"div\",\n                        { staticClass: \"sub-plugin-actions\" },\n                        [\n                          _c(\n                            \"a-button\",\n                            {\n                              attrs: { type: \"primary\", size: \"small\" },\n                              on: {\n                                click: function($event) {\n                                  $event.stopPropagation()\n                                  return _vm.selectSubPlugin(subPlugin)\n                                }\n                              }\n                            },\n                            [\n                              _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                              _vm._v(\"\\n            查看详情\\n          \")\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ]\n                  )\n                }),\n                0\n              )\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}