{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue", "mtime": 1753944973183}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./RelatedPlugins.vue?vue&type=template&id=a83192ae&scoped=true&\"\nimport script from \"./RelatedPlugins.vue?vue&type=script&lang=js&\"\nexport * from \"./RelatedPlugins.vue?vue&type=script&lang=js&\"\nimport style0 from \"./RelatedPlugins.vue?vue&type=style&index=0&id=a83192ae&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a83192ae\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\AigcView_zj\\\\AigcViewFe\\\\智界Aigc\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('a83192ae')) {\n      api.createRecord('a83192ae', component.options)\n    } else {\n      api.reload('a83192ae', component.options)\n    }\n    module.hot.accept(\"./RelatedPlugins.vue?vue&type=template&id=a83192ae&scoped=true&\", function () {\n      api.rerender('a83192ae', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/website/market/components/RelatedPlugins.vue\"\nexport default component.exports"]}