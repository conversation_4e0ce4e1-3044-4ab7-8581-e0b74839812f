{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=template&id=f8301f64&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754051311797}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"agent-detail-modal custom-modal\",\n      attrs: {\n        visible: _vm.visible,\n        width: 1200,\n        footer: null,\n        closable: false,\n        maskClosable: true,\n        bodyStyle: { padding: 0, borderRadius: \"16px\", overflow: \"hidden\" },\n        centered: true,\n        destroyOnClose: true\n      },\n      on: { cancel: _vm.handleClose }\n    },\n    [\n      _vm.loading\n        ? _c(\n            \"div\",\n            { staticClass: \"loading-container\" },\n            [\n              _c(\"a-spin\", { attrs: { size: \"large\", tip: \"加载中...\" } }, [\n                _c(\"div\", { staticClass: \"loading-placeholder\" })\n              ])\n            ],\n            1\n          )\n        : _c(\"div\", { staticClass: \"modal-content\" }, [\n            _c(\n              \"div\",\n              {\n                staticClass: \"custom-close-button\",\n                on: { click: _vm.handleClose }\n              },\n              [_c(\"a-icon\", { attrs: { type: \"close\" } })],\n              1\n            ),\n            _c(\"div\", { staticClass: \"modal-background\" }, [\n              _c(\"div\", { staticClass: \"bg-pattern\" }),\n              _c(\"div\", { staticClass: \"bg-gradient\" })\n            ]),\n            _c(\"div\", { staticClass: \"agent-info-section\" }, [\n              _c(\"div\", { staticClass: \"agent-header\" }, [\n                _c(\"div\", { staticClass: \"agent-avatar\" }, [\n                  _vm.agentDetail.agentAvatar\n                    ? _c(\"img\", {\n                        attrs: {\n                          src: _vm.agentDetail.agentAvatar,\n                          alt: _vm.agentDetail.agentName\n                        },\n                        on: { error: _vm.handleImageError }\n                      })\n                    : _c(\n                        \"div\",\n                        { staticClass: \"avatar-placeholder\" },\n                        [_c(\"a-icon\", { attrs: { type: \"robot\" } })],\n                        1\n                      )\n                ]),\n                _c(\"div\", { staticClass: \"agent-basic-info\" }, [\n                  _c(\"h2\", { staticClass: \"agent-name\" }, [\n                    _vm._v(_vm._s(_vm.agentDetail.agentName))\n                  ]),\n                  _c(\"p\", { staticClass: \"agent-description\" }, [\n                    _vm._v(_vm._s(_vm.agentDetail.agentDescription))\n                  ]),\n                  _c(\"div\", { staticClass: \"creator-info\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"creator-avatar\" },\n                      [\n                        _vm.agentDetail.creatorInfo &&\n                        _vm.agentDetail.creatorInfo.avatar\n                          ? _c(\"img\", {\n                              attrs: {\n                                src: _vm.agentDetail.creatorInfo.avatar,\n                                alt: _vm.agentDetail.creatorInfo.name\n                              },\n                              on: { error: _vm.handleCreatorAvatarError }\n                            })\n                          : _c(\"a-icon\", { attrs: { type: \"user\" } })\n                      ],\n                      1\n                    ),\n                    _c(\"div\", { staticClass: \"creator-details\" }, [\n                      _c(\"span\", { staticClass: \"creator-name\" }, [\n                        _vm._v(_vm._s(_vm.creatorName))\n                      ]),\n                      _c(\"span\", { staticClass: \"creator-type\" }, [\n                        _vm._v(_vm._s(_vm.authorTypeText))\n                      ])\n                    ])\n                  ])\n                ]),\n                _c(\"div\", { staticClass: \"price-section\" }, [\n                  _vm.agentDetail.isFree\n                    ? _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"free-price\" }, [\n                          _vm._v(\"免费\")\n                        ])\n                      ])\n                    : _vm.agentDetail.showDiscountPrice\n                    ? _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"discount-price\" }, [\n                          _vm._v(\"¥\" + _vm._s(_vm.finalPrice))\n                        ]),\n                        _c(\"span\", { staticClass: \"original-price\" }, [\n                          _vm._v(\n                            \"¥\" +\n                              _vm._s(\n                                _vm.agentDetail.price ||\n                                  _vm.agentDetail.originalPrice ||\n                                  0\n                              )\n                          )\n                        ])\n                      ])\n                    : _c(\"div\", { staticClass: \"price-container\" }, [\n                        _c(\"span\", { staticClass: \"current-price\" }, [\n                          _vm._v(\n                            \"¥\" +\n                              _vm._s(\n                                _vm.agentDetail.price ||\n                                  _vm.agentDetail.originalPrice ||\n                                  0\n                              )\n                          )\n                        ])\n                      ])\n                ])\n              ])\n            ]),\n            _vm.agentDetail.demoVideo\n              ? _c(\"div\", { staticClass: \"demo-video-section\" }, [\n                  _c(\n                    \"h3\",\n                    { staticClass: \"section-title\" },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"play-circle\" } }),\n                      _vm._v(\"\\n        演示视频\\n      \")\n                    ],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"video-container\" }, [\n                    !_vm.videoError\n                      ? _c(\"div\", { staticClass: \"video-wrapper\" }, [\n                          _c(\n                            \"video\",\n                            {\n                              ref: \"demoVideo\",\n                              staticClass: \"demo-video\",\n                              attrs: {\n                                src: _vm.agentDetail.demoVideo,\n                                autoplay: _vm.videoAutoplay,\n                                preload: \"metadata\"\n                              },\n                              domProps: { muted: _vm.videoMuted },\n                              on: {\n                                loadstart: _vm.handleVideoLoadStart,\n                                loadeddata: _vm.handleVideoLoaded,\n                                error: _vm.handleVideoError,\n                                play: _vm.handleVideoPlay,\n                                pause: _vm.handleVideoPause,\n                                timeupdate: _vm.handleVideoTimeUpdate,\n                                volumechange: _vm.handleVolumeChange,\n                                click: _vm.toggleVideoPlay\n                              }\n                            },\n                            [\n                              _vm._v(\n                                \"\\n            您的浏览器不支持视频播放\\n          \"\n                              )\n                            ]\n                          ),\n                          _c(\n                            \"div\",\n                            {\n                              directives: [\n                                {\n                                  name: \"show\",\n                                  rawName: \"v-show\",\n                                  value: _vm.showControls,\n                                  expression: \"showControls\"\n                                }\n                              ],\n                              staticClass: \"video-controls\"\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"controls-left\" },\n                                [\n                                  _c(\"a-button\", {\n                                    staticClass: \"control-btn play-btn\",\n                                    attrs: {\n                                      type: \"link\",\n                                      icon: _vm.videoPlaying\n                                        ? \"pause\"\n                                        : \"caret-right\"\n                                    },\n                                    on: { click: _vm.toggleVideoPlay }\n                                  }),\n                                  _c(\"span\", { staticClass: \"time-display\" }, [\n                                    _vm._v(\n                                      \"\\n                \" +\n                                        _vm._s(\n                                          _vm.formatTime(_vm.currentTime)\n                                        ) +\n                                        \" / \" +\n                                        _vm._s(_vm.formatTime(_vm.duration)) +\n                                        \"\\n              \"\n                                    )\n                                  ])\n                                ],\n                                1\n                              ),\n                              _c(\"div\", { staticClass: \"controls-center\" }, [\n                                _c(\n                                  \"div\",\n                                  {\n                                    staticClass: \"progress-container\",\n                                    on: { click: _vm.handleProgressClick }\n                                  },\n                                  [\n                                    _c(\"div\", { staticClass: \"progress-bar\" }, [\n                                      _c(\"div\", {\n                                        staticClass: \"progress-filled\",\n                                        style: {\n                                          width: _vm.progressPercent + \"%\"\n                                        }\n                                      }),\n                                      _c(\"div\", {\n                                        staticClass: \"progress-thumb\",\n                                        style: {\n                                          left: _vm.progressPercent + \"%\"\n                                        }\n                                      })\n                                    ])\n                                  ]\n                                )\n                              ]),\n                              _c(\n                                \"div\",\n                                { staticClass: \"controls-right\" },\n                                [\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"volume-control\",\n                                      on: {\n                                        mouseenter: function($event) {\n                                          _vm.showVolumeSlider = true\n                                        },\n                                        mouseleave: function($event) {\n                                          _vm.showVolumeSlider = false\n                                        }\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-button\", {\n                                        staticClass: \"control-btn volume-btn\",\n                                        attrs: {\n                                          type: \"link\",\n                                          icon: _vm.videoMuted\n                                            ? \"sound\"\n                                            : \"sound-filled\"\n                                        },\n                                        on: { click: _vm.toggleMute }\n                                      }),\n                                      _c(\n                                        \"div\",\n                                        {\n                                          directives: [\n                                            {\n                                              name: \"show\",\n                                              rawName: \"v-show\",\n                                              value: _vm.showVolumeSlider,\n                                              expression: \"showVolumeSlider\"\n                                            }\n                                          ],\n                                          staticClass: \"volume-slider\"\n                                        },\n                                        [\n                                          _c(\"a-slider\", {\n                                            staticClass: \"volume-range\",\n                                            attrs: {\n                                              min: 0,\n                                              max: 100,\n                                              step: 1,\n                                              vertical: \"\",\n                                              \"tip-formatter\": null\n                                            },\n                                            on: {\n                                              change:\n                                                _vm.handleVolumeSliderChange\n                                            },\n                                            model: {\n                                              value: _vm.videoVolume,\n                                              callback: function($$v) {\n                                                _vm.videoVolume = $$v\n                                              },\n                                              expression: \"videoVolume\"\n                                            }\n                                          })\n                                        ],\n                                        1\n                                      )\n                                    ],\n                                    1\n                                  ),\n                                  _c(\"a-button\", {\n                                    staticClass: \"control-btn fullscreen-btn\",\n                                    attrs: { type: \"link\", icon: \"fullscreen\" },\n                                    on: { click: _vm.toggleFullscreen }\n                                  })\n                                ],\n                                1\n                              )\n                            ]\n                          ),\n                          _c(\n                            \"div\",\n                            {\n                              directives: [\n                                {\n                                  name: \"show\",\n                                  rawName: \"v-show\",\n                                  value: _vm.videoLoading,\n                                  expression: \"videoLoading\"\n                                }\n                              ],\n                              staticClass: \"video-loading\"\n                            },\n                            [\n                              _c(\n                                \"a-spin\",\n                                { attrs: { size: \"large\" } },\n                                [\n                                  _c(\"a-icon\", {\n                                    staticStyle: { \"font-size\": \"24px\" },\n                                    attrs: {\n                                      slot: \"indicator\",\n                                      type: \"loading\",\n                                      spin: \"\"\n                                    },\n                                    slot: \"indicator\"\n                                  })\n                                ],\n                                1\n                              ),\n                              _c(\"p\", [_vm._v(\"视频加载中...\")])\n                            ],\n                            1\n                          )\n                        ])\n                      : _vm._e(),\n                    _vm.videoError\n                      ? _c(\"div\", { staticClass: \"video-error-placeholder\" }, [\n                          _c(\n                            \"div\",\n                            { staticClass: \"error-content\" },\n                            [\n                              _c(\"a-icon\", {\n                                staticClass: \"error-icon\",\n                                attrs: { type: \"exclamation-circle\" }\n                              }),\n                              _c(\"h4\", [_vm._v(\"视频加载失败\")]),\n                              _c(\"p\", [_vm._v(\"抱歉，演示视频暂时无法播放\")]),\n                              _c(\n                                \"a-button\",\n                                {\n                                  attrs: { type: \"primary\", ghost: \"\" },\n                                  on: { click: _vm.retryVideoLoad }\n                                },\n                                [\n                                  _c(\"a-icon\", { attrs: { type: \"reload\" } }),\n                                  _vm._v(\n                                    \"\\n              重新加载\\n            \"\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ])\n                      : _vm._e()\n                  ])\n                ])\n              : _vm._e(),\n            _c(\"div\", { staticClass: \"workflow-section\" }, [\n              _c(\"h3\", { staticClass: \"section-title\" }, [\n                _vm._v(\"\\n        工作流列表\\n        \"),\n                _c(\"span\", { staticClass: \"workflow-count\" }, [\n                  _vm._v(\"(\" + _vm._s(_vm.workflowList.length) + \"个)\")\n                ])\n              ]),\n              _vm.workflowLoading\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-loading\" },\n                    [_c(\"a-spin\", { attrs: { tip: \"加载工作流中...\" } })],\n                    1\n                  )\n                : _vm.workflowList.length > 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-list\" },\n                    _vm._l(_vm.workflowList, function(workflow, index) {\n                      return _c(\n                        \"div\",\n                        { key: workflow.id, staticClass: \"workflow-item\" },\n                        [\n                          _c(\"div\", { staticClass: \"workflow-info\" }, [\n                            _c(\"div\", { staticClass: \"workflow-sequence\" }, [\n                              _vm._v(_vm._s(index + 1))\n                            ]),\n                            _c(\n                              \"div\",\n                              { staticClass: \"workflow-avatar\" },\n                              [\n                                workflow.agentAvatar ||\n                                _vm.agentDetail.agentAvatar\n                                  ? _c(\"img\", {\n                                      attrs: {\n                                        src:\n                                          workflow.agentAvatar ||\n                                          _vm.agentDetail.agentAvatar,\n                                        alt: workflow.workflowName\n                                      },\n                                      on: {\n                                        error: _vm.handleWorkflowImageError\n                                      }\n                                    })\n                                  : _c(\"a-icon\", { attrs: { type: \"setting\" } })\n                              ],\n                              1\n                            ),\n                            _c(\"div\", { staticClass: \"workflow-details\" }, [\n                              _c(\"h4\", { staticClass: \"workflow-name\" }, [\n                                _vm._v(_vm._s(workflow.workflowName))\n                              ]),\n                              _c(\"p\", { staticClass: \"workflow-description\" }, [\n                                _vm._v(_vm._s(workflow.workflowDescription))\n                              ])\n                            ])\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"workflow-actions\" },\n                            [\n                              !_vm.isPurchased\n                                ? _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: { type: \"default\", disabled: \"\" },\n                                      on: { click: _vm.handleDownloadTip }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { type: \"download\" }\n                                      }),\n                                      _vm._v(\n                                        \"\\n              请先购买\\n            \"\n                                      )\n                                    ],\n                                    1\n                                  )\n                                : _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: {\n                                        type: \"primary\",\n                                        loading:\n                                          _vm.downloadLoading[workflow.id]\n                                      },\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.handleWorkflowDownload(\n                                            workflow\n                                          )\n                                        }\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { type: \"download\" }\n                                      }),\n                                      _vm._v(\n                                        \"\\n              下载\\n            \"\n                                      )\n                                    ],\n                                    1\n                                  )\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    }),\n                    0\n                  )\n                : _c(\n                    \"div\",\n                    { staticClass: \"workflow-empty\" },\n                    [_c(\"a-empty\", { attrs: { description: \"暂无工作流\" } })],\n                    1\n                  )\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"action-buttons modern-actions\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"close-btn modern-btn-secondary\",\n                    on: { click: _vm.handleClose }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"close\" } }),\n                    _vm._v(\"\\n        关闭\\n      \")\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"primary-actions\" },\n                  [\n                    _vm.isPurchased\n                      ? _c(\n                          \"a-button\",\n                          {\n                            staticClass: \"detail-btn modern-btn-outline\",\n                            attrs: { type: \"default\" },\n                            on: { click: _vm.handleViewDetail }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                            _vm._v(\"\\n          查看详情\\n        \")\n                          ],\n                          1\n                        )\n                      : _c(\n                          \"a-button\",\n                          {\n                            staticClass:\n                              \"detail-btn modern-btn-outline disabled\",\n                            attrs: { type: \"default\", disabled: \"\" }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                            _vm._v(\"\\n          查看详情\\n        \")\n                          ],\n                          1\n                        ),\n                    !_vm.isPurchased\n                      ? _c(\n                          \"a-button\",\n                          {\n                            staticClass: \"purchase-btn modern-btn-primary\",\n                            attrs: {\n                              type: \"primary\",\n                              loading: _vm.purchaseLoading\n                            },\n                            on: { click: _vm.handlePurchase }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"shopping-cart\" } }),\n                            _vm._v(\"\\n          立即购买\\n        \")\n                          ],\n                          1\n                        )\n                      : _vm._e(),\n                    _vm.agentDetail.experienceLink\n                      ? _c(\n                          \"a-button\",\n                          {\n                            staticClass: \"experience-btn modern-btn-outline\",\n                            attrs: { type: \"default\" },\n                            on: { click: _vm.handleExperience }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"play-circle\" } }),\n                            _vm._v(\"\\n          体验智能体\\n        \")\n                          ],\n                          1\n                        )\n                      : _c(\n                          \"a-button\",\n                          {\n                            staticClass:\n                              \"experience-btn modern-btn-outline disabled\",\n                            attrs: { type: \"default\", disabled: \"\" }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"play-circle\" } }),\n                            _vm._v(\"\\n          暂无体验\\n        \")\n                          ],\n                          1\n                        )\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ])\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}