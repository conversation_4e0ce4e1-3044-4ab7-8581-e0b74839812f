package org.jeecg.modules.demo.aigc_agent.mapper;

import java.util.List;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 工作流表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
public interface AigcWorkflowMapper extends BaseMapper<AigcWorkflow> {

	public boolean deleteByMainId(@Param("mainId") String mainId);
    
	public List<AigcWorkflow> selectByMainId(@Param("mainId") String mainId);
}
