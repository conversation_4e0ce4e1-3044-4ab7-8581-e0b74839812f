{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue?vue&type=style&index=0&id=a4b5014e&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue", "mtime": 1753945012309}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.plugin-detail-page {\n  /* WebsitePage组件已经提供了基本布局，这里只需要设置背景色 */\n  background-color: #f5f5f5;\n}\n\n.loading-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.loading-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.loading-skeleton {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.skeleton-header {\n  margin-bottom: 24px;\n}\n\n.skeleton-breadcrumb {\n  height: 20px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  width: 300px;\n}\n\n.skeleton-plugin-info {\n  display: flex;\n  gap: 32px;\n}\n\n.skeleton-image {\n  width: 300px;\n  height: 200px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n  flex-shrink: 0;\n}\n\n.skeleton-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.skeleton-title {\n  height: 32px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  width: 60%;\n}\n\n.skeleton-description {\n  height: 20px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  width: 80%;\n}\n\n.skeleton-meta {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.skeleton-tag {\n  height: 24px;\n  width: 80px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 12px;\n}\n\n.skeleton-tabs {\n  margin-top: 24px;\n}\n\n.skeleton-tab-bar {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 24px;\n  border-bottom: 2px solid #f0f0f0;\n  padding-bottom: 12px;\n}\n\n.skeleton-tab {\n  height: 40px;\n  width: 120px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n}\n\n.skeleton-tab-content {\n  height: 300px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n.plugin-detail-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.navigation-bar {\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n  padding: 16px 24px;\n  border-radius: 16px;\n  margin-bottom: 24px;\n  border: 1px solid rgba(59, 130, 246, 0.15);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: #475569;\n  backdrop-filter: blur(20px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  flex: 1;\n}\n\n.navigation-bar::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);\n  opacity: 0.6;\n}\n\n.back-button {\n  color: #3b82f6 !important;\n  background: rgba(59, 130, 246, 0.1) !important;\n  border: 1px solid rgba(59, 130, 246, 0.2) !important;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 44px;\n  height: 44px;\n}\n\n.back-button:hover {\n  background: rgba(59, 130, 246, 0.15) !important;\n  border-color: rgba(59, 130, 246, 0.4) !important;\n  color: #2563eb !important;\n  transform: translateX(-3px) scale(1.05);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n}\n\n.back-button .anticon {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.breadcrumb-path {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  flex-wrap: wrap;\n}\n\n.path-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  color: #64748b;\n  cursor: pointer;\n  padding: 6px 12px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.path-item:hover {\n  color: #3b82f6;\n  background: rgba(59, 130, 246, 0.08);\n  transform: translateY(-1px);\n}\n\n.path-item .anticon {\n  font-size: 12px;\n}\n\n.path-separator {\n  color: #cbd5e1;\n  font-size: 12px;\n  margin: 0 4px;\n}\n\n.path-current {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 15px;\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  background: rgba(59, 130, 246, 0.1);\n  padding: 6px 12px;\n  border-radius: 8px;\n  border: 1px solid rgba(59, 130, 246, 0.2);\n}\n\n.nav-right {\n  flex-shrink: 0;\n}\n\n.share-button {\n  color: #64748b !important;\n  background: rgba(255, 255, 255, 0.8) !important;\n  border: 1px solid rgba(203, 213, 225, 0.6) !important;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  padding: 8px 16px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n}\n\n.share-button:hover {\n  color: #3b82f6 !important;\n  background: rgba(59, 130, 246, 0.08) !important;\n  border-color: rgba(59, 130, 246, 0.2) !important;\n  transform: translateY(-2px) scale(1.02);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.share-button .anticon {\n  font-size: 14px;\n}\n\n.plugin-header {\n  background: white;\n  border-radius: 12px;\n  padding: 32px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.plugin-header-content {\n  display: flex;\n  gap: 32px;\n}\n\n.plugin-image {\n  flex-shrink: 0;\n}\n\n.plugin-image img {\n  width: 300px;\n  height: 200px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e8e8e8;\n}\n\n.plugin-info {\n  flex: 1;\n}\n\n.plugin-title {\n  font-size: 32px;\n  font-weight: bold;\n  margin: 0 0 16px 0;\n  color: #1a1a1a;\n}\n\n.plugin-description {\n  font-size: 16px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 24px;\n}\n\n.plugin-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.meta-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24px;\n  align-items: center;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 14px;\n  color: #666;\n  min-width: 200px;\n}\n\n.meta-icon {\n  color: #1890ff;\n  font-size: 16px;\n}\n\n.meta-label {\n  color: #666;\n  font-weight: 500;\n}\n\n.meta-value {\n  color: #333;\n  font-weight: 600;\n}\n\n.price-item {\n  background: linear-gradient(165deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-weight: bold;\n}\n\n.price-item .meta-icon,\n.price-item .meta-label,\n.price-item .meta-value {\n  color: white;\n}\n\n.price-icon {\n  color: #ffd700 !important;\n}\n\n.price-value {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.tutorial-hint {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1890ff;\n  margin-left: 12px;\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\n  padding: 8px 16px;\n  border-radius: 16px;\n  border: 1px solid #91d5ff;\n  display: inline-flex;\n  align-items: center;\n  animation: pulse-hint 2s ease-in-out infinite;\n  white-space: nowrap;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  user-select: none;\n  min-height: 32px;\n}\n\n.tutorial-hint:hover {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  color: white;\n  transform: translateY(-3px) scale(1.08);\n  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);\n  border-color: #1890ff;\n  font-weight: 700;\n}\n\n.tutorial-hint:active {\n  transform: translateY(-1px) scale(1.02);\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);\n}\n\n@keyframes pulse-hint {\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);\n  }\n}\n\n/* 自定义Tab导航样式 */\n.plugin-tabs-container {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.plugin-tabs-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);\n  background-size: 300% 100%;\n  animation: gradient-flow 3s ease infinite;\n}\n\n@keyframes gradient-flow {\n  0%, 100% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n}\n\n.custom-tabs-nav {\n  margin-bottom: 32px;\n}\n\n.tabs-nav-wrapper {\n  display: flex;\n  align-items: center;\n  background: rgba(248, 250, 252, 0.8);\n  border-radius: 50px;\n  padding: 8px;\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);\n  gap: 8px;\n}\n\n.tab-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 18px 32px;\n  font-size: 16px;\n  font-weight: 600;\n  border-radius: 50px;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  border: none;\n  background: transparent;\n  color: #64748b;\n  white-space: nowrap;\n  user-select: none;\n  z-index: 10;\n  \n}\n\n.tab-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));\n  border-radius: 50px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  transform: scale(0.8);\n}\n\n.tab-item:hover {\n  color: #3b82f6;\n  transform: translateY(-2px) scale(1.02);\n}\n\n.tab-item:hover::before {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.tab-item.active {\n  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  transform: translateY(-3px) scale(1.05);\n  box-shadow:\n    0 8px 32px rgba(59, 130, 246, 0.3),\n    0 0 0 1px rgba(255, 255, 255, 0.2) inset;\n  position: relative;\n  z-index: 2;\n  top: 3px;\n}\n\n.tab-item.active::before {\n  opacity: 0;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  background: linear-gradient(165deg, #3b82f6, #8b5cf6);\n  border-radius: 50px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(12px);\n  animation: pulse-glow 2s ease-in-out infinite alternate;\n}\n\n@keyframes pulse-glow {\n  0% { opacity: 0.3; transform: scale(1); }\n  100% { opacity: 0.6; transform: scale(1.05); }\n}\n\n.tab-item.active:hover {\n  color: white;\n  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-3px) scale(1.05);\n}\n\n.tab-item i {\n  font-size: 18px;\n  transition: all 0.3s ease;\n}\n\n.tab-item:hover i {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.tab-item.active i {\n  transform: scale(1.15) rotate(0deg);\n  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));\n}\n\n.custom-tabs-content {\n  position: relative;\n}\n\n.tab-pane {\n  animation: fadeInUp 0.5s ease;\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.plugin-tabs {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.plugin-tabs::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);\n  background-size: 300% 100%;\n  animation: gradient-flow 3s ease infinite;\n}\n\n@keyframes gradient-flow {\n  0%, 100% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n}\n\n.plugin-tabs .ant-tabs-bar {\n  border-bottom: none !important;\n  margin-bottom: 32px !important;\n  background: transparent !important;\n  padding: 0 !important;\n  position: relative !important;\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的tab导航容器 */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,\n.modern-tabs-container .ant-tabs-nav-container {\n  position: relative !important;\n  background: rgba(248, 250, 252, 0.8) !important;\n  border-radius: 50px !important;\n  padding: 8px !important;\n  backdrop-filter: blur(10px) !important;\n  border: 2px solid rgba(59, 130, 246, 0.2) !important;\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1) !important;\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的tab按钮 */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,\n.modern-tabs-container .ant-tabs-tab {\n  padding: 18px 32px !important;\n  font-size: 16px !important;\n  font-weight: 600 !important;\n  border-radius: 50px !important;\n  margin: 0 4px !important;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  position: relative !important;\n  overflow: hidden !important;\n  border: none !important;\n  background: transparent !important;\n  color: #64748b !important;\n  white-space: nowrap !important;\n}\n\n.plugin-tabs .ant-tabs-tab::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));\n  border-radius: 50px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  transform: scale(0.8);\n}\n\n.plugin-tabs .ant-tabs-tab:hover {\n  color: #3b82f6;\n  transform: translateY(-2px) scale(1.02);\n}\n\n.plugin-tabs .ant-tabs-tab:hover::before {\n  opacity: 1;\n  transform: scale(1);\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的激活tab */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,\n.modern-tabs-container .ant-tabs-tab-active {\n  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%) !important;\n  color: white !important;\n  transform: translateY(-3px) scale(1.05) !important;\n  box-shadow:\n    0 8px 32px rgba(59, 130, 246, 0.3),\n    0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;\n  position: relative !important;\n  z-index: 2 !important;\n  border-radius: 50px !important;\n}\n\n.plugin-tabs .ant-tabs-tab-active::before {\n  opacity: 0;\n}\n\n.plugin-tabs .ant-tabs-tab-active::after {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  background: linear-gradient(165deg, #3b82f6, #8b5cf6);\n  border-radius: 50px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(12px);\n  animation: pulse-glow 2s ease-in-out infinite alternate;\n}\n\n@keyframes pulse-glow {\n  0% { opacity: 0.3; transform: scale(1); }\n  100% { opacity: 0.6; transform: scale(1.05); }\n}\n\n.plugin-tabs .ant-tabs-tab-active:hover {\n  color: white;\n  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-3px) scale(1.05);\n}\n\n.plugin-tabs .ant-tabs-ink-bar {\n  display: none;\n}\n\n.plugin-tabs .ant-tabs-tab .anticon {\n  margin-right: 10px;\n  font-size: 18px;\n  transition: all 0.3s ease;\n}\n\n.plugin-tabs .ant-tabs-tab:hover .anticon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.plugin-tabs .ant-tabs-tab-active .anticon {\n  transform: scale(1.15) rotate(0deg);\n  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));\n}\n\n/* 添加流畅的切换动画 */\n.plugin-tabs .ant-tabs-content {\n  transition: all 0.3s ease;\n}\n\n.plugin-tabs .ant-tabs-tabpane {\n  animation: fadeInUp 0.5s ease;\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.tab-content {\n  min-height: 400px;\n  padding: 24px 0;\n}\n\n.rich-content {\n  line-height: 1.8;\n  color: #333;\n}\n\n.empty-content {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.author-section,\n.recommendations-section {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 60vh;\n  background-color: #f5f5f5;\n  padding: 40px 20px;\n}\n\n.error-actions {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.error-actions .ant-btn {\n  min-width: 120px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .plugin-detail-content {\n    max-width: 1000px;\n  }\n\n  .plugin-header-content {\n    gap: 24px;\n  }\n\n  .plugin-image img {\n    width: 280px;\n    height: 180px;\n  }\n}\n\n@media (max-width: 768px) {\n  .plugin-detail-content {\n    padding: 16px;\n    max-width: 100%;\n  }\n\n  .navigation-bar {\n    padding: 12px 16px;\n    margin-bottom: 16px;\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n\n  .nav-left {\n    gap: 12px;\n  }\n\n  .breadcrumb-path {\n    font-size: 12px;\n    gap: 6px;\n  }\n\n  .path-current {\n    max-width: 150px;\n    font-size: 14px;\n  }\n\n  .nav-right {\n    align-self: center;\n  }\n\n  .plugin-header {\n    padding: 20px;\n    margin-bottom: 16px;\n  }\n\n  .plugin-header-content {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n\n  .plugin-image {\n    align-self: center;\n  }\n\n  .plugin-image img {\n    width: 100%;\n    max-width: 280px;\n    height: 180px;\n  }\n\n  .plugin-title {\n    font-size: 24px;\n    text-align: center;\n  }\n\n  .plugin-description {\n    text-align: center;\n    font-size: 15px;\n  }\n\n  .meta-row {\n    flex-direction: column;\n    gap: 12px;\n    align-items: center;\n  }\n\n  .meta-item {\n    min-width: auto;\n    width: auto;\n    justify-content: center;\n  }\n\n  .price-item {\n    justify-content: center;\n    text-align: center;\n    width: fit-content;\n  }\n\n  .plugin-tabs {\n    padding: 16px;\n    margin-bottom: 16px;\n  }\n\n  .plugin-tabs .ant-tabs-tab {\n    padding: 8px 12px;\n    font-size: 14px;\n    margin-right: 4px;\n  }\n\n  .tab-content {\n    min-height: 300px;\n    padding: 16px 0;\n  }\n\n  .author-section,\n  .recommendations-section {\n    margin-bottom: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .plugin-detail-content {\n    padding: 12px;\n  }\n\n  .navigation-bar {\n    padding: 8px 12px;\n    border-radius: 8px;\n  }\n\n  .back-button {\n    width: 36px;\n    height: 36px;\n  }\n\n  .breadcrumb-path {\n    font-size: 11px;\n    gap: 4px;\n  }\n\n  .path-current {\n    max-width: 120px;\n    font-size: 12px;\n  }\n\n  .nav-right .ant-btn-group .ant-btn {\n    font-size: 12px;\n    padding: 4px 8px;\n  }\n\n  .plugin-header {\n    padding: 16px;\n  }\n\n  .plugin-title {\n    font-size: 20px;\n  }\n\n  .plugin-description {\n    font-size: 14px;\n  }\n\n  .meta-row {\n    gap: 8px;\n  }\n\n  .meta-item {\n    font-size: 12px;\n  }\n\n  .plugin-tabs {\n    padding: 12px;\n  }\n\n  .plugin-tabs .ant-tabs-tab {\n    padding: 6px 8px;\n    font-size: 12px;\n    margin-right: 2px;\n  }\n\n  .tab-content {\n    padding: 12px 0;\n  }\n}\n", {"version": 3, "sources": ["PluginDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmwBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "PluginDetail.vue", "sourceRoot": "src/views/website/market", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"plugin-detail-page\">\n    <!-- 页面加载状态 -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <div class=\"loading-content\">\n        <a-spin size=\"large\" tip=\"加载插件详情中...\">\n          <div class=\"loading-skeleton\">\n            <!-- 骨架屏效果 -->\n            <div class=\"skeleton-header\">\n              <div class=\"skeleton-breadcrumb\"></div>\n              <div class=\"skeleton-plugin-info\">\n                <div class=\"skeleton-image\"></div>\n                <div class=\"skeleton-details\">\n                  <div class=\"skeleton-title\"></div>\n                  <div class=\"skeleton-description\"></div>\n                  <div class=\"skeleton-meta\">\n                    <div class=\"skeleton-tag\"></div>\n                    <div class=\"skeleton-tag\"></div>\n                    <div class=\"skeleton-tag\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"skeleton-tabs\">\n              <div class=\"skeleton-tab-bar\">\n                <div class=\"skeleton-tab\"></div>\n                <div class=\"skeleton-tab\"></div>\n                <div class=\"skeleton-tab\"></div>\n                <div class=\"skeleton-tab\"></div>\n              </div>\n              <div class=\"skeleton-tab-content\"></div>\n            </div>\n          </div>\n        </a-spin>\n      </div>\n    </div>\n\n    <!-- 页面内容 -->\n    <div v-else-if=\"pluginDetail\" class=\"plugin-detail-content\">\n      <!-- 优化后的导航栏 -->\n      <div class=\"navigation-bar\">\n        <!-- 左侧导航信息 -->\n        <div class=\"nav-left\">\n          <!-- 返回按钮 -->\n          <a-button\n            type=\"text\"\n            size=\"large\"\n            @click=\"goBack\"\n            class=\"back-button\">\n            <a-icon type=\"arrow-left\" />\n          </a-button>\n\n          <!-- 面包屑路径 -->\n          <div class=\"breadcrumb-path\">\n            <span class=\"path-item\" @click=\"$router.push('/')\">\n              <a-icon type=\"home\" />\n              首页\n            </span>\n            <a-icon type=\"right\" class=\"path-separator\" />\n            <span class=\"path-item\" @click=\"goBackToMarket\">\n              <a-icon type=\"shop\" />\n              商城\n            </span>\n            <a-icon type=\"right\" class=\"path-separator\" />\n            <span class=\"path-current\">\n              {{ pluginDetail.plubname || '插件详情' }}\n            </span>\n          </div>\n        </div>\n\n        <!-- 右侧操作按钮 -->\n        <div class=\"nav-right\">\n          <a-button @click=\"sharePlugin\" class=\"share-button\">\n            <a-icon type=\"share-alt\" />\n            分享\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 插件头部信息区域 -->\n      <div class=\"plugin-header\">\n        <div class=\"plugin-header-content\">\n          <!-- 插件封面图 -->\n          <div class=\"plugin-image\">\n            <img\n              :src=\"getPluginImage(pluginDetail)\"\n              :alt=\"pluginDetail.plubname\"\n              @error=\"handleImageError\"\n            />\n          </div>\n\n          <!-- 插件基础信息 -->\n          <div class=\"plugin-info\">\n            <h1 class=\"plugin-title\">{{ pluginDetail.plubname }}</h1>\n            <p class=\"plugin-description\">{{ pluginDetail.plubinfo }}</p>\n            \n            <div class=\"plugin-meta\">\n              <!-- 第一行：分类和状态 -->\n              <div class=\"meta-row\">\n                <div class=\"meta-item\">\n                  <a-tag :color=\"getCategoryColor(pluginDetail.plubCategory)\" size=\"large\">\n                    <a-icon type=\"appstore\" />\n                    {{ categoryText }}\n                  </a-tag>\n                </div>\n                <div class=\"meta-item\">\n                  <a-tag :color=\"getStatusColor(pluginDetail.status)\" size=\"large\">\n                    <a-icon type=\"check-circle\" />\n                    {{ getStatusText(pluginDetail.status) }}\n                  </a-tag>\n                </div>\n              </div>\n\n              <!-- 第二行：创作者和价格 -->\n              <div class=\"meta-row\">\n                <div class=\"meta-item\">\n                  <a-icon type=\"user\" class=\"meta-icon\" />\n                  <span class=\"meta-label\">创作者：</span>\n                  <span class=\"meta-value\">{{ authorInfo.authorname || '未知' }}</span>\n                </div>\n                <div class=\"meta-item price-item\">\n                  <a-icon type=\"dollar\" class=\"meta-icon price-icon\" />\n                  <span class=\"meta-label\">价格：</span>\n                  <span class=\"meta-value price-value\">{{ getDetailPriceText() }}</span>\n                </div>\n                <div v-if=\"hasTutorial\" class=\"meta-item tutorial-item\">\n                  <span class=\"tutorial-hint\" @click=\"goToTutorial\">\n                    本插件有教程视频，详细请点此观看\n                  </span>\n                </div>\n              </div>\n\n              <!-- 第三行：时间信息 -->\n              <div class=\"meta-row\">\n                <div class=\"meta-item\">\n                  <a-icon type=\"calendar\" class=\"meta-icon\" />\n                  <span class=\"meta-label\">发布时间：</span>\n                  <span class=\"meta-value\">{{ formatDate(pluginDetail.createTime) }}</span>\n                </div>\n                <div class=\"meta-item\">\n                  <a-icon type=\"sync\" class=\"meta-icon\" />\n                  <span class=\"meta-label\">更新时间：</span>\n                  <span class=\"meta-value\">{{ formatDate(pluginDetail.updateTime) }}</span>\n                </div>\n              </div>\n\n              <!-- 统计信息已移除 -->\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Tab切换内容区域 -->\n      <div class=\"plugin-tabs-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs-nav\">\n          <div class=\"tabs-nav-wrapper\">\n            <div\n              v-for=\"(tab, index) in tabList\"\n              :key=\"tab.key\"\n              :class=\"['tab-item', { 'active': activeTab === tab.key }]\"\n              @click=\"handleTabClick(tab.key)\"\n            >\n              <i :class=\"tab.icon\"></i>\n              <span>{{ tab.label }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Tab内容区域 -->\n        <div class=\"custom-tabs-content\">\n          <div v-show=\"activeTab === 'intro'\" class=\"tab-pane\">\n            <plugin-introduction\n              :content=\"pluginDetail.plubContent\"\n              :info=\"pluginDetail.plubinfo\"\n              :plugin-name=\"pluginDetail.plubname\"\n              :plugin-detail=\"pluginDetail\"\n            />\n          </div>\n          <div v-show=\"activeTab === 'tutorial'\" class=\"tab-pane\">\n            <plugin-tutorial\n              :tutorial-link=\"pluginDetail.tutorialLink\"\n              :video-file=\"pluginDetail.plubvideo\"\n              :plugin-name=\"pluginDetail.plubname\"\n              :detailed-content=\"pluginDetail.plubContent\"\n            />\n          </div>\n          <div v-show=\"activeTab === 'features'\" class=\"tab-pane\">\n            <plugin-features\n              :plugin-detail=\"pluginDetail\"\n              :category=\"pluginDetail.plubCategory\"\n            />\n          </div>\n          <div v-show=\"activeTab === 'tech'\" class=\"tab-pane\">\n            <plugin-technical\n              :plugin-detail=\"pluginDetail\"\n              :plugin-key=\"pluginDetail.pluginKey\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- 创作者信息区域 -->\n      <div class=\"author-section\">\n        <author-info\n          :author=\"authorInfo\"\n          :plugin-count=\"authorPluginCount\"\n        />\n      </div>\n\n      <!-- 相关推荐区域 -->\n      <div class=\"recommendations-section\">\n        <related-plugins\n          :recommendations=\"recommendations\"\n          :current-category=\"pluginDetail.plubCategory\"\n          :current-plugin-id=\"pluginDetail.id\"\n        />\n      </div>\n    </div>\n\n    <!-- 错误状态 -->\n    <div v-else class=\"error-container\">\n      <a-result\n        :status=\"errorStatus\"\n        :title=\"errorTitle\"\n        :sub-title=\"errorMessage\"\n      >\n        <template #extra>\n          <div class=\"error-actions\">\n            <a-button type=\"primary\" @click=\"retryLoad\" v-if=\"canRetry\">\n              <a-icon type=\"reload\" />\n              重新加载\n            </a-button>\n            <a-button @click=\"$router.push('/market')\">\n              <a-icon type=\"shop\" />\n              返回商城\n            </a-button>\n            <a-button type=\"dashed\" @click=\"goHome\">\n              <a-icon type=\"home\" />\n              返回首页\n            </a-button>\n          </div>\n        </template>\n      </a-result>\n    </div>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport { getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils'\nimport marketApi from '@/api/market'\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport PluginIntroduction from './components/PluginIntroduction.vue'\nimport PluginTutorial from './components/PluginTutorial.vue'\nimport PluginFeatures from './components/PluginFeatures.vue'\nimport PluginTechnical from './components/PluginTechnical.vue'\nimport AuthorInfo from './components/AuthorInfo.vue'\nimport RelatedPlugins from './components/RelatedPlugins.vue'\n\nexport default {\n  name: 'PluginDetail',\n\n  components: {\n    WebsitePage,\n    PluginIntroduction,\n    PluginTutorial,\n    PluginFeatures,\n    PluginTechnical,\n    AuthorInfo,\n    RelatedPlugins\n  },\n\n  data() {\n    return {\n      loading: true,\n      pluginDetail: null,\n      authorInfo: {},\n      authorPluginCount: 0,\n      recommendations: [],\n      activeTab: 'tutorial',\n      // Tab列表\n      tabList: [\n        { key: 'tutorial', label: '使用教程', icon: 'anticon anticon-play-circle' },\n        { key: 'intro', label: '插件介绍', icon: 'anticon anticon-file-text' },\n        { key: 'features', label: '功能特点', icon: 'anticon anticon-star' },\n        { key: 'tech', label: '技术说明', icon: 'anticon anticon-code' }\n      ],\n      // 错误处理相关\n      errorStatus: '404',\n      errorTitle: '插件不存在',\n      errorMessage: '抱歉，您访问的插件不存在或已被删除',\n      canRetry: false,\n      retryCount: 0,\n      maxRetries: 3,\n      // 调试模式\n      debugMode: process.env.NODE_ENV === 'development',\n      // 🔥 使用TOS统一管理的默认插件图片\n      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    }\n  },\n\n  computed: {\n    pluginId() {\n      return this.$route.params.id;\n    },\n\n    // 判断是否有教程视频\n    hasTutorial() {\n      if (!this.pluginDetail) return false;\n\n      // 检查是否有教程链接或视频文件\n      const hasLink = this.pluginDetail.tutorialLink && this.pluginDetail.tutorialLink.trim() !== '';\n      const hasVideo = this.pluginDetail.plubvideo && this.pluginDetail.plubvideo.trim() !== '';\n\n      return hasLink || hasVideo;\n    },\n\n    categoryText() {\n      // 🔥 使用全局分类字典服务获取分类文本\n      if (!this.pluginDetail || !this.pluginDetail.plubCategory) {\n        return '未知分类';\n      }\n      return this.$categoryService.getCategoryText(this.pluginDetail.plubCategory);\n    }\n  },\n\n  async created() {\n    // 添加页面访问统计\n    this.trackPageView();\n\n    // 🔥 初始化分类数据\n    await this.$categoryService.getCategories();\n\n    await this.loadPluginDetail();\n  },\n\n  // 监听路由变化\n  watch: {\n    '$route'(to, from) {\n      // 如果是同一个组件但插件ID变了，重新加载数据\n      if (to.params.id !== from.params.id) {\n        this.loadPluginDetail();\n      }\n    }\n  },\n\n  methods: {\n    async loadPluginDetail() {\n      try {\n        this.loading = true;\n        this.canRetry = false;\n\n        // 验证插件ID\n        if (!this.pluginId) {\n          this.handleError('参数错误', '插件ID不能为空', '400', false);\n          return;\n        }\n\n        // 调用插件详情API\n        const response = await marketApi.getPluginDetail(this.pluginId);\n\n        if (response.success && response.result) {\n          this.pluginDetail = response.result.plugin;\n          this.authorInfo = response.result.author || {};\n          this.authorPluginCount = this.authorInfo.plubnum || 0;\n          // 重置错误状态\n          this.retryCount = 0;\n\n          if (this.debugMode) {\n            console.log('插件详情加载成功:', this.pluginDetail);\n            console.log('创作者信息:', this.authorInfo);\n            console.log('推荐插件:', this.recommendations);\n            console.log('推荐插件数量:', this.recommendations.length);\n            console.log('创作者职位原始值:', this.authorInfo.title);\n            console.log('创作者职位字典文本:', this.authorInfo.title_dictText);\n            console.log('创作者专业领域原始值:', this.authorInfo.expertise);\n            console.log('创作者专业领域字典文本:', this.authorInfo.expertise_dictText);\n            console.log('插件图片字段 plubimg:', this.pluginDetail.plubimg);\n            console.log('最终图片URL:', this.getPluginImage(this.pluginDetail));\n            console.log('默认图片URL:', this.defaultPluginImage);\n            console.log('环境变量 VUE_APP_IMG_BASE_URL:', process.env.VUE_APP_IMG_BASE_URL);\n          }\n\n          // 更新页面标题\n          if (this.pluginDetail.plubname) {\n            document.title = `${this.pluginDetail.plubname} - 插件详情 - 智界AIGC`;\n          }\n\n          // 获取相关推荐（根据分类）\n          await this.fetchRecommendations();\n        } else {\n          const errorCode = response.code || 500;\n          const errorMsg = response.message || '获取插件详情失败';\n\n          if (errorCode === 404) {\n            this.handleError('插件不存在', '抱歉，您访问的插件不存在或已被删除', '404', false);\n          } else if (errorCode >= 500) {\n            this.handleError('服务器错误', '服务器暂时无法响应，请稍后重试', '500', true);\n          } else {\n            this.handleError('加载失败', errorMsg, 'warning', true);\n          }\n        }\n\n      } catch (error) {\n        console.error('加载插件详情失败:', error);\n\n        if (error.message && error.message.includes('Network Error')) {\n          this.handleError('网络错误', '网络连接失败，请检查网络后重试', '500', true);\n        } else if (error.message && error.message.includes('timeout')) {\n          this.handleError('请求超时', '请求超时，请稍后重试', '500', true);\n        } else {\n          this.handleError('加载失败', '加载插件详情时发生未知错误', 'warning', true);\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 处理错误状态\n    handleError(title, message, status, canRetry) {\n      this.pluginDetail = null;\n      this.errorTitle = title;\n      this.errorMessage = message;\n      this.errorStatus = status;\n      this.canRetry = canRetry && this.retryCount < this.maxRetries;\n\n      // 显示错误提示\n      if (canRetry && this.retryCount < this.maxRetries) {\n        this.$message.error(`${message}，可以尝试重新加载`);\n      } else {\n        this.$message.error(message);\n      }\n    },\n\n    // 重试加载\n    async retryLoad() {\n      if (this.retryCount >= this.maxRetries) {\n        this.$message.warning('重试次数已达上限');\n        return;\n      }\n\n      this.retryCount++;\n      this.$message.info(`正在重试加载... (${this.retryCount}/${this.maxRetries})`);\n\n      await this.loadPluginDetail();\n    },\n\n    // 返回首页\n    goHome() {\n      this.$router.push('/');\n    },\n\n\n\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage(plugin) {\n      return getPluginImageUrl(plugin, this.defaultPluginImage)\n    },\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      console.log('图片加载失败，使用默认图片:', event.target.src);\n      console.log('原始图片路径 plubimg:', this.pluginDetail && this.pluginDetail.plubimg);\n      console.log('切换到默认图片:', this.defaultPluginImage);\n      event.target.src = this.defaultPluginImage;\n    },\n\n    formatDate(dateStr) {\n      if (!dateStr) return '未知';\n      return new Date(dateStr).toLocaleDateString('zh-CN');\n    },\n\n    formatNumber(num) {\n      if (!num) return '0';\n      if (num >= 10000) {\n        return (num / 10000).toFixed(1) + '万';\n      }\n      return num.toLocaleString();\n    },\n\n    getCategoryColor(category) {\n      // 🔥 使用全局分类字典服务获取分类颜色\n      return this.$categoryService.getCategoryColor(category);\n    },\n\n    getStatusColor(status) {\n      const colors = {\n        0: 'red',     // 下架\n        1: 'green',   // 上架\n        2: 'orange',  // 审核中\n        3: 'red'      // 已拒绝\n      };\n      return colors[status] || 'default';\n    },\n\n    getStatusText(status) {\n      const texts = {\n        0: '已下架',\n        1: '已上架',\n        2: '审核中',\n        3: '已拒绝'\n      };\n      return texts[status] || '未知状态';\n    },\n\n    // Tab切换事件\n    onTabChange(activeKey) {\n      console.log('切换到Tab:', activeKey);\n      // 可以在这里添加Tab切换的统计或其他逻辑\n    },\n\n    // 自定义Tab切换方法\n    handleTabClick(tabKey) {\n      this.activeTab = tabKey;\n      console.log('切换到Tab:', tabKey);\n    },\n\n    // 跳转到使用教程tab\n    goToTutorial() {\n      this.activeTab = 'tutorial';\n      console.log('点击教程提示，跳转到使用教程tab');\n\n      // 滚动到tab区域\n      // this.$nextTick(() => {\n      //   const tabsElement = document.querySelector('.plugin-tabs-container');\n      //   if (tabsElement) {\n      //     tabsElement.scrollIntoView({\n      //       behavior: 'smooth',\n      //       block: 'start'\n      //     });\n      //   }\n      // });\n    },\n\n    // 🔥 获取详情页价格显示文本\n    getDetailPriceText() {\n      const price = this.pluginDetail.neednum\n      const isSvipFree = this.pluginDetail.isSvipFree === 1 || this.pluginDetail.isSvipFree === '1'\n\n      if (!price || price <= 0) {\n        return '免费'\n      }\n\n      if (isSvipFree) {\n        return `SVIP免费，低至¥${price}/次`\n      } else {\n        return `低至¥${price}/次`\n      }\n    },\n\n    // 获取相关推荐（根据分类）\n    async fetchRecommendations() {\n      try {\n        if (!this.pluginDetail || !this.pluginDetail.plubCategory) {\n          console.log('没有插件分类信息，跳过推荐获取');\n          this.recommendations = [];\n          return;\n        }\n\n        // 🔥 扩大推荐范围：不限制分类，获取更多插件\n        const params = {\n          // category: this.pluginDetail.plubCategory, // 移除分类限制，获取所有分类\n          pageSize: 50, // 增加获取数量，确保有足够的插件进行筛选\n          status: 1 // 只获取已上架的插件\n        };\n\n        const response = await marketApi.getPluginList(params);\n\n        if (this.debugMode) {\n          console.log('相关推荐API响应:', response);\n          console.log('推荐参数:', params);\n        }\n\n        if (response.success && response.result && response.result.records) {\n          // 🔥 使用与Market页面相同的组合插件处理逻辑\n          const allPlugins = response.result.records\n\n          // 1. 先过滤掉当前插件\n          const filteredPlugins = allPlugins.filter(plugin => plugin.id !== this.pluginDetail.id)\n\n          // 2. 使用统一的组合插件处理逻辑（普通插件 + 组合插件代表）\n          const processedPlugins = processPluginsWithCombined(filteredPlugins)\n\n          // 3. 智能推荐：优先同分类，然后是其他分类\n          const currentCategory = this.pluginDetail.plubCategory\n          const sameCategoryPlugins = processedPlugins.filter(plugin => plugin.plubCategory === currentCategory)\n          const otherCategoryPlugins = processedPlugins.filter(plugin => plugin.plubCategory !== currentCategory)\n\n          // 4. 合并推荐：优先同分类，不足时补充其他分类\n          let recommendations = [\n            ...sameCategoryPlugins.sort(() => Math.random() - 0.5), // 同分类随机排序\n            ...otherCategoryPlugins.sort(() => Math.random() - 0.5)  // 其他分类随机排序\n          ].slice(0, 3); // 🎯 最多显示3个推荐\n\n          this.recommendations = recommendations;\n\n          if (this.debugMode) {\n            console.log('推荐插件:', this.recommendations);\n            console.log('推荐插件数量:', this.recommendations.length);\n            console.log('当前插件分类:', this.pluginDetail.plubCategory);\n          }\n        } else {\n          console.log('获取推荐失败或无数据');\n          this.recommendations = [];\n        }\n      } catch (error) {\n        console.error('获取相关推荐失败:', error);\n        this.recommendations = [];\n      }\n    },\n\n    // 返回上一页\n    goBack() {\n      // 检查是否有历史记录且上一页是商城页面\n      if (window.history.length > 1 && document.referrer.includes('/market')) {\n        this.$router.go(-1);\n      } else {\n        // 返回商城，保持筛选状态\n        this.goBackToMarket();\n      }\n    },\n\n    // 返回商城页面，保持筛选状态\n    goBackToMarket() {\n      // 从localStorage获取商城的筛选状态\n      const marketState = this.getMarketState();\n\n      if (marketState && Object.keys(marketState).length > 0) {\n        // 如果有保存的筛选状态，构建查询参数\n        const query = {};\n\n        if (marketState.category) {\n          query.category = marketState.category;\n        }\n        if (marketState.search) {\n          query.search = marketState.search;\n        }\n        if (marketState.priceRange) {\n          query.priceRange = marketState.priceRange;\n        }\n        if (marketState.sortBy) {\n          query.sortBy = marketState.sortBy;\n        }\n\n        this.$router.push({ path: '/market', query });\n      } else {\n        // 没有筛选状态，直接返回商城\n        this.$router.push('/market');\n      }\n    },\n\n    // 获取商城筛选状态\n    getMarketState() {\n      try {\n        const stateStr = localStorage.getItem('market_filter_state');\n        return stateStr ? JSON.parse(stateStr) : {};\n      } catch (error) {\n        console.warn('获取商城筛选状态失败:', error);\n        return {};\n      }\n    },\n\n    // 分享插件 - 复制链接\n    sharePlugin() {\n      const url = window.location.href;\n\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(url).then(() => {\n          this.$message.success('插件详情链接已复制到剪贴板');\n        }).catch(() => {\n          this.fallbackCopyToClipboard(url);\n        });\n      } else {\n        this.fallbackCopyToClipboard(url);\n      }\n    },\n\n    // 兜底复制方法\n    fallbackCopyToClipboard(text) {\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      document.body.appendChild(textArea);\n      textArea.select();\n      try {\n        document.execCommand('copy');\n        this.$message.success('插件详情链接已复制到剪贴板');\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制链接');\n      }\n      document.body.removeChild(textArea);\n    },\n\n    // 页面访问统计\n    trackPageView() {\n      try {\n        const trackData = {\n          page: 'plugin-detail',\n          pluginId: this.pluginId,\n          timestamp: new Date().toISOString(),\n          userAgent: navigator.userAgent,\n          referrer: document.referrer\n        };\n\n        console.log('页面访问统计:', trackData);\n\n        // 这里可以发送统计数据到后端\n        // analytics.track('page_view', trackData);\n      } catch (error) {\n        console.warn('统计数据发送失败:', error);\n      }\n    },\n\n    // 性能监控\n    trackPerformance() {\n      try {\n        if (window.performance && window.performance.timing) {\n          const timing = window.performance.timing;\n          const loadTime = timing.loadEventEnd - timing.navigationStart;\n\n          console.log('页面加载性能:', {\n            loadTime: loadTime + 'ms',\n            domReady: (timing.domContentLoadedEventEnd - timing.navigationStart) + 'ms',\n            firstPaint: (timing.responseStart - timing.navigationStart) + 'ms'\n          });\n        }\n      } catch (error) {\n        console.warn('性能监控失败:', error);\n      }\n    },\n\n    // 错误上报\n    reportError(error, context = '') {\n      try {\n        const errorData = {\n          message: error.message,\n          stack: error.stack,\n          context,\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          timestamp: new Date().toISOString()\n        };\n\n        console.error('错误上报:', errorData);\n\n        // 这里可以发送错误数据到后端\n        // errorReporting.report(errorData);\n      } catch (reportError) {\n        console.warn('错误上报失败:', reportError);\n      }\n    }\n  },\n\n  // 组件销毁时的清理\n  beforeDestroy() {\n    // 清理定时器、事件监听器等\n    if (this.retryTimer) {\n      clearTimeout(this.retryTimer);\n    }\n  },\n\n  // 错误边界\n  errorCaptured(err, instance, info) {\n    this.reportError(err, `组件错误: ${info}`);\n    return false;\n  }\n}\n</script>\n\n<style scoped>\n.plugin-detail-page {\n  /* WebsitePage组件已经提供了基本布局，这里只需要设置背景色 */\n  background-color: #f5f5f5;\n}\n\n.loading-container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.loading-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.loading-skeleton {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.skeleton-header {\n  margin-bottom: 24px;\n}\n\n.skeleton-breadcrumb {\n  height: 20px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  width: 300px;\n}\n\n.skeleton-plugin-info {\n  display: flex;\n  gap: 32px;\n}\n\n.skeleton-image {\n  width: 300px;\n  height: 200px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n  flex-shrink: 0;\n}\n\n.skeleton-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.skeleton-title {\n  height: 32px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  width: 60%;\n}\n\n.skeleton-description {\n  height: 20px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 4px;\n  width: 80%;\n}\n\n.skeleton-meta {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.skeleton-tag {\n  height: 24px;\n  width: 80px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 12px;\n}\n\n.skeleton-tabs {\n  margin-top: 24px;\n}\n\n.skeleton-tab-bar {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 24px;\n  border-bottom: 2px solid #f0f0f0;\n  padding-bottom: 12px;\n}\n\n.skeleton-tab {\n  height: 40px;\n  width: 120px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n}\n\n.skeleton-tab-content {\n  height: 300px;\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite;\n  border-radius: 8px;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n.plugin-detail-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.navigation-bar {\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);\n  padding: 16px 24px;\n  border-radius: 16px;\n  margin-bottom: 24px;\n  border: 1px solid rgba(59, 130, 246, 0.15);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: #475569;\n  backdrop-filter: blur(20px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  flex: 1;\n}\n\n.navigation-bar::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);\n  opacity: 0.6;\n}\n\n.back-button {\n  color: #3b82f6 !important;\n  background: rgba(59, 130, 246, 0.1) !important;\n  border: 1px solid rgba(59, 130, 246, 0.2) !important;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 44px;\n  height: 44px;\n}\n\n.back-button:hover {\n  background: rgba(59, 130, 246, 0.15) !important;\n  border-color: rgba(59, 130, 246, 0.4) !important;\n  color: #2563eb !important;\n  transform: translateX(-3px) scale(1.05);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n}\n\n.back-button .anticon {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.breadcrumb-path {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  flex-wrap: wrap;\n}\n\n.path-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  color: #64748b;\n  cursor: pointer;\n  padding: 6px 12px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  font-weight: 500;\n}\n\n.path-item:hover {\n  color: #3b82f6;\n  background: rgba(59, 130, 246, 0.08);\n  transform: translateY(-1px);\n}\n\n.path-item .anticon {\n  font-size: 12px;\n}\n\n.path-separator {\n  color: #cbd5e1;\n  font-size: 12px;\n  margin: 0 4px;\n}\n\n.path-current {\n  color: #1e293b;\n  font-weight: 600;\n  font-size: 15px;\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  background: rgba(59, 130, 246, 0.1);\n  padding: 6px 12px;\n  border-radius: 8px;\n  border: 1px solid rgba(59, 130, 246, 0.2);\n}\n\n.nav-right {\n  flex-shrink: 0;\n}\n\n.share-button {\n  color: #64748b !important;\n  background: rgba(255, 255, 255, 0.8) !important;\n  border: 1px solid rgba(203, 213, 225, 0.6) !important;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  padding: 8px 16px;\n  height: 44px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n}\n\n.share-button:hover {\n  color: #3b82f6 !important;\n  background: rgba(59, 130, 246, 0.08) !important;\n  border-color: rgba(59, 130, 246, 0.2) !important;\n  transform: translateY(-2px) scale(1.02);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.share-button .anticon {\n  font-size: 14px;\n}\n\n.plugin-header {\n  background: white;\n  border-radius: 12px;\n  padding: 32px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.plugin-header-content {\n  display: flex;\n  gap: 32px;\n}\n\n.plugin-image {\n  flex-shrink: 0;\n}\n\n.plugin-image img {\n  width: 300px;\n  height: 200px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e8e8e8;\n}\n\n.plugin-info {\n  flex: 1;\n}\n\n.plugin-title {\n  font-size: 32px;\n  font-weight: bold;\n  margin: 0 0 16px 0;\n  color: #1a1a1a;\n}\n\n.plugin-description {\n  font-size: 16px;\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 24px;\n}\n\n.plugin-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.meta-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 24px;\n  align-items: center;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 14px;\n  color: #666;\n  min-width: 200px;\n}\n\n.meta-icon {\n  color: #1890ff;\n  font-size: 16px;\n}\n\n.meta-label {\n  color: #666;\n  font-weight: 500;\n}\n\n.meta-value {\n  color: #333;\n  font-weight: 600;\n}\n\n.price-item {\n  background: linear-gradient(165deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-weight: bold;\n}\n\n.price-item .meta-icon,\n.price-item .meta-label,\n.price-item .meta-value {\n  color: white;\n}\n\n.price-icon {\n  color: #ffd700 !important;\n}\n\n.price-value {\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.tutorial-hint {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1890ff;\n  margin-left: 12px;\n  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);\n  padding: 8px 16px;\n  border-radius: 16px;\n  border: 1px solid #91d5ff;\n  display: inline-flex;\n  align-items: center;\n  animation: pulse-hint 2s ease-in-out infinite;\n  white-space: nowrap;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n  cursor: pointer;\n  transition: all 0.3s ease;\n  user-select: none;\n  min-height: 32px;\n}\n\n.tutorial-hint:hover {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  color: white;\n  transform: translateY(-3px) scale(1.08);\n  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);\n  border-color: #1890ff;\n  font-weight: 700;\n}\n\n.tutorial-hint:active {\n  transform: translateY(-1px) scale(1.02);\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);\n}\n\n@keyframes pulse-hint {\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);\n  }\n}\n\n/* 自定义Tab导航样式 */\n.plugin-tabs-container {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.plugin-tabs-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);\n  background-size: 300% 100%;\n  animation: gradient-flow 3s ease infinite;\n}\n\n@keyframes gradient-flow {\n  0%, 100% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n}\n\n.custom-tabs-nav {\n  margin-bottom: 32px;\n}\n\n.tabs-nav-wrapper {\n  display: flex;\n  align-items: center;\n  background: rgba(248, 250, 252, 0.8);\n  border-radius: 50px;\n  padding: 8px;\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);\n  gap: 8px;\n}\n\n.tab-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 18px 32px;\n  font-size: 16px;\n  font-weight: 600;\n  border-radius: 50px;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  border: none;\n  background: transparent;\n  color: #64748b;\n  white-space: nowrap;\n  user-select: none;\n  z-index: 10;\n  \n}\n\n.tab-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));\n  border-radius: 50px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  transform: scale(0.8);\n}\n\n.tab-item:hover {\n  color: #3b82f6;\n  transform: translateY(-2px) scale(1.02);\n}\n\n.tab-item:hover::before {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.tab-item.active {\n  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  transform: translateY(-3px) scale(1.05);\n  box-shadow:\n    0 8px 32px rgba(59, 130, 246, 0.3),\n    0 0 0 1px rgba(255, 255, 255, 0.2) inset;\n  position: relative;\n  z-index: 2;\n  top: 3px;\n}\n\n.tab-item.active::before {\n  opacity: 0;\n}\n\n.tab-item.active::after {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  background: linear-gradient(165deg, #3b82f6, #8b5cf6);\n  border-radius: 50px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(12px);\n  animation: pulse-glow 2s ease-in-out infinite alternate;\n}\n\n@keyframes pulse-glow {\n  0% { opacity: 0.3; transform: scale(1); }\n  100% { opacity: 0.6; transform: scale(1.05); }\n}\n\n.tab-item.active:hover {\n  color: white;\n  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-3px) scale(1.05);\n}\n\n.tab-item i {\n  font-size: 18px;\n  transition: all 0.3s ease;\n}\n\n.tab-item:hover i {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.tab-item.active i {\n  transform: scale(1.15) rotate(0deg);\n  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));\n}\n\n.custom-tabs-content {\n  position: relative;\n}\n\n.tab-pane {\n  animation: fadeInUp 0.5s ease;\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.plugin-tabs {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 24px;\n  padding: 32px;\n  margin-bottom: 32px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  position: relative;\n  overflow: hidden;\n}\n\n.plugin-tabs::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);\n  background-size: 300% 100%;\n  animation: gradient-flow 3s ease infinite;\n}\n\n@keyframes gradient-flow {\n  0%, 100% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n}\n\n.plugin-tabs .ant-tabs-bar {\n  border-bottom: none !important;\n  margin-bottom: 32px !important;\n  background: transparent !important;\n  padding: 0 !important;\n  position: relative !important;\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的tab导航容器 */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,\n.modern-tabs-container .ant-tabs-nav-container {\n  position: relative !important;\n  background: rgba(248, 250, 252, 0.8) !important;\n  border-radius: 50px !important;\n  padding: 8px !important;\n  backdrop-filter: blur(10px) !important;\n  border: 2px solid rgba(59, 130, 246, 0.2) !important;\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1) !important;\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的tab按钮 */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,\n.modern-tabs-container .ant-tabs-tab {\n  padding: 18px 32px !important;\n  font-size: 16px !important;\n  font-weight: 600 !important;\n  border-radius: 50px !important;\n  margin: 0 4px !important;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  position: relative !important;\n  overflow: hidden !important;\n  border: none !important;\n  background: transparent !important;\n  color: #64748b !important;\n  white-space: nowrap !important;\n}\n\n.plugin-tabs .ant-tabs-tab::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));\n  border-radius: 50px;\n  opacity: 0;\n  transition: all 0.3s ease;\n  transform: scale(0.8);\n}\n\n.plugin-tabs .ant-tabs-tab:hover {\n  color: #3b82f6;\n  transform: translateY(-2px) scale(1.02);\n}\n\n.plugin-tabs .ant-tabs-tab:hover::before {\n  opacity: 1;\n  transform: scale(1);\n}\n\n/* 使用最高优先级选择器覆盖Ant Design的激活tab */\ndiv.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,\ndiv.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,\n.modern-tabs-container .ant-tabs-tab-active {\n  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%) !important;\n  color: white !important;\n  transform: translateY(-3px) scale(1.05) !important;\n  box-shadow:\n    0 8px 32px rgba(59, 130, 246, 0.3),\n    0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;\n  position: relative !important;\n  z-index: 2 !important;\n  border-radius: 50px !important;\n}\n\n.plugin-tabs .ant-tabs-tab-active::before {\n  opacity: 0;\n}\n\n.plugin-tabs .ant-tabs-tab-active::after {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  background: linear-gradient(165deg, #3b82f6, #8b5cf6);\n  border-radius: 50px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(12px);\n  animation: pulse-glow 2s ease-in-out infinite alternate;\n}\n\n@keyframes pulse-glow {\n  0% { opacity: 0.3; transform: scale(1); }\n  100% { opacity: 0.6; transform: scale(1.05); }\n}\n\n.plugin-tabs .ant-tabs-tab-active:hover {\n  color: white;\n  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);\n  transform: translateY(-3px) scale(1.05);\n}\n\n.plugin-tabs .ant-tabs-ink-bar {\n  display: none;\n}\n\n.plugin-tabs .ant-tabs-tab .anticon {\n  margin-right: 10px;\n  font-size: 18px;\n  transition: all 0.3s ease;\n}\n\n.plugin-tabs .ant-tabs-tab:hover .anticon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.plugin-tabs .ant-tabs-tab-active .anticon {\n  transform: scale(1.15) rotate(0deg);\n  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));\n}\n\n/* 添加流畅的切换动画 */\n.plugin-tabs .ant-tabs-content {\n  transition: all 0.3s ease;\n}\n\n.plugin-tabs .ant-tabs-tabpane {\n  animation: fadeInUp 0.5s ease;\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.tab-content {\n  min-height: 400px;\n  padding: 24px 0;\n}\n\n.rich-content {\n  line-height: 1.8;\n  color: #333;\n}\n\n.empty-content {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.author-section,\n.recommendations-section {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  margin-bottom: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.error-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 60vh;\n  background-color: #f5f5f5;\n  padding: 40px 20px;\n}\n\n.error-actions {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n\n.error-actions .ant-btn {\n  min-width: 120px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .plugin-detail-content {\n    max-width: 1000px;\n  }\n\n  .plugin-header-content {\n    gap: 24px;\n  }\n\n  .plugin-image img {\n    width: 280px;\n    height: 180px;\n  }\n}\n\n@media (max-width: 768px) {\n  .plugin-detail-content {\n    padding: 16px;\n    max-width: 100%;\n  }\n\n  .navigation-bar {\n    padding: 12px 16px;\n    margin-bottom: 16px;\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n\n  .nav-left {\n    gap: 12px;\n  }\n\n  .breadcrumb-path {\n    font-size: 12px;\n    gap: 6px;\n  }\n\n  .path-current {\n    max-width: 150px;\n    font-size: 14px;\n  }\n\n  .nav-right {\n    align-self: center;\n  }\n\n  .plugin-header {\n    padding: 20px;\n    margin-bottom: 16px;\n  }\n\n  .plugin-header-content {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n\n  .plugin-image {\n    align-self: center;\n  }\n\n  .plugin-image img {\n    width: 100%;\n    max-width: 280px;\n    height: 180px;\n  }\n\n  .plugin-title {\n    font-size: 24px;\n    text-align: center;\n  }\n\n  .plugin-description {\n    text-align: center;\n    font-size: 15px;\n  }\n\n  .meta-row {\n    flex-direction: column;\n    gap: 12px;\n    align-items: center;\n  }\n\n  .meta-item {\n    min-width: auto;\n    width: auto;\n    justify-content: center;\n  }\n\n  .price-item {\n    justify-content: center;\n    text-align: center;\n    width: fit-content;\n  }\n\n  .plugin-tabs {\n    padding: 16px;\n    margin-bottom: 16px;\n  }\n\n  .plugin-tabs .ant-tabs-tab {\n    padding: 8px 12px;\n    font-size: 14px;\n    margin-right: 4px;\n  }\n\n  .tab-content {\n    min-height: 300px;\n    padding: 16px 0;\n  }\n\n  .author-section,\n  .recommendations-section {\n    margin-bottom: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .plugin-detail-content {\n    padding: 12px;\n  }\n\n  .navigation-bar {\n    padding: 8px 12px;\n    border-radius: 8px;\n  }\n\n  .back-button {\n    width: 36px;\n    height: 36px;\n  }\n\n  .breadcrumb-path {\n    font-size: 11px;\n    gap: 4px;\n  }\n\n  .path-current {\n    max-width: 120px;\n    font-size: 12px;\n  }\n\n  .nav-right .ant-btn-group .ant-btn {\n    font-size: 12px;\n    padding: 4px 8px;\n  }\n\n  .plugin-header {\n    padding: 16px;\n  }\n\n  .plugin-title {\n    font-size: 20px;\n  }\n\n  .plugin-description {\n    font-size: 14px;\n  }\n\n  .meta-row {\n    gap: 8px;\n  }\n\n  .meta-item {\n    font-size: 12px;\n  }\n\n  .plugin-tabs {\n    padding: 12px;\n  }\n\n  .plugin-tabs .ant-tabs-tab {\n    padding: 6px 8px;\n    font-size: 12px;\n    margin-right: 2px;\n  }\n\n  .tab-content {\n    padding: 12px 0;\n  }\n}\n</style>\n"]}]}