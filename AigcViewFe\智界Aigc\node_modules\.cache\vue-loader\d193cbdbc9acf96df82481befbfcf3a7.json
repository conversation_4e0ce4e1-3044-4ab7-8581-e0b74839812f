{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue?vue&type=style&index=0&id=dbf24c8c&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue", "mtime": 1754042137915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.agent-market {\n  padding: 0;\n}\n\n/* 固定的搜索和筛选区域 */\n.sticky-filters {\n  position: sticky;\n  top: 156px; /* 顶部导航栏100px + tab栏60px */\n  z-index: 100;\n  background: white;\n  padding: 1rem 0;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.market-filters {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  padding: 2rem;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n  max-width: 1600px;\n  margin: 0 auto;\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 600px;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  border: 2px solid #cbd5e1;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.search-wrapper:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.search-wrapper:focus-within {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n  transform: translateY(-1px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 16px;\n  z-index: 2;\n  color: #64748b;\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.search-wrapper:focus-within .search-icon {\n  color: #3b82f6;\n}\n\n.clear-icon {\n  position: absolute;\n  right: 16px;\n  z-index: 2;\n  color: #94a3b8;\n  font-size: 16px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.clear-icon:hover {\n  color: #64748b;\n}\n\n.search-input {\n  flex: 1;\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n  font-size: 16px;\n  height: 48px;\n  background: transparent;\n}\n\n.search-input:focus {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.filter-controls {\n  display: flex;\n  gap: 1.5rem;\n  align-items: center;\n}\n\n.filter-item-inline {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  white-space: nowrap;\n}\n\n.filter-select {\n  min-width: 160px;\n}\n\n.filter-select .ant-select-selector {\n  border-radius: 12px !important;\n  border: 2px solid #cbd5e1 !important;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  background: white !important;\n  height: 48px !important;\n}\n\n.filter-select .ant-select-selection-item {\n  line-height: 44px !important;\n  font-weight: 500 !important;\n  color: #374151 !important;\n}\n\n.filter-select:hover .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select.ant-select-focused .ant-select-selector,\n.filter-select.ant-select-open .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select .ant-select-arrow {\n  color: #64748b !important;\n  font-size: 16px !important;\n  transition: all 0.3s ease !important;\n}\n\n.filter-select:hover .ant-select-arrow,\n.filter-select.ant-select-focused .ant-select-arrow,\n.filter-select.ant-select-open .ant-select-arrow {\n  color: #3b82f6 !important;\n  transform: scale(1.1) !important;\n}\n\n/* 下拉菜单样式 */\n.filter-select .ant-select-dropdown {\n  border-radius: 12px !important;\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12) !important;\n  border: 1px solid #e2e8f0 !important;\n  overflow: hidden !important;\n}\n\n.filter-select .ant-select-item {\n  padding: 12px 16px !important;\n  font-weight: 500 !important;\n  transition: all 0.2s ease !important;\n}\n\n.filter-select .ant-select-item:hover {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;\n  color: #3b82f6 !important;\n}\n\n.filter-select .ant-select-item-option-selected {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n.filter-select .ant-select-item-option-selected:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;\n}\n\n/* 智能体列表 */\n.agent-list {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 2rem 2rem 1rem 2rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.list-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.list-count {\n  color: #64748b;\n  font-weight: 400;\n  font-size: 1rem;\n}\n\n/* 智能体网格 */\n.agent-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 2rem;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* 懒加载相关样式 */\n.load-more-wrapper {\n  padding: 2rem;\n  border-top: 1px solid #f1f5f9;\n  display: flex;\n  justify-content: center;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.load-more-trigger {\n  height: 20px;\n  width: 100%;\n}\n\n.no-more-data {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #94a3b8;\n  font-size: 0.875rem;\n}\n\n.no-more-data .anticon {\n  color: #10b981;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 400px;\n  background: transparent;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n\n  .filter-controls {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .agent-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .market-filters,\n  .list-header,\n  .load-more-wrapper {\n    padding: 1rem;\n  }\n}\n", {"version": 3, "sources": ["AgentMarket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgeA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "AgentMarket.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <div class=\"agent-market\">\n    <!-- 固定的搜索和筛选区域 -->\n    <div class=\"sticky-filters\">\n      <div class=\"market-filters\">\n        <div class=\"filter-row\">\n          <!-- 搜索框 -->\n          <div class=\"search-box\">\n            <div class=\"search-wrapper\">\n              <a-icon type=\"search\" class=\"search-icon\" />\n              <a-input\n                v-model=\"searchQuery\"\n                placeholder=\"搜索智能体名称、描述或标签...\"\n                size=\"large\"\n                @pressEnter=\"handleSearch\"\n                @input=\"handleSearch\"\n                class=\"search-input\"\n              />\n              <a-icon\n                v-if=\"searchQuery\"\n                type=\"close-circle\"\n                class=\"clear-icon\"\n                @click=\"clearSearch\"\n              />\n            </div>\n          </div>\n\n          <!-- 筛选器 -->\n          <div class=\"filter-controls\">\n            <div class=\"filter-item-inline\">\n              <span class=\"filter-label\">作者类型：</span>\n              <a-select\n                v-model=\"authorTypeFilter\"\n                placeholder=\"全部类型\"\n                size=\"large\"\n                class=\"filter-select\"\n                @change=\"handleFilterChange\"\n              >\n                <a-select-option value=\"\">全部类型</a-select-option>\n                <a-select-option value=\"1\">\n                  <a-icon type=\"crown\" style=\"color: #f59e0b; margin-right: 4px;\" />\n                  官方\n                </a-select-option>\n                <a-select-option value=\"2\">\n                  <a-icon type=\"user\" style=\"color: #3b82f6; margin-right: 4px;\" />\n                  创作者\n                </a-select-option>\n              </a-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能体列表 -->\n    <div class=\"agent-list\" v-if=\"!loading\">\n      <div class=\"list-header\">\n        <h3 class=\"list-title\">\n          智能体列表\n          <span class=\"list-count\">({{ totalCount }}个)</span>\n        </h3>\n      </div>\n\n      <!-- 智能体卡片网格 -->\n      <div class=\"agent-grid\" v-if=\"agentList.length > 0\">\n        <AgentCard\n          v-for=\"agent in agentList\"\n          :key=\"agent.id\"\n          :agent=\"agent\"\n          @view-detail=\"handleViewDetail\"\n        />\n      </div>\n\n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <a-empty\n          description=\"暂无智能体数据\"\n        >\n          <a-button type=\"primary\" @click=\"handleRefresh\">\n            <a-icon type=\"reload\" />\n            刷新数据\n          </a-button>\n        </a-empty>\n      </div>\n\n      <!-- 加载更多提示 -->\n      <div class=\"load-more-wrapper\" v-if=\"agentList.length > 0\">\n        <div v-if=\"loadingMore\" class=\"loading-more\">\n          <a-spin size=\"small\" />\n          <span>正在加载更多...</span>\n        </div>\n        <div v-else-if=\"hasMore\" class=\"load-more-trigger\" ref=\"loadMoreTrigger\">\n          <!-- 滚动到这里触发加载更多 -->\n        </div>\n        <div v-else class=\"no-more-data\">\n          <a-icon type=\"check-circle\" />\n          <span>已加载全部数据 (共{{ totalCount }}个)</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-else class=\"loading-state\">\n      <a-spin size=\"large\" tip=\"正在加载智能体数据...\">\n        <div class=\"loading-placeholder\"></div>\n      </a-spin>\n    </div>\n\n    <!-- 智能体详情弹窗 -->\n    <AgentDetailModal\n      :visible=\"detailModalVisible\"\n      :agentId=\"selectedAgentId\"\n      :isPurchased=\"isSelectedAgentPurchased\"\n      @close=\"handleCloseDetailModal\"\n      @purchase=\"handlePurchaseFromModal\"\n      @purchase-success=\"handlePurchaseSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport AgentCard from './AgentCard.vue'\nimport AgentDetailModal from './AgentDetailModal.vue'\nimport { getUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'AgentMarket',\n  components: {\n    AgentCard,\n    AgentDetailModal\n  },\n  data() {\n    return {\n      loading: false,\n      loadingMore: false,\n      searchQuery: '',\n      authorTypeFilter: '',\n      agentList: [],\n      userRole: 'user', // 用户角色\n      currentPage: 1,\n      pageSize: 16,\n      totalCount: 0,\n      hasMore: true,\n      // 详情弹窗相关\n      detailModalVisible: false, // 详情弹窗显示状态\n      selectedAgentId: '', // 选中的智能体ID\n      selectedAgent: null, // 选中的智能体数据\n      purchasedAgents: [] // 已购买的智能体ID列表\n    }\n  },\n  computed: {\n    // 检查选中的智能体是否已购买\n    isSelectedAgentPurchased() {\n      return this.selectedAgentId && this.purchasedAgents.includes(this.selectedAgentId)\n    }\n  },\n  async mounted() {\n    await this.loadUserRole()\n    await this.loadPurchasedAgents()\n    await this.loadAgentList()\n    this.setupIntersectionObserver()\n  },\n\n  beforeDestroy() {\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n  },\n  methods: {\n    // 加载用户角色\n    async loadUserRole() {\n      try {\n        const role = await getUserRole()\n        this.userRole = role\n        console.log('🔍 AgentMarket: 用户角色:', this.userRole)\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = null\n      }\n    },\n\n    // 加载智能体列表（首次加载或搜索时重置）\n    async loadAgentList(reset = true) {\n      if (reset) {\n        this.loading = true\n        this.currentPage = 1\n        this.agentList = []\n        this.hasMore = true\n      } else {\n        this.loadingMore = true\n      }\n\n      try {\n        const params = {\n          pageNo: this.currentPage,\n          pageSize: this.pageSize,\n          agentName: this.searchQuery || undefined,\n          authorType: this.authorTypeFilter || undefined,\n          auditStatus: '2' // 只显示审核通过的\n        }\n\n        // 调用后端API\n        const response = await this.$http.get('/api/agent/market/list', { params })\n        console.log('API响应:', response)\n\n        // 兼容不同的响应格式\n        const data = response.data || response\n        if (data && data.success) {\n          const newRecords = data.result.records || []\n\n          // 对每个智能体添加价格信息和购买状态\n          const processedRecords = newRecords.map(agent => {\n            const priceInfo = this.calculatePrice(agent)\n            const isPurchased = this.isAgentPurchased(agent.id)\n            return {\n              ...agent,\n              ...priceInfo,\n              isPurchased\n            }\n          })\n\n          if (reset) {\n            this.agentList = processedRecords\n          } else {\n            this.agentList.push(...processedRecords)\n          }\n\n          this.totalCount = data.result.total || 0\n\n          // 判断是否还有更多数据\n          this.hasMore = this.agentList.length < this.totalCount\n\n          // 如果有数据，准备下一页\n          if (newRecords.length > 0) {\n            this.currentPage++\n          }\n        } else {\n          this.$message.error((data && data.message) || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('加载智能体列表失败，请稍后重试')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n\n        // 重新设置IntersectionObserver，确保监听新的DOM元素\n        if (reset) {\n          this.$nextTick(() => {\n            this.setupIntersectionObserver()\n          })\n        }\n      }\n    },\n\n    // 临时模拟数据\n    async loadMockData() {\n      // 模拟API延迟\n      await new Promise(resolve => setTimeout(resolve, 500))\n\n      this.agentList = []\n      this.totalCount = 0\n      this.hasMore = false\n    },\n\n    // 加载更多数据\n    async loadMore() {\n      if (!this.hasMore || this.loadingMore) {\n        return\n      }\n      await this.loadAgentList(false)\n    },\n\n    // 计算价格和推广标签显示\n    calculatePrice(agent) {\n      let showSvipPromo = false\n      let showDiscountPrice = false\n      let discountRate = 1 // 默认无折扣\n      let isFree = false // 是否免费\n\n      // 根据用户角色计算价格和推广显示\n      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {\n        // 未登录、普通用户或管理员：显示SVIP推广标签\n        showSvipPromo = true\n        showDiscountPrice = false\n      } else if (this.userRole === 'VIP') {\n        // VIP用户：显示7折价格，不显示推广标签\n        showSvipPromo = false\n        showDiscountPrice = true\n        discountRate = 0.7 // VIP 7折\n      } else if (this.userRole === 'SVIP') {\n        // SVIP用户：根据作者类型计算价格\n        showSvipPromo = false\n\n        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {\n          // 官方智能体：免费\n          isFree = true\n          discountRate = 0\n          showDiscountPrice = false // 免费时不显示折扣价格\n        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {\n          // 创作者智能体：5折\n          isFree = false\n          showDiscountPrice = true\n          discountRate = 0.5\n        }\n      }\n\n      console.log('🔍 calculatePrice [NEW VERSION]: 智能体名称:', agent && agent.agentName, '用户角色:', this.userRole, '作者类型:', agent && agent.authorType, '显示SVIP推广:', showSvipPromo, '显示折扣价:', showDiscountPrice, '折扣率:', discountRate, '是否免费:', isFree)\n\n      return {\n        showSvipPromo,\n        showDiscountPrice,\n        discountRate,\n        isFree\n      }\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 筛选变化处理\n    handleFilterChange() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 滚动到顶部\n    scrollToTop() {\n      // 滚动到页面顶部\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      })\n    },\n\n    // 设置滚动监听\n    setupIntersectionObserver() {\n      // 先断开旧的observer\n      if (this.observer) {\n        this.observer.disconnect()\n        this.observer = null\n      }\n\n      this.$nextTick(() => {\n        const target = this.$refs.loadMoreTrigger\n        if (!target) return\n\n        this.observer = new IntersectionObserver((entries) => {\n          entries.forEach(entry => {\n            if (entry.isIntersecting && this.hasMore && !this.loadingMore) {\n              console.log('触发懒加载，hasMore:', this.hasMore, 'loadingMore:', this.loadingMore)\n              this.loadMore()\n            }\n          })\n        }, {\n          rootMargin: '100px' // 提前100px开始加载\n        })\n\n        this.observer.observe(target)\n        console.log('IntersectionObserver已重新设置')\n      })\n    },\n\n    // 查看详情\n    handleViewDetail(agent) {\n      console.log('查看智能体详情:', agent)\n      this.selectedAgent = agent\n      this.selectedAgentId = agent.id\n      this.detailModalVisible = true\n    },\n\n    // 关闭详情弹窗\n    handleCloseDetailModal() {\n      this.detailModalVisible = false\n      this.selectedAgent = null\n      this.selectedAgentId = ''\n    },\n\n    // 从弹窗发起购买\n    handlePurchaseFromModal(agent) {\n      console.log('从弹窗购买智能体:', agent)\n      // 购买逻辑已在AgentDetailModal中实现\n    },\n\n    // 购买成功回调\n    handlePurchaseSuccess(agentId) {\n      console.log('购买成功回调:', agentId)\n      this.onPurchaseSuccess(agentId)\n      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了')\n    },\n\n    // 加载已购买的智能体列表\n    async loadPurchasedAgents() {\n      try {\n        // 检查是否已登录\n        const token = this.$store.getters.token\n        if (!token) {\n          console.log('用户未登录，跳过购买状态检查')\n          return\n        }\n\n        // 检查缓存时效性（5分钟）\n        const cacheKey = 'purchasedAgents'\n        const cacheTimeKey = 'purchasedAgentsTime'\n        const cacheTime = localStorage.getItem(cacheTimeKey)\n        const now = Date.now()\n        const cacheExpiry = 5 * 60 * 1000 // 5分钟\n\n        // 从缓存中获取\n        const cached = localStorage.getItem(cacheKey)\n        if (cached && cacheTime && (now - parseInt(cacheTime)) < cacheExpiry) {\n          try {\n            this.purchasedAgents = JSON.parse(cached)\n            console.log('🔍 从缓存加载已购买智能体:', this.purchasedAgents.length, '个')\n            return // 缓存有效，直接返回\n          } catch (e) {\n            console.warn('购买状态缓存解析失败:', e)\n            localStorage.removeItem(cacheKey)\n            localStorage.removeItem(cacheTimeKey)\n          }\n        }\n\n        // 缓存过期或不存在，从后端获取\n        // TODO: 调用后端API获取最新的购买状态\n        // const response = await this.$http.get('/api/agent/purchase/list')\n        // if (response.data && response.data.success) {\n        //   this.purchasedAgents = response.data.result || []\n        //   localStorage.setItem(cacheKey, JSON.stringify(this.purchasedAgents))\n        //   localStorage.setItem(cacheTimeKey, now.toString())\n        //   console.log('✅ 购买状态更新成功:', this.purchasedAgents.length, '个')\n        // }\n      } catch (error) {\n        console.error('❌ 加载购买状态失败:', error)\n      }\n    },\n\n    // 检查智能体是否已购买\n    isAgentPurchased(agentId) {\n      return this.purchasedAgents.includes(agentId)\n    },\n\n    // 购买成功后更新状态\n    onPurchaseSuccess(agentId) {\n      if (!this.purchasedAgents.includes(agentId)) {\n        this.purchasedAgents.push(agentId)\n        localStorage.setItem('purchasedAgents', JSON.stringify(this.purchasedAgents))\n        console.log('✅ 购买状态已更新:', agentId)\n\n        // 更新智能体列表中的购买状态\n        this.agentList.forEach(agent => {\n          if (agent.id === agentId) {\n            agent.isPurchased = true\n          }\n        })\n      }\n    },\n\n    // 刷新数据\n    handleRefresh() {\n      this.loadAgentList()\n    },\n\n    // 获取创作者数量\n    getCreatorCount() {\n      return this.agentList.filter(agent => agent.authorType === '2').length\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.searchQuery = ''\n      this.handleSearch()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.agent-market {\n  padding: 0;\n}\n\n/* 固定的搜索和筛选区域 */\n.sticky-filters {\n  position: sticky;\n  top: 156px; /* 顶部导航栏100px + tab栏60px */\n  z-index: 100;\n  background: white;\n  padding: 1rem 0;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.market-filters {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  padding: 2rem;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n  max-width: 1600px;\n  margin: 0 auto;\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 600px;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  border: 2px solid #cbd5e1;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.search-wrapper:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.search-wrapper:focus-within {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n  transform: translateY(-1px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 16px;\n  z-index: 2;\n  color: #64748b;\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.search-wrapper:focus-within .search-icon {\n  color: #3b82f6;\n}\n\n.clear-icon {\n  position: absolute;\n  right: 16px;\n  z-index: 2;\n  color: #94a3b8;\n  font-size: 16px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.clear-icon:hover {\n  color: #64748b;\n}\n\n.search-input {\n  flex: 1;\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n  font-size: 16px;\n  height: 48px;\n  background: transparent;\n}\n\n.search-input:focus {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.filter-controls {\n  display: flex;\n  gap: 1.5rem;\n  align-items: center;\n}\n\n.filter-item-inline {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  white-space: nowrap;\n}\n\n.filter-select {\n  min-width: 160px;\n}\n\n.filter-select .ant-select-selector {\n  border-radius: 12px !important;\n  border: 2px solid #cbd5e1 !important;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  background: white !important;\n  height: 48px !important;\n}\n\n.filter-select .ant-select-selection-item {\n  line-height: 44px !important;\n  font-weight: 500 !important;\n  color: #374151 !important;\n}\n\n.filter-select:hover .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select.ant-select-focused .ant-select-selector,\n.filter-select.ant-select-open .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select .ant-select-arrow {\n  color: #64748b !important;\n  font-size: 16px !important;\n  transition: all 0.3s ease !important;\n}\n\n.filter-select:hover .ant-select-arrow,\n.filter-select.ant-select-focused .ant-select-arrow,\n.filter-select.ant-select-open .ant-select-arrow {\n  color: #3b82f6 !important;\n  transform: scale(1.1) !important;\n}\n\n/* 下拉菜单样式 */\n.filter-select .ant-select-dropdown {\n  border-radius: 12px !important;\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12) !important;\n  border: 1px solid #e2e8f0 !important;\n  overflow: hidden !important;\n}\n\n.filter-select .ant-select-item {\n  padding: 12px 16px !important;\n  font-weight: 500 !important;\n  transition: all 0.2s ease !important;\n}\n\n.filter-select .ant-select-item:hover {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;\n  color: #3b82f6 !important;\n}\n\n.filter-select .ant-select-item-option-selected {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n.filter-select .ant-select-item-option-selected:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;\n}\n\n/* 智能体列表 */\n.agent-list {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 2rem 2rem 1rem 2rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.list-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.list-count {\n  color: #64748b;\n  font-weight: 400;\n  font-size: 1rem;\n}\n\n/* 智能体网格 */\n.agent-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 2rem;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* 懒加载相关样式 */\n.load-more-wrapper {\n  padding: 2rem;\n  border-top: 1px solid #f1f5f9;\n  display: flex;\n  justify-content: center;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.load-more-trigger {\n  height: 20px;\n  width: 100%;\n}\n\n.no-more-data {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #94a3b8;\n  font-size: 0.875rem;\n}\n\n.no-more-data .anticon {\n  color: #10b981;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 400px;\n  background: transparent;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n\n  .filter-controls {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .agent-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .market-filters,\n  .list-header,\n  .load-more-wrapper {\n    padding: 1rem;\n  }\n}\n</style>\n"]}]}