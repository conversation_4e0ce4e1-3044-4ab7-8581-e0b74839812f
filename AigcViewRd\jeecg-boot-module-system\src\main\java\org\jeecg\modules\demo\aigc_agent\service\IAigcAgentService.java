package org.jeecg.modules.demo.aigc_agent.service;

import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 智能体表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
public interface IAigcAgentService extends IService<AigcAgent> {

	/**
	 * 添加一对多
	 * 
	 */
	public void saveMain(AigcAgent aigcAgent,List<AigcWorkflow> aigcWorkflowList) ;
	
	/**
	 * 修改一对多
	 * 
	 */
	public void updateMain(AigcAgent aigcAgent,List<AigcWorkflow> aigcWorkflowList);
	
	/**
	 * 删除一对多
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
