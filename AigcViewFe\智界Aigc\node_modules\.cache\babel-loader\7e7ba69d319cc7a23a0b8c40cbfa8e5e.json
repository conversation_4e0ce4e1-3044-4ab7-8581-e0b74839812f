{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754051311797}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nvar _methods;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nexport default {\n  name: 'AgentDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    agentId: {\n      type: String,\n      default: ''\n    },\n    isPurchased: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      loading: false,\n      workflowLoading: false,\n      purchaseLoading: false,\n      downloadLoading: {},\n      // 工作流下载loading状态\n      agentDetail: {},\n      // 智能体详情\n      workflowList: [],\n      // 工作流列表\n      // 视频播放相关状态\n      videoLoading: false,\n      videoError: false,\n      videoPlaying: false,\n      videoMuted: true,\n      // 默认静音以支持自动播放\n      videoAutoplay: true,\n      currentTime: 0,\n      duration: 0,\n      videoVolume: 50,\n      showControls: true,\n      showVolumeSlider: false,\n      progressPercent: 0\n    };\n  },\n  computed: {\n    // 创作者名称\n    creatorName: function creatorName() {\n      if (this.agentDetail.creatorInfo) {\n        // 优先显示昵称，其次显示姓名\n        return this.agentDetail.creatorInfo.nickname || this.agentDetail.creatorInfo.name || '未知创作者';\n      }\n\n      return '未知创作者';\n    },\n    // 作者类型文本\n    authorTypeText: function authorTypeText() {\n      if (this.agentDetail.authorType === '1') {\n        return '官方';\n      } else if (this.agentDetail.authorType === '2') {\n        return '创作者';\n      }\n\n      return '未知';\n    },\n    // 最终价格（考虑折扣）\n    finalPrice: function finalPrice() {\n      if (!this.agentDetail) return 0;\n      if (this.agentDetail.isFree) return 0; // 安全地获取价格，确保返回数字\n\n      var originalPrice = parseFloat(this.agentDetail.price || this.agentDetail.originalPrice) || 0;\n      var discountPrice = parseFloat(this.agentDetail.discountPrice) || originalPrice;\n      return this.agentDetail.showDiscountPrice ? discountPrice : originalPrice;\n    }\n  },\n  watch: {\n    visible: function visible(newVal) {\n      if (newVal && this.agentId) {\n        this.loadAgentDetail();\n        this.loadWorkflowList();\n      } else {\n        this.resetData();\n      }\n    }\n  },\n  methods: (_methods = {\n    // 加载智能体详情\n    loadAgentDetail: function () {\n      var _loadAgentDetail = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var baseAgent;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                this.loading = true;\n                _context.prev = 1;\n                _context.next = 4;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 500);\n                });\n\n              case 4:\n                // 如果有传入的agent数据，使用它作为基础\n                baseAgent = this.$parent.selectedAgent || {};\n                this.agentDetail = {\n                  id: this.agentId,\n                  agentName: baseAgent.agentName || '示例智能体',\n                  agentDescription: baseAgent.agentDescription || baseAgent.description || '这是一个示例智能体的详细描述信息',\n                  agentAvatar: baseAgent.agentAvatar || '',\n                  demoVideo: baseAgent.demoVideo || '',\n                  experienceLink: baseAgent.experienceLink || 'https://example.com',\n                  price: baseAgent.price || 99,\n                  originalPrice: baseAgent.originalPrice || baseAgent.price || 99,\n                  authorType: baseAgent.authorType || '1',\n                  // 继承价格计算结果\n                  showSvipPromo: baseAgent.showSvipPromo || false,\n                  showDiscountPrice: baseAgent.showDiscountPrice || false,\n                  discountRate: baseAgent.discountRate || 1,\n                  isFree: baseAgent.isFree || false,\n                  creatorInfo: {\n                    name: '官方团队',\n                    nickname: '智界Aigc',\n                    avatar: ''\n                  }\n                };\n                console.log('🔍 AgentDetailModal: 加载智能体详情', this.agentDetail);\n                _context.next = 14;\n                break;\n\n              case 9:\n                _context.prev = 9;\n                _context.t0 = _context[\"catch\"](1);\n                console.error('加载智能体详情失败:', _context.t0);\n                this.$message.error('加载智能体详情失败'); // 设置默认数据防止页面崩溃\n\n                this.agentDetail = {\n                  id: this.agentId,\n                  agentName: '加载失败',\n                  agentDescription: '智能体详情加载失败',\n                  agentAvatar: '',\n                  demoVideo: '',\n                  experienceLink: '',\n                  price: 0,\n                  originalPrice: 0,\n                  authorType: '2',\n                  showSvipPromo: false,\n                  showDiscountPrice: false,\n                  discountRate: 0,\n                  isFree: false,\n                  creatorInfo: {\n                    name: '未知',\n                    nickname: '未知',\n                    avatar: ''\n                  }\n                };\n\n              case 14:\n                _context.prev = 14;\n                this.loading = false;\n                return _context.finish(14);\n\n              case 17:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[1, 9, 14, 17]]);\n      }));\n\n      function loadAgentDetail() {\n        return _loadAgentDetail.apply(this, arguments);\n      }\n\n      return loadAgentDetail;\n    }(),\n    // 加载工作流列表\n    loadWorkflowList: function () {\n      var _loadWorkflowList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var baseAgent, agentName;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                this.workflowLoading = true;\n                _context2.prev = 1;\n                _context2.next = 4;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 300);\n                });\n\n              case 4:\n                baseAgent = this.$parent.selectedAgent || {};\n                agentName = baseAgent.agentName || '智能体'; // 根据智能体类型生成相应的工作流\n\n                if (baseAgent.authorType === '1') {\n                  // 官方智能体 - 更多工作流\n                  this.workflowList = [{\n                    id: '1',\n                    workflowName: \"\".concat(agentName, \" - \\u57FA\\u7840\\u5BF9\\u8BDD\\u6D41\\u7A0B\"),\n                    workflowDescription: '适用于日常对话和基础问答的工作流程',\n                    agentAvatar: baseAgent.agentAvatar || '',\n                    sequence: 1\n                  }, {\n                    id: '2',\n                    workflowName: \"\".concat(agentName, \" - \\u9AD8\\u7EA7\\u5206\\u6790\\u6D41\\u7A0B\"),\n                    workflowDescription: '用于复杂数据分析和深度思考的高级工作流',\n                    agentAvatar: baseAgent.agentAvatar || '',\n                    sequence: 2\n                  }, {\n                    id: '3',\n                    workflowName: \"\".concat(agentName, \" - \\u521B\\u610F\\u751F\\u6210\\u6D41\\u7A0B\"),\n                    workflowDescription: '专门用于创意内容生成和头脑风暴的工作流',\n                    agentAvatar: baseAgent.agentAvatar || '',\n                    sequence: 3\n                  }];\n                } else {\n                  // 创作者智能体 - 较少工作流\n                  this.workflowList = [{\n                    id: '1',\n                    workflowName: \"\".concat(agentName, \" - \\u6838\\u5FC3\\u529F\\u80FD\\u6D41\\u7A0B\"),\n                    workflowDescription: '智能体的核心功能和主要应用场景',\n                    agentAvatar: baseAgent.agentAvatar || '',\n                    sequence: 1\n                  }, {\n                    id: '2',\n                    workflowName: \"\".concat(agentName, \" - \\u6269\\u5C55\\u5E94\\u7528\\u6D41\\u7A0B\"),\n                    workflowDescription: '扩展功能和特殊应用场景的工作流程',\n                    agentAvatar: baseAgent.agentAvatar || '',\n                    sequence: 2\n                  }];\n                }\n\n                console.log('🔍 AgentDetailModal: 加载工作流列表', this.workflowList.length, '个');\n                _context2.next = 14;\n                break;\n\n              case 10:\n                _context2.prev = 10;\n                _context2.t0 = _context2[\"catch\"](1);\n                console.error('加载工作流列表失败:', _context2.t0);\n                this.$message.error('加载工作流列表失败');\n\n              case 14:\n                _context2.prev = 14;\n                this.workflowLoading = false;\n                return _context2.finish(14);\n\n              case 17:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[1, 10, 14, 17]]);\n      }));\n\n      function loadWorkflowList() {\n        return _loadWorkflowList.apply(this, arguments);\n      }\n\n      return loadWorkflowList;\n    }(),\n    // 重置数据\n    resetData: function resetData() {\n      this.agentDetail = {};\n      this.workflowList = [];\n      this.downloadLoading = {}; // 重置视频状态\n\n      this.videoLoading = false;\n      this.videoError = false;\n      this.videoPlaying = false;\n      this.currentTime = 0;\n      this.duration = 0;\n      this.progressPercent = 0;\n      this.showVolumeSlider = false; // 停止视频播放\n\n      if (this.$refs.demoVideo) {\n        this.$refs.demoVideo.pause();\n        this.$refs.demoVideo.currentTime = 0;\n      }\n    },\n    // 关闭弹窗\n    handleClose: function handleClose() {\n      this.$emit('close');\n    },\n    // 购买操作\n    handlePurchase: function handlePurchase() {\n      this.showPurchaseConfirm();\n    },\n    // 显示购买确认弹窗\n    showPurchaseConfirm: function showPurchaseConfirm() {\n      var _this = this;\n\n      var h = this.$createElement;\n      var agent = this.agentDetail;\n      var finalPrice = this.finalPrice;\n      this.$confirm({\n        title: '确认购买智能体',\n        content: function content() {\n          return h(\"div\", {\n            \"style\": \"padding: 16px 0;\"\n          }, [h(\"div\", {\n            \"style\": \"display: flex; align-items: center; margin-bottom: 16px;\"\n          }, [h(\"img\", {\n            \"attrs\": {\n              \"src\": agent.agentAvatar || '',\n              \"alt\": agent.agentName\n            },\n            \"style\": \"width: 48px; height: 48px; border-radius: 8px; margin-right: 12px; object-fit: cover;\",\n            \"on\": {\n              \"error\": function error(e) {\n                e.target.style.display = 'none';\n              }\n            }\n          }), h(\"div\", [h(\"div\", {\n            \"style\": \"font-weight: 600; font-size: 16px; color: #1a202c;\"\n          }, [agent.agentName]), h(\"div\", {\n            \"style\": \"font-size: 12px; color: #718096; margin-top: 4px;\"\n          }, [agent.authorType === '1' ? '官方智能体' : '创作者智能体'])])]), h(\"div\", {\n            \"style\": \"background: #f7fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;\"\n          }, [h(\"div\", {\n            \"style\": \"display: flex; justify-content: space-between; align-items: center;\"\n          }, [h(\"span\", {\n            \"style\": \"color: #4a5568;\"\n          }, [\"\\u8D2D\\u4E70\\u4EF7\\u683C\\uFF1A\"]), h(\"div\", [agent.isFree ? h(\"span\", {\n            \"style\": \"font-size: 18px; font-weight: 600; color: #38a169;\"\n          }, [\"\\u514D\\u8D39\"]) : agent.showDiscountPrice ? h(\"div\", [h(\"span\", {\n            \"style\": \"font-size: 18px; font-weight: 600; color: #e53e3e;\"\n          }, [\"\\xA5\", finalPrice]), h(\"span\", {\n            \"style\": \"font-size: 14px; color: #a0aec0; text-decoration: line-through; margin-left: 8px;\"\n          }, [\"\\xA5\", agent.price || agent.originalPrice || 0])]) : h(\"span\", {\n            \"style\": \"font-size: 18px; font-weight: 600; color: #2d3748;\"\n          }, [\"\\xA5\", agent.price || agent.originalPrice || 0])])])]), h(\"div\", {\n            \"style\": \"font-size: 14px; color: #718096; line-height: 1.5;\"\n          }, [\"\\u8D2D\\u4E70\\u540E\\u60A8\\u5C06\\u83B7\\u5F97\\uFF1A\", h(\"ul\", {\n            \"style\": \"margin: 8px 0 0 16px; padding: 0;\"\n          }, [h(\"li\", [\"\\u6C38\\u4E45\\u4F7F\\u7528\\u6743\\u9650\"]), h(\"li\", [\"\\u6240\\u6709\\u5DE5\\u4F5C\\u6D41\\u4E0B\\u8F7D\\u6743\\u9650\"]), h(\"li\", [\"\\u65E0\\u4F7F\\u7528\\u6B21\\u6570\\u9650\\u5236\"]), h(\"li\", [\"\\u540E\\u7EED\\u529F\\u80FD\\u66F4\\u65B0\"])])])]);\n        },\n        okText: '确认购买',\n        cancelText: '取消',\n        okType: 'primary',\n        width: 480,\n        onOk: function onOk() {\n          _this.processPurchase();\n        }\n      });\n    },\n    // 处理购买流程\n    processPurchase: function () {\n      var _processPurchase = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.purchaseLoading = true;\n                _context3.prev = 1;\n                _context3.next = 4;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 1500);\n                });\n\n              case 4:\n                this.$message.success('购买成功！'); // 通知父组件更新购买状态\n\n                this.$emit('purchase-success', this.agentDetail.id); // 关闭弹窗\n\n                this.handleClose();\n                _context3.next = 13;\n                break;\n\n              case 9:\n                _context3.prev = 9;\n                _context3.t0 = _context3[\"catch\"](1);\n                console.error('购买失败:', _context3.t0);\n\n                if (_context3.t0.response && _context3.t0.response.data && _context3.t0.response.data.message) {\n                  this.$message.error(_context3.t0.response.data.message);\n                } else {\n                  this.$message.error('购买失败，请稍后重试');\n                }\n\n              case 13:\n                _context3.prev = 13;\n                this.purchaseLoading = false;\n                return _context3.finish(13);\n\n              case 16:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[1, 9, 13, 16]]);\n      }));\n\n      function processPurchase() {\n        return _processPurchase.apply(this, arguments);\n      }\n\n      return processPurchase;\n    }(),\n    // 查看详情\n    handleViewDetail: function handleViewDetail() {\n      if (!this.isPurchased) {\n        this.$message.warning('请先购买该智能体后查看详情');\n        return;\n      } // TODO: 跳转到智能体详情页面\n      // 这里应该跳转到一个更详细的智能体介绍页面\n\n\n      this.$router.push({\n        path: '/agent/detail',\n        query: {\n          agentId: this.agentDetail.id,\n          agentName: this.agentDetail.agentName\n        }\n      });\n      console.log('🔗 跳转到智能体详情页面');\n      this.$message.success('正在跳转到详情页面...');\n    },\n    // 体验智能体\n    handleExperience: function handleExperience() {\n      if (!this.agentDetail.experienceLink) {\n        this.$message.warning('该智能体暂未提供体验链接');\n        return;\n      }\n\n      try {\n        // 在新窗口打开体验链接\n        window.open(this.agentDetail.experienceLink, '_blank');\n        console.log('🔗 打开体验链接:', this.agentDetail.experienceLink);\n        this.$message.success('正在打开体验页面...');\n      } catch (error) {\n        console.error('打开体验链接失败:', error);\n        this.$message.error('打开体验页面失败');\n      }\n    },\n    // 工作流下载\n    handleWorkflowDownload: function () {\n      var _handleWorkflowDownload = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4(workflow) {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                this.$set(this.downloadLoading, workflow.id, true);\n\n                try {\n                  // TODO: 跳转到下载页面\n                  this.$message.success('正在跳转到下载页面...');\n                } catch (error) {\n                  this.$message.error('下载失败');\n                } finally {\n                  this.$set(this.downloadLoading, workflow.id, false);\n                }\n\n              case 2:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function handleWorkflowDownload(_x) {\n        return _handleWorkflowDownload.apply(this, arguments);\n      }\n\n      return handleWorkflowDownload;\n    }(),\n    // 下载提示\n    handleDownloadTip: function handleDownloadTip() {\n      this.$message.warning('请先购买智能体后再下载工作流');\n    }\n  }, _defineProperty(_methods, \"handleWorkflowDownload\", function () {\n    var _handleWorkflowDownload2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(workflow) {\n      return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n        while (1) {\n          switch (_context5.prev = _context5.next) {\n            case 0:\n              console.log('下载工作流:', workflow); // 检查购买状态\n\n              if (this.isPurchased) {\n                _context5.next = 4;\n                break;\n              }\n\n              this.$message.warning('请先购买该智能体后再下载工作流');\n              return _context5.abrupt(\"return\");\n\n            case 4:\n              // 设置下载状态\n              this.$set(this.downloadLoading, workflow.id, true);\n              _context5.prev = 5;\n              _context5.next = 8;\n              return this.navigateToDownloadPage(workflow);\n\n            case 8:\n              _context5.next = 14;\n              break;\n\n            case 10:\n              _context5.prev = 10;\n              _context5.t0 = _context5[\"catch\"](5);\n              console.error('下载工作流失败:', _context5.t0);\n              this.$message.error('下载失败，请稍后重试');\n\n            case 14:\n              _context5.prev = 14;\n              this.$set(this.downloadLoading, workflow.id, false);\n              return _context5.finish(14);\n\n            case 17:\n            case \"end\":\n              return _context5.stop();\n          }\n        }\n      }, _callee5, this, [[5, 10, 14, 17]]);\n    }));\n\n    function handleWorkflowDownload(_x2) {\n      return _handleWorkflowDownload2.apply(this, arguments);\n    }\n\n    return handleWorkflowDownload;\n  }()), _defineProperty(_methods, \"navigateToDownloadPage\", function () {\n    var _navigateToDownloadPage = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(workflow) {\n      var downloadUrl;\n      return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n        while (1) {\n          switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.prev = 0;\n              _context6.next = 3;\n              return new Promise(function (resolve) {\n                return setTimeout(resolve, 800);\n              });\n\n            case 3:\n              downloadUrl = \"/workflow/download/\".concat(workflow.id, \"?agentId=\").concat(this.agentDetail.id); // 使用路由跳转\n\n              this.$router.push({\n                path: '/workflow/download',\n                query: {\n                  workflowId: workflow.id,\n                  agentId: this.agentDetail.id,\n                  workflowName: workflow.workflowName\n                }\n              });\n              console.log('🔗 跳转到工作流下载页面:', downloadUrl);\n              this.$message.success('正在跳转到下载页面...');\n              _context6.next = 13;\n              break;\n\n            case 9:\n              _context6.prev = 9;\n              _context6.t0 = _context6[\"catch\"](0);\n              console.error('跳转下载页面失败:', _context6.t0);\n              this.$message.error('跳转失败，请稍后重试');\n\n            case 13:\n            case \"end\":\n              return _context6.stop();\n          }\n        }\n      }, _callee6, this, [[0, 9]]);\n    }));\n\n    function navigateToDownloadPage(_x3) {\n      return _navigateToDownloadPage.apply(this, arguments);\n    }\n\n    return navigateToDownloadPage;\n  }()), _defineProperty(_methods, \"handleVideoLoadStart\", function handleVideoLoadStart() {\n    console.log('🎬 视频开始加载');\n    this.videoLoading = true;\n    this.videoError = false;\n  }), _defineProperty(_methods, \"handleVideoLoaded\", function handleVideoLoaded() {\n    console.log('🎬 视频加载完成');\n    this.videoLoading = false;\n    this.videoError = false;\n    var video = this.$refs.demoVideo;\n\n    if (video) {\n      this.duration = video.duration || 0;\n      this.videoVolume = Math.round(video.volume * 100); // 尝试自动播放（静音状态下）\n\n      if (this.videoAutoplay && this.videoMuted) {\n        this.playVideo();\n      }\n    }\n  }), _defineProperty(_methods, \"handleVideoError\", function handleVideoError(event) {\n    console.error('🎬 视频加载失败:', event);\n    this.videoLoading = false;\n    this.videoError = true;\n    this.videoPlaying = false;\n    this.$message.error('视频加载失败');\n  }), _defineProperty(_methods, \"handleVideoPlay\", function handleVideoPlay() {\n    console.log('🎬 视频开始播放');\n    this.videoPlaying = true;\n  }), _defineProperty(_methods, \"handleVideoPause\", function handleVideoPause() {\n    console.log('🎬 视频暂停播放');\n    this.videoPlaying = false;\n  }), _defineProperty(_methods, \"handleVideoTimeUpdate\", function handleVideoTimeUpdate() {\n    var video = this.$refs.demoVideo;\n\n    if (video && video.duration) {\n      this.currentTime = video.currentTime;\n      this.progressPercent = video.currentTime / video.duration * 100;\n    }\n  }), _defineProperty(_methods, \"handleVolumeChange\", function handleVolumeChange() {\n    var video = this.$refs.demoVideo;\n\n    if (video) {\n      this.videoVolume = Math.round(video.volume * 100);\n      this.videoMuted = video.muted;\n    }\n  }), _defineProperty(_methods, \"toggleVideoPlay\", function toggleVideoPlay() {\n    var video = this.$refs.demoVideo;\n    if (!video) return;\n\n    if (this.videoPlaying) {\n      video.pause();\n    } else {\n      this.playVideo();\n    }\n  }), _defineProperty(_methods, \"playVideo\", function () {\n    var _playVideo = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7() {\n      var video;\n      return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n        while (1) {\n          switch (_context7.prev = _context7.next) {\n            case 0:\n              video = this.$refs.demoVideo;\n\n              if (video) {\n                _context7.next = 3;\n                break;\n              }\n\n              return _context7.abrupt(\"return\");\n\n            case 3:\n              _context7.prev = 3;\n              _context7.next = 6;\n              return video.play();\n\n            case 6:\n              console.log('🎬 视频播放成功');\n              _context7.next = 25;\n              break;\n\n            case 9:\n              _context7.prev = 9;\n              _context7.t0 = _context7[\"catch\"](3);\n              console.error('🎬 视频播放失败:', _context7.t0); // 如果自动播放失败，可能是因为浏览器策略，尝试静音播放\n\n              if (this.videoMuted) {\n                _context7.next = 25;\n                break;\n              }\n\n              this.videoMuted = true;\n              video.muted = true;\n              _context7.prev = 15;\n              _context7.next = 18;\n              return video.play();\n\n            case 18:\n              this.$message.info('视频已静音播放，点击音量按钮开启声音');\n              _context7.next = 25;\n              break;\n\n            case 21:\n              _context7.prev = 21;\n              _context7.t1 = _context7[\"catch\"](15);\n              console.error('🎬 静音播放也失败:', _context7.t1);\n              this.$message.warning('视频播放失败，请手动点击播放');\n\n            case 25:\n            case \"end\":\n              return _context7.stop();\n          }\n        }\n      }, _callee7, this, [[3, 9], [15, 21]]);\n    }));\n\n    function playVideo() {\n      return _playVideo.apply(this, arguments);\n    }\n\n    return playVideo;\n  }()), _defineProperty(_methods, \"toggleMute\", function toggleMute() {\n    var video = this.$refs.demoVideo;\n    if (!video) return;\n    this.videoMuted = !this.videoMuted;\n    video.muted = this.videoMuted;\n    console.log('🎬 切换静音状态:', this.videoMuted ? '静音' : '有声');\n  }), _defineProperty(_methods, \"handleProgressClick\", function handleProgressClick(event) {\n    var video = this.$refs.demoVideo;\n    if (!video || !video.duration) return;\n    var progressContainer = event.currentTarget;\n    var rect = progressContainer.getBoundingClientRect();\n    var clickX = event.clientX - rect.left;\n    var progressWidth = rect.width;\n    var clickPercent = clickX / progressWidth;\n    var newTime = clickPercent * video.duration;\n    video.currentTime = newTime;\n    console.log('🎬 跳转到时间:', this.formatTime(newTime));\n  }), _defineProperty(_methods, \"handleVolumeSliderChange\", function handleVolumeSliderChange(value) {\n    var video = this.$refs.demoVideo;\n    if (!video) return;\n    this.videoVolume = value;\n    video.volume = value / 100; // 如果音量大于0，自动取消静音\n\n    if (value > 0 && this.videoMuted) {\n      this.videoMuted = false;\n      video.muted = false;\n    }\n\n    console.log('🎬 调整音量:', value + '%');\n  }), _defineProperty(_methods, \"toggleFullscreen\", function toggleFullscreen() {\n    var video = this.$refs.demoVideo;\n    if (!video) return;\n\n    if (video.requestFullscreen) {\n      video.requestFullscreen();\n    } else if (video.webkitRequestFullscreen) {\n      video.webkitRequestFullscreen();\n    } else if (video.mozRequestFullScreen) {\n      video.mozRequestFullScreen();\n    } else if (video.msRequestFullscreen) {\n      video.msRequestFullscreen();\n    }\n\n    console.log('🎬 切换全屏模式');\n  }), _defineProperty(_methods, \"retryVideoLoad\", function retryVideoLoad() {\n    console.log('🎬 重新加载视频');\n    this.videoError = false;\n    this.videoLoading = true;\n    var video = this.$refs.demoVideo;\n\n    if (video) {\n      video.load();\n    }\n  }), _defineProperty(_methods, \"formatTime\", function formatTime(seconds) {\n    if (!seconds || isNaN(seconds)) return '00:00';\n    var minutes = Math.floor(seconds / 60);\n    var remainingSeconds = Math.floor(seconds % 60);\n    return \"\".concat(minutes.toString().padStart(2, '0'), \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n  }), _defineProperty(_methods, \"handleImageError\", function handleImageError(event) {\n    event.target.style.display = 'none';\n  }), _defineProperty(_methods, \"handleCreatorAvatarError\", function handleCreatorAvatarError(event) {\n    event.target.style.display = 'none';\n  }), _defineProperty(_methods, \"handleWorkflowImageError\", function handleWorkflowImageError(event) {\n    event.target.style.display = 'none';\n  }), _methods)\n};", {"version": 3, "sources": ["AgentDetailModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0UA,eAAA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KALA;AASA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA;AATA,GAFA;AAgBA,EAAA,IAhBA,kBAgBA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,eAAA,EAAA,KAFA;AAGA,MAAA,eAAA,EAAA,KAHA;AAIA,MAAA,eAAA,EAAA,EAJA;AAIA;AACA,MAAA,WAAA,EAAA,EALA;AAKA;AACA,MAAA,YAAA,EAAA,EANA;AAMA;AAEA;AACA,MAAA,YAAA,EAAA,KATA;AAUA,MAAA,UAAA,EAAA,KAVA;AAWA,MAAA,YAAA,EAAA,KAXA;AAYA,MAAA,UAAA,EAAA,IAZA;AAYA;AACA,MAAA,aAAA,EAAA,IAbA;AAcA,MAAA,WAAA,EAAA,CAdA;AAeA,MAAA,QAAA,EAAA,CAfA;AAgBA,MAAA,WAAA,EAAA,EAhBA;AAiBA,MAAA,YAAA,EAAA,IAjBA;AAkBA,MAAA,gBAAA,EAAA,KAlBA;AAmBA,MAAA,eAAA,EAAA;AAnBA,KAAA;AAqBA,GAtCA;AAuCA,EAAA,QAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AACA,UAAA,KAAA,WAAA,CAAA,WAAA,EAAA;AACA;AACA,eAAA,KAAA,WAAA,CAAA,WAAA,CAAA,QAAA,IAAA,KAAA,WAAA,CAAA,WAAA,CAAA,IAAA,IAAA,OAAA;AACA;;AACA,aAAA,OAAA;AACA,KARA;AAUA;AACA,IAAA,cAXA,4BAWA;AACA,UAAA,KAAA,WAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,eAAA,IAAA;AACA,OAFA,MAEA,IAAA,KAAA,WAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,eAAA,KAAA;AACA;;AACA,aAAA,IAAA;AACA,KAlBA;AAoBA;AACA,IAAA,UArBA,wBAqBA;AACA,UAAA,CAAA,KAAA,WAAA,EAAA,OAAA,CAAA;AACA,UAAA,KAAA,WAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAFA,CAIA;;AACA,UAAA,aAAA,GAAA,UAAA,CAAA,KAAA,WAAA,CAAA,KAAA,IAAA,KAAA,WAAA,CAAA,aAAA,CAAA,IAAA,CAAA;AACA,UAAA,aAAA,GAAA,UAAA,CAAA,KAAA,WAAA,CAAA,aAAA,CAAA,IAAA,aAAA;AAEA,aAAA,KAAA,WAAA,CAAA,iBAAA,GAAA,aAAA,GAAA,aAAA;AACA;AA9BA,GAvCA;AAuEA,EAAA,KAAA,EAAA;AACA,IAAA,OADA,mBACA,MADA,EACA;AACA,UAAA,MAAA,IAAA,KAAA,OAAA,EAAA;AACA,aAAA,eAAA;AACA,aAAA,gBAAA;AACA,OAHA,MAGA;AACA,aAAA,SAAA;AACA;AACA;AARA,GAvEA;AAiFA,EAAA,OAAA;AACA;AACA,IAAA,eAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAA,OAAA,GAAA,IAAA;AAHA;AAAA;AAAA,uBASA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,yBAAA,UAAA,CAAA,OAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CATA;;AAAA;AAWA;AACA,gBAAA,SAZA,GAYA,KAAA,OAAA,CAAA,aAAA,IAAA,EAZA;AAcA,qBAAA,WAAA,GAAA;AACA,kBAAA,EAAA,EAAA,KAAA,OADA;AAEA,kBAAA,SAAA,EAAA,SAAA,CAAA,SAAA,IAAA,OAFA;AAGA,kBAAA,gBAAA,EAAA,SAAA,CAAA,gBAAA,IAAA,SAAA,CAAA,WAAA,IAAA,kBAHA;AAIA,kBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,kBAAA,SAAA,EAAA,SAAA,CAAA,SAAA,IAAA,EALA;AAMA,kBAAA,cAAA,EAAA,SAAA,CAAA,cAAA,IAAA,qBANA;AAOA,kBAAA,KAAA,EAAA,SAAA,CAAA,KAAA,IAAA,EAPA;AAQA,kBAAA,aAAA,EAAA,SAAA,CAAA,aAAA,IAAA,SAAA,CAAA,KAAA,IAAA,EARA;AASA,kBAAA,UAAA,EAAA,SAAA,CAAA,UAAA,IAAA,GATA;AAUA;AACA,kBAAA,aAAA,EAAA,SAAA,CAAA,aAAA,IAAA,KAXA;AAYA,kBAAA,iBAAA,EAAA,SAAA,CAAA,iBAAA,IAAA,KAZA;AAaA,kBAAA,YAAA,EAAA,SAAA,CAAA,YAAA,IAAA,CAbA;AAcA,kBAAA,MAAA,EAAA,SAAA,CAAA,MAAA,IAAA,KAdA;AAeA,kBAAA,WAAA,EAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,QAAA,EAAA,QAFA;AAGA,oBAAA,MAAA,EAAA;AAHA;AAfA,iBAAA;AAsBA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,KAAA,WAAA;AApCA;AAAA;;AAAA;AAAA;AAAA;AAsCA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,EAvCA,CAwCA;;AACA,qBAAA,WAAA,GAAA;AACA,kBAAA,EAAA,EAAA,KAAA,OADA;AAEA,kBAAA,SAAA,EAAA,MAFA;AAGA,kBAAA,gBAAA,EAAA,WAHA;AAIA,kBAAA,WAAA,EAAA,EAJA;AAKA,kBAAA,SAAA,EAAA,EALA;AAMA,kBAAA,cAAA,EAAA,EANA;AAOA,kBAAA,KAAA,EAAA,CAPA;AAQA,kBAAA,aAAA,EAAA,CARA;AASA,kBAAA,UAAA,EAAA,GATA;AAUA,kBAAA,aAAA,EAAA,KAVA;AAWA,kBAAA,iBAAA,EAAA,KAXA;AAYA,kBAAA,YAAA,EAAA,CAZA;AAaA,kBAAA,MAAA,EAAA,KAbA;AAcA,kBAAA,WAAA,EAAA;AACA,oBAAA,IAAA,EAAA,IADA;AAEA,oBAAA,QAAA,EAAA,IAFA;AAGA,oBAAA,MAAA,EAAA;AAHA;AAdA,iBAAA;;AAzCA;AAAA;AA8DA,qBAAA,OAAA,GAAA,KAAA;AA9DA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkEA;AACA,IAAA,gBAnEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoEA,qBAAA,eAAA,GAAA,IAAA;AApEA;AAAA;AAAA,uBA0EA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,yBAAA,UAAA,CAAA,OAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CA1EA;;AAAA;AA4EA,gBAAA,SA5EA,GA4EA,KAAA,OAAA,CAAA,aAAA,IAAA,EA5EA;AA6EA,gBAAA,SA7EA,GA6EA,SAAA,CAAA,SAAA,IAAA,KA7EA,EA+EA;;AACA,oBAAA,SAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,uBAAA,YAAA,GAAA,CACA;AACA,oBAAA,EAAA,EAAA,GADA;AAEA,oBAAA,YAAA,YAAA,SAAA,4CAFA;AAGA,oBAAA,mBAAA,EAAA,mBAHA;AAIA,oBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,oBAAA,QAAA,EAAA;AALA,mBADA,EAQA;AACA,oBAAA,EAAA,EAAA,GADA;AAEA,oBAAA,YAAA,YAAA,SAAA,4CAFA;AAGA,oBAAA,mBAAA,EAAA,qBAHA;AAIA,oBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,oBAAA,QAAA,EAAA;AALA,mBARA,EAeA;AACA,oBAAA,EAAA,EAAA,GADA;AAEA,oBAAA,YAAA,YAAA,SAAA,4CAFA;AAGA,oBAAA,mBAAA,EAAA,qBAHA;AAIA,oBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,oBAAA,QAAA,EAAA;AALA,mBAfA,CAAA;AAuBA,iBAzBA,MAyBA;AACA;AACA,uBAAA,YAAA,GAAA,CACA;AACA,oBAAA,EAAA,EAAA,GADA;AAEA,oBAAA,YAAA,YAAA,SAAA,4CAFA;AAGA,oBAAA,mBAAA,EAAA,iBAHA;AAIA,oBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,oBAAA,QAAA,EAAA;AALA,mBADA,EAQA;AACA,oBAAA,EAAA,EAAA,GADA;AAEA,oBAAA,YAAA,YAAA,SAAA,4CAFA;AAGA,oBAAA,mBAAA,EAAA,kBAHA;AAIA,oBAAA,WAAA,EAAA,SAAA,CAAA,WAAA,IAAA,EAJA;AAKA,oBAAA,QAAA,EAAA;AALA,mBARA,CAAA;AAgBA;;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,KAAA,YAAA,CAAA,MAAA,EAAA,GAAA;AA7HA;AAAA;;AAAA;AAAA;AAAA;AA+HA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AAhIA;AAAA;AAkIA,qBAAA,eAAA,GAAA,KAAA;AAlIA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsIA;AACA,IAAA,SAvIA,uBAuIA;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA,CAHA,CAKA;;AACA,WAAA,YAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA;AACA,WAAA,eAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,KAAA,CAZA,CAcA;;AACA,UAAA,KAAA,KAAA,CAAA,SAAA,EAAA;AACA,aAAA,KAAA,CAAA,SAAA,CAAA,KAAA;AACA,aAAA,KAAA,CAAA,SAAA,CAAA,WAAA,GAAA,CAAA;AACA;AACA,KA1JA;AA4JA;AACA,IAAA,WA7JA,yBA6JA;AACA,WAAA,KAAA,CAAA,OAAA;AACA,KA/JA;AAiKA;AACA,IAAA,cAlKA,4BAkKA;AACA,WAAA,mBAAA;AACA,KApKA;AAsKA;AACA,IAAA,mBAvKA,iCAuKA;AAAA;;AAAA;AACA,UAAA,KAAA,GAAA,KAAA,WAAA;AACA,UAAA,UAAA,GAAA,KAAA,UAAA;AAEA,WAAA,QAAA,CAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA;AAAA;AAAA,qBACA;AADA;AAAA,qBAEA;AAFA;AAAA;AAAA,qBAIA,KAAA,CAAA,WAAA,IAAA,EAJA;AAAA,qBAKA,KAAA,CAAA;AALA;AAAA,qBAMA,uFANA;AAAA;AAAA,uBAOA,eAAA,CAAA,EAAA;AAAA,gBAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AAAA;AAPA;AAAA;AAAA,qBAUA;AAVA,cAUA,KAAA,CAAA,SAVA;AAAA,qBAWA;AAXA,cAYA,KAAA,CAAA,UAAA,KAAA,GAAA,GAAA,OAAA,GAAA,QAZA;AAAA,qBAiBA;AAjBA;AAAA,qBAkBA;AAlBA;AAAA,qBAmBA;AAnBA,4DAqBA,KAAA,CAAA,MAAA;AAAA,qBACA;AADA,iCAEA,KAAA,CAAA,iBAAA;AAAA,qBAEA;AAFA,sBAEA,UAFA;AAAA,qBAGA;AAHA,sBAIA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,aAAA,IAAA,CAJA;AAAA,qBAQA;AARA,sBAQA,KAAA,CAAA,KAAA,IAAA,KAAA,CAAA,aAAA,IAAA,CARA,EAvBA;AAAA,qBAqCA;AArCA;AAAA,qBAuCA;AAvCA;AAAA,SAFA;AAkDA,QAAA,MAAA,EAAA,MAlDA;AAmDA,QAAA,UAAA,EAAA,IAnDA;AAoDA,QAAA,MAAA,EAAA,SApDA;AAqDA,QAAA,KAAA,EAAA,GArDA;AAsDA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,KAAA,CAAA,eAAA;AACA;AAxDA,OAAA;AA0DA,KArOA;AAuOA;AACA,IAAA,eAxOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyOA,qBAAA,eAAA,GAAA,IAAA;AAzOA;AAAA;AAAA,uBAyPA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,yBAAA,UAAA,CAAA,OAAA,EAAA,IAAA,CAAA;AAAA,iBAAA,CAzPA;;AAAA;AA2PA,qBAAA,QAAA,CAAA,OAAA,CAAA,OAAA,EA3PA,CA6PA;;AACA,qBAAA,KAAA,CAAA,kBAAA,EAAA,KAAA,WAAA,CAAA,EAAA,EA9PA,CAgQA;;AACA,qBAAA,WAAA;AAjQA;AAAA;;AAAA;AAAA;AAAA;AAoQA,gBAAA,OAAA,CAAA,KAAA,CAAA,OAAA;;AACA,oBAAA,aAAA,QAAA,IAAA,aAAA,QAAA,CAAA,IAAA,IAAA,aAAA,QAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,aAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,iBAFA,MAEA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA;;AAzQA;AAAA;AA2QA,qBAAA,eAAA,GAAA,KAAA;AA3QA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+QA;AACA,IAAA,gBAhRA,8BAgRA;AACA,UAAA,CAAA,KAAA,WAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA;AACA,OAJA,CAMA;AACA;;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,eADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,OAAA,EAAA,KAAA,WAAA,CAAA,EADA;AAEA,UAAA,SAAA,EAAA,KAAA,WAAA,CAAA;AAFA;AAFA,OAAA;AAQA,MAAA,OAAA,CAAA,GAAA,CAAA,eAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,cAAA;AACA,KAlSA;AAoSA;AACA,IAAA,gBArSA,8BAqSA;AACA,UAAA,CAAA,KAAA,WAAA,CAAA,cAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,cAAA;AACA;AACA;;AAEA,UAAA;AACA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,CAAA,cAAA,EAAA,QAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,WAAA,CAAA,cAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA,OALA,CAKA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,KApTA;AAsTA;AACA,IAAA,sBAvTA;AAAA,+GAuTA,QAvTA;AAAA;AAAA;AAAA;AAAA;AAwTA,qBAAA,IAAA,CAAA,KAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,IAAA;;AACA,oBAAA;AACA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,cAAA;AACA,iBAHA,CAGA,OAAA,KAAA,EAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA,iBALA,SAKA;AACA,uBAAA,IAAA,CAAA,KAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,KAAA;AACA;;AAhUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmUA;AACA,IAAA,iBApUA,+BAoUA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,gBAAA;AACA;AAtUA;AAAA,8GAyUA,QAzUA;AAAA;AAAA;AAAA;AAAA;AA0UA,cAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,QAAA,EA1UA,CA4UA;;AA5UA,kBA6UA,KAAA,WA7UA;AAAA;AAAA;AAAA;;AA8UA,mBAAA,QAAA,CAAA,OAAA,CAAA,iBAAA;AA9UA;;AAAA;AAkVA;AACA,mBAAA,IAAA,CAAA,KAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,IAAA;AAnVA;AAAA;AAAA,qBAuVA,KAAA,sBAAA,CAAA,QAAA,CAvVA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAyVA,cAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,mBAAA,QAAA,CAAA,KAAA,CAAA,YAAA;;AA1VA;AAAA;AA4VA,mBAAA,IAAA,CAAA,KAAA,eAAA,EAAA,QAAA,CAAA,EAAA,EAAA,KAAA;AA5VA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,6GAiWA,QAjWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAuWA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,uBAAA,UAAA,CAAA,OAAA,EAAA,GAAA,CAAA;AAAA,eAAA,CAvWA;;AAAA;AAyWA,cAAA,WAzWA,gCAyWA,QAAA,CAAA,EAzWA,sBAyWA,KAAA,WAAA,CAAA,EAzWA,GA2WA;;AACA,mBAAA,OAAA,CAAA,IAAA,CAAA;AACA,gBAAA,IAAA,EAAA,oBADA;AAEA,gBAAA,KAAA,EAAA;AACA,kBAAA,UAAA,EAAA,QAAA,CAAA,EADA;AAEA,kBAAA,OAAA,EAAA,KAAA,WAAA,CAAA,EAFA;AAGA,kBAAA,YAAA,EAAA,QAAA,CAAA;AAHA;AAFA,eAAA;AASA,cAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,WAAA;AACA,mBAAA,QAAA,CAAA,OAAA,CAAA,cAAA;AAtXA;AAAA;;AAAA;AAAA;AAAA;AAyXA,cAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,mBAAA,QAAA,CAAA,KAAA,CAAA,YAAA;;AA1XA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,0FAiYA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,SAAA,YAAA,GAAA,IAAA;AACA,SAAA,UAAA,GAAA,KAAA;AACA,GArYA,+EAwYA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,SAAA,YAAA,GAAA,KAAA;AACA,SAAA,UAAA,GAAA,KAAA;AAEA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;;AACA,QAAA,KAAA,EAAA;AACA,WAAA,QAAA,GAAA,KAAA,CAAA,QAAA,IAAA,CAAA;AACA,WAAA,WAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA,CAFA,CAIA;;AACA,UAAA,KAAA,aAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,SAAA;AACA;AACA;AACA,GAvZA,2EA0ZA,KA1ZA,EA0ZA;AACA,IAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA,KAAA;AACA,SAAA,YAAA,GAAA,KAAA;AACA,SAAA,UAAA,GAAA,IAAA;AACA,SAAA,YAAA,GAAA,KAAA;AACA,SAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,GAhaA,2EAmaA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,SAAA,YAAA,GAAA,IAAA;AACA,GAtaA,6EAyaA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,SAAA,YAAA,GAAA,KAAA;AACA,GA5aA,uFA+aA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;;AACA,QAAA,KAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,WAAA;AACA,WAAA,eAAA,GAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,QAAA,GAAA,GAAA;AACA;AACA,GArbA,iFAwbA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;;AACA,QAAA,KAAA,EAAA;AACA,WAAA,WAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAAA,KAAA;AACA;AACA,GA9bA,2EAicA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,QAAA,CAAA,KAAA,EAAA;;AAEA,QAAA,KAAA,YAAA,EAAA;AACA,MAAA,KAAA,CAAA,KAAA;AACA,KAFA,MAEA;AACA,WAAA,SAAA;AACA;AACA,GA1cA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8cA,cAAA,KA9cA,GA8cA,KAAA,KAAA,CAAA,SA9cA;;AAAA,kBA+cA,KA/cA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAAA;AAAA,qBAkdA,KAAA,CAAA,IAAA,EAldA;;AAAA;AAmdA,cAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AAndA;AAAA;;AAAA;AAAA;AAAA;AAqdA,cAAA,OAAA,CAAA,KAAA,CAAA,YAAA,gBArdA,CAsdA;;AAtdA,kBAudA,KAAA,UAvdA;AAAA;AAAA;AAAA;;AAwdA,mBAAA,UAAA,GAAA,IAAA;AACA,cAAA,KAAA,CAAA,KAAA,GAAA,IAAA;AAzdA;AAAA;AAAA,qBA2dA,KAAA,CAAA,IAAA,EA3dA;;AAAA;AA4dA,mBAAA,QAAA,CAAA,IAAA,CAAA,oBAAA;AA5dA;AAAA;;AAAA;AAAA;AAAA;AA8dA,cAAA,OAAA,CAAA,KAAA,CAAA,aAAA;AACA,mBAAA,QAAA,CAAA,OAAA,CAAA,gBAAA;;AA/dA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sEAseA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,QAAA,CAAA,KAAA,EAAA;AAEA,SAAA,UAAA,GAAA,CAAA,KAAA,UAAA;AACA,IAAA,KAAA,CAAA,KAAA,GAAA,KAAA,UAAA;AAEA,IAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,UAAA,GAAA,IAAA,GAAA,IAAA;AACA,GA9eA,iFAifA,KAjfA,EAifA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,QAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AAEA,QAAA,iBAAA,GAAA,KAAA,CAAA,aAAA;AACA,QAAA,IAAA,GAAA,iBAAA,CAAA,qBAAA,EAAA;AACA,QAAA,MAAA,GAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA,IAAA;AACA,QAAA,aAAA,GAAA,IAAA,CAAA,KAAA;AACA,QAAA,YAAA,GAAA,MAAA,GAAA,aAAA;AAEA,QAAA,OAAA,GAAA,YAAA,GAAA,KAAA,CAAA,QAAA;AACA,IAAA,KAAA,CAAA,WAAA,GAAA,OAAA;AAEA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,UAAA,CAAA,OAAA,CAAA;AACA,GA/fA,2FAkgBA,KAlgBA,EAkgBA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,QAAA,CAAA,KAAA,EAAA;AAEA,SAAA,WAAA,GAAA,KAAA;AACA,IAAA,KAAA,CAAA,MAAA,GAAA,KAAA,GAAA,GAAA,CALA,CAOA;;AACA,QAAA,KAAA,GAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,MAAA,KAAA,CAAA,KAAA,GAAA,KAAA;AACA;;AAEA,IAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA,GAAA,GAAA;AACA,GAhhBA,6EAmhBA;AACA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,QAAA,CAAA,KAAA,EAAA;;AAEA,QAAA,KAAA,CAAA,iBAAA,EAAA;AACA,MAAA,KAAA,CAAA,iBAAA;AACA,KAFA,MAEA,IAAA,KAAA,CAAA,uBAAA,EAAA;AACA,MAAA,KAAA,CAAA,uBAAA;AACA,KAFA,MAEA,IAAA,KAAA,CAAA,oBAAA,EAAA;AACA,MAAA,KAAA,CAAA,oBAAA;AACA,KAFA,MAEA,IAAA,KAAA,CAAA,mBAAA,EAAA;AACA,MAAA,KAAA,CAAA,mBAAA;AACA;;AAEA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,GAliBA,yEAqiBA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,SAAA,UAAA,GAAA,KAAA;AACA,SAAA,YAAA,GAAA,IAAA;AAEA,QAAA,KAAA,GAAA,KAAA,KAAA,CAAA,SAAA;;AACA,QAAA,KAAA,EAAA;AACA,MAAA,KAAA,CAAA,IAAA;AACA;AACA,GA9iBA,+DAijBA,OAjjBA,EAijBA;AACA,QAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,EAAA,OAAA,OAAA;AAEA,QAAA,OAAA,GAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,EAAA,CAAA;AACA,QAAA,gBAAA,GAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,EAAA,CAAA;AAEA,qBAAA,OAAA,CAAA,QAAA,GAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,cAAA,gBAAA,CAAA,QAAA,GAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,GAxjBA,2EA2jBA,KA3jBA,EA2jBA;AACA,IAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,GA7jBA,2FAgkBA,KAhkBA,EAgkBA;AACA,IAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,GAlkBA,2FAqkBA,KArkBA,EAqkBA;AACA,IAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,GAvkBA;AAjFA,CAAA", "sourcesContent": ["<template>\n  <a-modal\n    :visible=\"visible\"\n    :width=\"1200\"\n    :footer=\"null\"\n    :closable=\"false\"\n    :maskClosable=\"true\"\n    @cancel=\"handleClose\"\n    class=\"agent-detail-modal custom-modal\"\n    :bodyStyle=\"{ padding: 0, borderRadius: '16px', overflow: 'hidden' }\"\n    :centered=\"true\"\n    :destroyOnClose=\"true\"\n  >\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <a-spin size=\"large\" tip=\"加载中...\">\n        <div class=\"loading-placeholder\"></div>\n      </a-spin>\n    </div>\n\n    <!-- 主要内容 -->\n    <div v-else class=\"modal-content\">\n      <!-- 自定义关闭按钮 -->\n      <div class=\"custom-close-button\" @click=\"handleClose\">\n        <a-icon type=\"close\" />\n      </div>\n\n      <!-- 背景装饰 -->\n      <div class=\"modal-background\">\n        <div class=\"bg-pattern\"></div>\n        <div class=\"bg-gradient\"></div>\n      </div>\n      <!-- 1. 智能体基本信息区域 -->\n      <div class=\"agent-info-section\">\n        <div class=\"agent-header\">\n          <!-- 智能体头像 -->\n          <div class=\"agent-avatar\">\n            <img\n              v-if=\"agentDetail.agentAvatar\"\n              :src=\"agentDetail.agentAvatar\"\n              :alt=\"agentDetail.agentName\"\n              @error=\"handleImageError\"\n            />\n            <div v-else class=\"avatar-placeholder\">\n              <a-icon type=\"robot\" />\n            </div>\n          </div>\n\n          <!-- 智能体基本信息 -->\n          <div class=\"agent-basic-info\">\n            <h2 class=\"agent-name\">{{ agentDetail.agentName }}</h2>\n            <p class=\"agent-description\">{{ agentDetail.agentDescription }}</p>\n            \n            <!-- 创作者信息 -->\n            <div class=\"creator-info\">\n              <div class=\"creator-avatar\">\n                <img\n                  v-if=\"agentDetail.creatorInfo && agentDetail.creatorInfo.avatar\"\n                  :src=\"agentDetail.creatorInfo.avatar\"\n                  :alt=\"agentDetail.creatorInfo.name\"\n                  @error=\"handleCreatorAvatarError\"\n                />\n                <a-icon v-else type=\"user\" />\n              </div>\n              <div class=\"creator-details\">\n                <span class=\"creator-name\">{{ creatorName }}</span>\n                <span class=\"creator-type\">{{ authorTypeText }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格信息 -->\n          <div class=\"price-section\">\n            <div v-if=\"agentDetail.isFree\" class=\"price-container\">\n              <span class=\"free-price\">免费</span>\n            </div>\n            <div v-else-if=\"agentDetail.showDiscountPrice\" class=\"price-container\">\n              <span class=\"discount-price\">¥{{ finalPrice }}</span>\n              <span class=\"original-price\">¥{{ agentDetail.price || agentDetail.originalPrice || 0 }}</span>\n            </div>\n            <div v-else class=\"price-container\">\n              <span class=\"current-price\">¥{{ agentDetail.price || agentDetail.originalPrice || 0 }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 2. 演示视频区域 -->\n      <div v-if=\"agentDetail.demoVideo\" class=\"demo-video-section\">\n        <h3 class=\"section-title\">\n          <a-icon type=\"play-circle\" />\n          演示视频\n        </h3>\n        <div class=\"video-container\">\n          <div class=\"video-wrapper\" v-if=\"!videoError\">\n            <video\n              ref=\"demoVideo\"\n              :src=\"agentDetail.demoVideo\"\n              class=\"demo-video\"\n              :muted=\"videoMuted\"\n              :autoplay=\"videoAutoplay\"\n              preload=\"metadata\"\n              @loadstart=\"handleVideoLoadStart\"\n              @loadeddata=\"handleVideoLoaded\"\n              @error=\"handleVideoError\"\n              @play=\"handleVideoPlay\"\n              @pause=\"handleVideoPause\"\n              @timeupdate=\"handleVideoTimeUpdate\"\n              @volumechange=\"handleVolumeChange\"\n              @click=\"toggleVideoPlay\"\n            >\n              您的浏览器不支持视频播放\n            </video>\n\n            <!-- 自定义视频控制栏 -->\n            <div class=\"video-controls\" v-show=\"showControls\">\n              <div class=\"controls-left\">\n                <!-- 播放/暂停按钮 -->\n                <a-button\n                  type=\"link\"\n                  :icon=\"videoPlaying ? 'pause' : 'caret-right'\"\n                  @click=\"toggleVideoPlay\"\n                  class=\"control-btn play-btn\"\n                />\n\n                <!-- 时间显示 -->\n                <span class=\"time-display\">\n                  {{ formatTime(currentTime) }} / {{ formatTime(duration) }}\n                </span>\n              </div>\n\n              <div class=\"controls-center\">\n                <!-- 进度条 -->\n                <div class=\"progress-container\" @click=\"handleProgressClick\">\n                  <div class=\"progress-bar\">\n                    <div\n                      class=\"progress-filled\"\n                      :style=\"{ width: progressPercent + '%' }\"\n                    ></div>\n                    <div\n                      class=\"progress-thumb\"\n                      :style=\"{ left: progressPercent + '%' }\"\n                    ></div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"controls-right\">\n                <!-- 音量控制 -->\n                <div class=\"volume-control\" @mouseenter=\"showVolumeSlider = true\" @mouseleave=\"showVolumeSlider = false\">\n                  <a-button\n                    type=\"link\"\n                    :icon=\"videoMuted ? 'sound' : 'sound-filled'\"\n                    @click=\"toggleMute\"\n                    class=\"control-btn volume-btn\"\n                  />\n                  <div class=\"volume-slider\" v-show=\"showVolumeSlider\">\n                    <a-slider\n                      v-model=\"videoVolume\"\n                      :min=\"0\"\n                      :max=\"100\"\n                      :step=\"1\"\n                      vertical\n                      :tip-formatter=\"null\"\n                      @change=\"handleVolumeSliderChange\"\n                      class=\"volume-range\"\n                    />\n                  </div>\n                </div>\n\n                <!-- 全屏按钮 -->\n                <a-button\n                  type=\"link\"\n                  icon=\"fullscreen\"\n                  @click=\"toggleFullscreen\"\n                  class=\"control-btn fullscreen-btn\"\n                />\n              </div>\n            </div>\n\n            <!-- 视频加载状态 -->\n            <div class=\"video-loading\" v-show=\"videoLoading\">\n              <a-spin size=\"large\">\n                <a-icon slot=\"indicator\" type=\"loading\" style=\"font-size: 24px;\" spin />\n              </a-spin>\n              <p>视频加载中...</p>\n            </div>\n          </div>\n\n          <!-- 视频加载失败占位符 -->\n          <div class=\"video-error-placeholder\" v-if=\"videoError\">\n            <div class=\"error-content\">\n              <a-icon type=\"exclamation-circle\" class=\"error-icon\" />\n              <h4>视频加载失败</h4>\n              <p>抱歉，演示视频暂时无法播放</p>\n              <a-button @click=\"retryVideoLoad\" type=\"primary\" ghost>\n                <a-icon type=\"reload\" />\n                重新加载\n              </a-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 3. 工作流列表区域 -->\n      <div class=\"workflow-section\">\n        <h3 class=\"section-title\">\n          工作流列表\n          <span class=\"workflow-count\">({{ workflowList.length }}个)</span>\n        </h3>\n        \n        <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n          <a-spin tip=\"加载工作流中...\" />\n        </div>\n        \n        <div v-else-if=\"workflowList.length > 0\" class=\"workflow-list\">\n          <div\n            v-for=\"(workflow, index) in workflowList\"\n            :key=\"workflow.id\"\n            class=\"workflow-item\"\n          >\n            <div class=\"workflow-info\">\n              <div class=\"workflow-sequence\">{{ index + 1 }}</div>\n              <div class=\"workflow-avatar\">\n                <img\n                  v-if=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                  :src=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                  :alt=\"workflow.workflowName\"\n                  @error=\"handleWorkflowImageError\"\n                />\n                <a-icon v-else type=\"setting\" />\n              </div>\n              <div class=\"workflow-details\">\n                <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n                <p class=\"workflow-description\">{{ workflow.workflowDescription }}</p>\n              </div>\n            </div>\n            \n            <!-- 下载按钮 -->\n            <div class=\"workflow-actions\">\n              <a-button\n                v-if=\"!isPurchased\"\n                type=\"default\"\n                disabled\n                @click=\"handleDownloadTip\"\n              >\n                <a-icon type=\"download\" />\n                请先购买\n              </a-button>\n              <a-button\n                v-else\n                type=\"primary\"\n                @click=\"handleWorkflowDownload(workflow)\"\n                :loading=\"downloadLoading[workflow.id]\"\n              >\n                <a-icon type=\"download\" />\n                下载\n              </a-button>\n            </div>\n          </div>\n        </div>\n        \n        <div v-else class=\"workflow-empty\">\n          <a-empty description=\"暂无工作流\" />\n        </div>\n      </div>\n\n      <!-- 4. 底部操作按钮区域 -->\n      <div class=\"action-buttons modern-actions\">\n        <a-button @click=\"handleClose\" class=\"close-btn modern-btn-secondary\">\n          <a-icon type=\"close\" />\n          关闭\n        </a-button>\n\n        <div class=\"primary-actions\">\n          <a-button\n            v-if=\"isPurchased\"\n            type=\"default\"\n            @click=\"handleViewDetail\"\n            class=\"detail-btn modern-btn-outline\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n          <a-button\n            v-else\n            type=\"default\"\n            disabled\n            class=\"detail-btn modern-btn-outline disabled\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n\n          <a-button\n            v-if=\"!isPurchased\"\n            type=\"primary\"\n            @click=\"handlePurchase\"\n            :loading=\"purchaseLoading\"\n            class=\"purchase-btn modern-btn-primary\"\n          >\n            <a-icon type=\"shopping-cart\" />\n            立即购买\n          </a-button>\n\n          <a-button\n            v-if=\"agentDetail.experienceLink\"\n            type=\"default\"\n            @click=\"handleExperience\"\n            class=\"experience-btn modern-btn-outline\"\n          >\n            <a-icon type=\"play-circle\" />\n            体验智能体\n          </a-button>\n          <a-button\n            v-else\n            type=\"default\"\n            disabled\n            class=\"experience-btn modern-btn-outline disabled\"\n          >\n            <a-icon type=\"play-circle\" />\n            暂无体验\n          </a-button>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script>\nexport default {\n  name: 'AgentDetailModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    agentId: {\n      type: String,\n      default: ''\n    },\n    isPurchased: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      workflowLoading: false,\n      purchaseLoading: false,\n      downloadLoading: {}, // 工作流下载loading状态\n      agentDetail: {}, // 智能体详情\n      workflowList: [], // 工作流列表\n\n      // 视频播放相关状态\n      videoLoading: false,\n      videoError: false,\n      videoPlaying: false,\n      videoMuted: true, // 默认静音以支持自动播放\n      videoAutoplay: true,\n      currentTime: 0,\n      duration: 0,\n      videoVolume: 50,\n      showControls: true,\n      showVolumeSlider: false,\n      progressPercent: 0\n    }\n  },\n  computed: {\n    // 创作者名称\n    creatorName() {\n      if (this.agentDetail.creatorInfo) {\n        // 优先显示昵称，其次显示姓名\n        return this.agentDetail.creatorInfo.nickname || this.agentDetail.creatorInfo.name || '未知创作者'\n      }\n      return '未知创作者'\n    },\n\n    // 作者类型文本\n    authorTypeText() {\n      if (this.agentDetail.authorType === '1') {\n        return '官方'\n      } else if (this.agentDetail.authorType === '2') {\n        return '创作者'\n      }\n      return '未知'\n    },\n\n    // 最终价格（考虑折扣）\n    finalPrice() {\n      if (!this.agentDetail) return 0\n      if (this.agentDetail.isFree) return 0\n\n      // 安全地获取价格，确保返回数字\n      const originalPrice = parseFloat(this.agentDetail.price || this.agentDetail.originalPrice) || 0\n      const discountPrice = parseFloat(this.agentDetail.discountPrice) || originalPrice\n\n      return this.agentDetail.showDiscountPrice ? discountPrice : originalPrice\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal && this.agentId) {\n        this.loadAgentDetail()\n        this.loadWorkflowList()\n      } else {\n        this.resetData()\n      }\n    }\n  },\n  methods: {\n    // 加载智能体详情\n    async loadAgentDetail() {\n      this.loading = true\n      try {\n        // TODO: 调用后端API获取智能体详情\n        // const response = await this.$http.get(`/api/agent/detail/${this.agentId}`)\n\n        // 临时模拟数据 - 从父组件传入的agent数据中获取基本信息\n        await new Promise(resolve => setTimeout(resolve, 500))\n\n        // 如果有传入的agent数据，使用它作为基础\n        const baseAgent = this.$parent.selectedAgent || {}\n\n        this.agentDetail = {\n          id: this.agentId,\n          agentName: baseAgent.agentName || '示例智能体',\n          agentDescription: baseAgent.agentDescription || baseAgent.description || '这是一个示例智能体的详细描述信息',\n          agentAvatar: baseAgent.agentAvatar || '',\n          demoVideo: baseAgent.demoVideo || '',\n          experienceLink: baseAgent.experienceLink || 'https://example.com',\n          price: baseAgent.price || 99,\n          originalPrice: baseAgent.originalPrice || baseAgent.price || 99,\n          authorType: baseAgent.authorType || '1',\n          // 继承价格计算结果\n          showSvipPromo: baseAgent.showSvipPromo || false,\n          showDiscountPrice: baseAgent.showDiscountPrice || false,\n          discountRate: baseAgent.discountRate || 1,\n          isFree: baseAgent.isFree || false,\n          creatorInfo: {\n            name: '官方团队',\n            nickname: '智界Aigc',\n            avatar: ''\n          }\n        }\n\n        console.log('🔍 AgentDetailModal: 加载智能体详情', this.agentDetail)\n      } catch (error) {\n        console.error('加载智能体详情失败:', error)\n        this.$message.error('加载智能体详情失败')\n        // 设置默认数据防止页面崩溃\n        this.agentDetail = {\n          id: this.agentId,\n          agentName: '加载失败',\n          agentDescription: '智能体详情加载失败',\n          agentAvatar: '',\n          demoVideo: '',\n          experienceLink: '',\n          price: 0,\n          originalPrice: 0,\n          authorType: '2',\n          showSvipPromo: false,\n          showDiscountPrice: false,\n          discountRate: 0,\n          isFree: false,\n          creatorInfo: {\n            name: '未知',\n            nickname: '未知',\n            avatar: ''\n          }\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载工作流列表\n    async loadWorkflowList() {\n      this.workflowLoading = true\n      try {\n        // TODO: 调用后端API获取工作流列表\n        // const response = await this.$http.get(`/api/agent/${this.agentId}/workflows`)\n\n        // 临时模拟数据 - 根据智能体类型生成不同的工作流\n        await new Promise(resolve => setTimeout(resolve, 300))\n\n        const baseAgent = this.$parent.selectedAgent || {}\n        const agentName = baseAgent.agentName || '智能体'\n\n        // 根据智能体类型生成相应的工作流\n        if (baseAgent.authorType === '1') {\n          // 官方智能体 - 更多工作流\n          this.workflowList = [\n            {\n              id: '1',\n              workflowName: `${agentName} - 基础对话流程`,\n              workflowDescription: '适用于日常对话和基础问答的工作流程',\n              agentAvatar: baseAgent.agentAvatar || '',\n              sequence: 1\n            },\n            {\n              id: '2',\n              workflowName: `${agentName} - 高级分析流程`,\n              workflowDescription: '用于复杂数据分析和深度思考的高级工作流',\n              agentAvatar: baseAgent.agentAvatar || '',\n              sequence: 2\n            },\n            {\n              id: '3',\n              workflowName: `${agentName} - 创意生成流程`,\n              workflowDescription: '专门用于创意内容生成和头脑风暴的工作流',\n              agentAvatar: baseAgent.agentAvatar || '',\n              sequence: 3\n            }\n          ]\n        } else {\n          // 创作者智能体 - 较少工作流\n          this.workflowList = [\n            {\n              id: '1',\n              workflowName: `${agentName} - 核心功能流程`,\n              workflowDescription: '智能体的核心功能和主要应用场景',\n              agentAvatar: baseAgent.agentAvatar || '',\n              sequence: 1\n            },\n            {\n              id: '2',\n              workflowName: `${agentName} - 扩展应用流程`,\n              workflowDescription: '扩展功能和特殊应用场景的工作流程',\n              agentAvatar: baseAgent.agentAvatar || '',\n              sequence: 2\n            }\n          ]\n        }\n\n        console.log('🔍 AgentDetailModal: 加载工作流列表', this.workflowList.length, '个')\n      } catch (error) {\n        console.error('加载工作流列表失败:', error)\n        this.$message.error('加载工作流列表失败')\n      } finally {\n        this.workflowLoading = false\n      }\n    },\n\n    // 重置数据\n    resetData() {\n      this.agentDetail = {}\n      this.workflowList = []\n      this.downloadLoading = {}\n\n      // 重置视频状态\n      this.videoLoading = false\n      this.videoError = false\n      this.videoPlaying = false\n      this.currentTime = 0\n      this.duration = 0\n      this.progressPercent = 0\n      this.showVolumeSlider = false\n\n      // 停止视频播放\n      if (this.$refs.demoVideo) {\n        this.$refs.demoVideo.pause()\n        this.$refs.demoVideo.currentTime = 0\n      }\n    },\n\n    // 关闭弹窗\n    handleClose() {\n      this.$emit('close')\n    },\n\n    // 购买操作\n    handlePurchase() {\n      this.showPurchaseConfirm()\n    },\n\n    // 显示购买确认弹窗\n    showPurchaseConfirm() {\n      const agent = this.agentDetail\n      const finalPrice = this.finalPrice\n\n      this.$confirm({\n        title: '确认购买智能体',\n        content: () => (\n          <div style=\"padding: 16px 0;\">\n            <div style=\"display: flex; align-items: center; margin-bottom: 16px;\">\n              <img\n                src={agent.agentAvatar || ''}\n                alt={agent.agentName}\n                style=\"width: 48px; height: 48px; border-radius: 8px; margin-right: 12px; object-fit: cover;\"\n                onError={(e) => { e.target.style.display = 'none' }}\n              />\n              <div>\n                <div style=\"font-weight: 600; font-size: 16px; color: #1a202c;\">{agent.agentName}</div>\n                <div style=\"font-size: 12px; color: #718096; margin-top: 4px;\">\n                  {agent.authorType === '1' ? '官方智能体' : '创作者智能体'}\n                </div>\n              </div>\n            </div>\n\n            <div style=\"background: #f7fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;\">\n              <div style=\"display: flex; justify-content: space-between; align-items: center;\">\n                <span style=\"color: #4a5568;\">购买价格：</span>\n                <div>\n                  {agent.isFree ? (\n                    <span style=\"font-size: 18px; font-weight: 600; color: #38a169;\">免费</span>\n                  ) : agent.showDiscountPrice ? (\n                    <div>\n                      <span style=\"font-size: 18px; font-weight: 600; color: #e53e3e;\">¥{finalPrice}</span>\n                      <span style=\"font-size: 14px; color: #a0aec0; text-decoration: line-through; margin-left: 8px;\">\n                        ¥{agent.price || agent.originalPrice || 0}\n                      </span>\n                    </div>\n                  ) : (\n                    <span style=\"font-size: 18px; font-weight: 600; color: #2d3748;\">¥{agent.price || agent.originalPrice || 0}</span>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div style=\"font-size: 14px; color: #718096; line-height: 1.5;\">\n              购买后您将获得：\n              <ul style=\"margin: 8px 0 0 16px; padding: 0;\">\n                <li>永久使用权限</li>\n                <li>所有工作流下载权限</li>\n                <li>无使用次数限制</li>\n                <li>后续功能更新</li>\n              </ul>\n            </div>\n          </div>\n        ),\n        okText: '确认购买',\n        cancelText: '取消',\n        okType: 'primary',\n        width: 480,\n        onOk: () => {\n          this.processPurchase()\n        }\n      })\n    },\n\n    // 处理购买流程\n    async processPurchase() {\n      this.purchaseLoading = true\n      try {\n        // TODO: 检查用户余额\n        // const balanceResponse = await this.$http.get('/api/user/balance')\n\n        // TODO: 调用购买API\n        // const purchaseData = {\n        //   agentId: this.agentDetail.id,\n        //   agentName: this.agentDetail.agentName,\n        //   purchasePrice: this.finalPrice,\n        //   originalPrice: this.agentDetail.price || this.agentDetail.originalPrice,\n        //   discountRate: this.agentDetail.discountRate || 1\n        // }\n        // const response = await this.$http.post('/api/agent/purchase', purchaseData)\n\n        // 模拟购买过程\n        await new Promise(resolve => setTimeout(resolve, 1500))\n\n        this.$message.success('购买成功！')\n\n        // 通知父组件更新购买状态\n        this.$emit('purchase-success', this.agentDetail.id)\n\n        // 关闭弹窗\n        this.handleClose()\n\n      } catch (error) {\n        console.error('购买失败:', error)\n        if (error.response && error.response.data && error.response.data.message) {\n          this.$message.error(error.response.data.message)\n        } else {\n          this.$message.error('购买失败，请稍后重试')\n        }\n      } finally {\n        this.purchaseLoading = false\n      }\n    },\n\n    // 查看详情\n    handleViewDetail() {\n      if (!this.isPurchased) {\n        this.$message.warning('请先购买该智能体后查看详情')\n        return\n      }\n\n      // TODO: 跳转到智能体详情页面\n      // 这里应该跳转到一个更详细的智能体介绍页面\n      this.$router.push({\n        path: '/agent/detail',\n        query: {\n          agentId: this.agentDetail.id,\n          agentName: this.agentDetail.agentName\n        }\n      })\n\n      console.log('🔗 跳转到智能体详情页面')\n      this.$message.success('正在跳转到详情页面...')\n    },\n\n    // 体验智能体\n    handleExperience() {\n      if (!this.agentDetail.experienceLink) {\n        this.$message.warning('该智能体暂未提供体验链接')\n        return\n      }\n\n      try {\n        // 在新窗口打开体验链接\n        window.open(this.agentDetail.experienceLink, '_blank')\n        console.log('🔗 打开体验链接:', this.agentDetail.experienceLink)\n        this.$message.success('正在打开体验页面...')\n      } catch (error) {\n        console.error('打开体验链接失败:', error)\n        this.$message.error('打开体验页面失败')\n      }\n    },\n\n    // 工作流下载\n    async handleWorkflowDownload(workflow) {\n      this.$set(this.downloadLoading, workflow.id, true)\n      try {\n        // TODO: 跳转到下载页面\n        this.$message.success('正在跳转到下载页面...')\n      } catch (error) {\n        this.$message.error('下载失败')\n      } finally {\n        this.$set(this.downloadLoading, workflow.id, false)\n      }\n    },\n\n    // 下载提示\n    handleDownloadTip() {\n      this.$message.warning('请先购买智能体后再下载工作流')\n    },\n\n    // 处理工作流下载\n    async handleWorkflowDownload(workflow) {\n      console.log('下载工作流:', workflow)\n\n      // 检查购买状态\n      if (!this.isPurchased) {\n        this.$message.warning('请先购买该智能体后再下载工作流')\n        return\n      }\n\n      // 设置下载状态\n      this.$set(this.downloadLoading, workflow.id, true)\n\n      try {\n        // 已购买用户 - 跳转到下载页面\n        await this.navigateToDownloadPage(workflow)\n      } catch (error) {\n        console.error('下载工作流失败:', error)\n        this.$message.error('下载失败，请稍后重试')\n      } finally {\n        this.$set(this.downloadLoading, workflow.id, false)\n      }\n    },\n\n    // 跳转到工作流下载页面\n    async navigateToDownloadPage(workflow) {\n      try {\n        // TODO: 实现跳转到工作流下载页面的逻辑\n        // 这里应该跳转到一个专门的下载页面，而不是直接下载文件\n\n        // 模拟跳转延迟\n        await new Promise(resolve => setTimeout(resolve, 800))\n\n        const downloadUrl = `/workflow/download/${workflow.id}?agentId=${this.agentDetail.id}`\n\n        // 使用路由跳转\n        this.$router.push({\n          path: '/workflow/download',\n          query: {\n            workflowId: workflow.id,\n            agentId: this.agentDetail.id,\n            workflowName: workflow.workflowName\n          }\n        })\n\n        console.log('🔗 跳转到工作流下载页面:', downloadUrl)\n        this.$message.success('正在跳转到下载页面...')\n\n      } catch (error) {\n        console.error('跳转下载页面失败:', error)\n        this.$message.error('跳转失败，请稍后重试')\n      }\n    },\n\n    // ========== 视频播放控制方法 ==========\n\n    // 视频开始加载\n    handleVideoLoadStart() {\n      console.log('🎬 视频开始加载')\n      this.videoLoading = true\n      this.videoError = false\n    },\n\n    // 视频加载完成\n    handleVideoLoaded() {\n      console.log('🎬 视频加载完成')\n      this.videoLoading = false\n      this.videoError = false\n\n      const video = this.$refs.demoVideo\n      if (video) {\n        this.duration = video.duration || 0\n        this.videoVolume = Math.round(video.volume * 100)\n\n        // 尝试自动播放（静音状态下）\n        if (this.videoAutoplay && this.videoMuted) {\n          this.playVideo()\n        }\n      }\n    },\n\n    // 视频加载错误\n    handleVideoError(event) {\n      console.error('🎬 视频加载失败:', event)\n      this.videoLoading = false\n      this.videoError = true\n      this.videoPlaying = false\n      this.$message.error('视频加载失败')\n    },\n\n    // 视频播放事件\n    handleVideoPlay() {\n      console.log('🎬 视频开始播放')\n      this.videoPlaying = true\n    },\n\n    // 视频暂停事件\n    handleVideoPause() {\n      console.log('🎬 视频暂停播放')\n      this.videoPlaying = false\n    },\n\n    // 视频时间更新\n    handleVideoTimeUpdate() {\n      const video = this.$refs.demoVideo\n      if (video && video.duration) {\n        this.currentTime = video.currentTime\n        this.progressPercent = (video.currentTime / video.duration) * 100\n      }\n    },\n\n    // 音量变化事件\n    handleVolumeChange() {\n      const video = this.$refs.demoVideo\n      if (video) {\n        this.videoVolume = Math.round(video.volume * 100)\n        this.videoMuted = video.muted\n      }\n    },\n\n    // 切换播放/暂停\n    toggleVideoPlay() {\n      const video = this.$refs.demoVideo\n      if (!video) return\n\n      if (this.videoPlaying) {\n        video.pause()\n      } else {\n        this.playVideo()\n      }\n    },\n\n    // 播放视频\n    async playVideo() {\n      const video = this.$refs.demoVideo\n      if (!video) return\n\n      try {\n        await video.play()\n        console.log('🎬 视频播放成功')\n      } catch (error) {\n        console.error('🎬 视频播放失败:', error)\n        // 如果自动播放失败，可能是因为浏览器策略，尝试静音播放\n        if (!this.videoMuted) {\n          this.videoMuted = true\n          video.muted = true\n          try {\n            await video.play()\n            this.$message.info('视频已静音播放，点击音量按钮开启声音')\n          } catch (retryError) {\n            console.error('🎬 静音播放也失败:', retryError)\n            this.$message.warning('视频播放失败，请手动点击播放')\n          }\n        }\n      }\n    },\n\n    // 切换静音\n    toggleMute() {\n      const video = this.$refs.demoVideo\n      if (!video) return\n\n      this.videoMuted = !this.videoMuted\n      video.muted = this.videoMuted\n\n      console.log('🎬 切换静音状态:', this.videoMuted ? '静音' : '有声')\n    },\n\n    // 处理进度条点击\n    handleProgressClick(event) {\n      const video = this.$refs.demoVideo\n      if (!video || !video.duration) return\n\n      const progressContainer = event.currentTarget\n      const rect = progressContainer.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const progressWidth = rect.width\n      const clickPercent = clickX / progressWidth\n\n      const newTime = clickPercent * video.duration\n      video.currentTime = newTime\n\n      console.log('🎬 跳转到时间:', this.formatTime(newTime))\n    },\n\n    // 音量滑块变化\n    handleVolumeSliderChange(value) {\n      const video = this.$refs.demoVideo\n      if (!video) return\n\n      this.videoVolume = value\n      video.volume = value / 100\n\n      // 如果音量大于0，自动取消静音\n      if (value > 0 && this.videoMuted) {\n        this.videoMuted = false\n        video.muted = false\n      }\n\n      console.log('🎬 调整音量:', value + '%')\n    },\n\n    // 切换全屏\n    toggleFullscreen() {\n      const video = this.$refs.demoVideo\n      if (!video) return\n\n      if (video.requestFullscreen) {\n        video.requestFullscreen()\n      } else if (video.webkitRequestFullscreen) {\n        video.webkitRequestFullscreen()\n      } else if (video.mozRequestFullScreen) {\n        video.mozRequestFullScreen()\n      } else if (video.msRequestFullscreen) {\n        video.msRequestFullscreen()\n      }\n\n      console.log('🎬 切换全屏模式')\n    },\n\n    // 重新加载视频\n    retryVideoLoad() {\n      console.log('🎬 重新加载视频')\n      this.videoError = false\n      this.videoLoading = true\n\n      const video = this.$refs.demoVideo\n      if (video) {\n        video.load()\n      }\n    },\n\n    // 格式化时间显示\n    formatTime(seconds) {\n      if (!seconds || isNaN(seconds)) return '00:00'\n\n      const minutes = Math.floor(seconds / 60)\n      const remainingSeconds = Math.floor(seconds % 60)\n\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`\n    },\n\n    // 图片加载错误处理\n    handleImageError(event) {\n      event.target.style.display = 'none'\n    },\n\n    // 创作者头像加载错误处理\n    handleCreatorAvatarError(event) {\n      event.target.style.display = 'none'\n    },\n\n    // 工作流图片加载错误处理\n    handleWorkflowImageError(event) {\n      event.target.style.display = 'none'\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 弹窗样式 */\n.agent-detail-modal {\n  top: 20px;\n}\n\n.agent-detail-modal .ant-modal-body {\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 0;\n}\n\n/* 自定义滚动条 */\n.agent-detail-modal .ant-modal-body::-webkit-scrollbar {\n  width: 6px;\n}\n\n.agent-detail-modal .ant-modal-body::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 弹窗动画优化 */\n.agent-detail-modal .ant-modal {\n  animation: modalFadeIn 0.3s ease-out;\n}\n\n@keyframes modalFadeIn {\n  from {\n    opacity: 0;\n    transform: scale(0.95) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* 加载状态 */\n.loading-container {\n  padding: 60px 0;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 200px;\n}\n\n/* 主要内容 */\n.modal-content {\n  padding: 0;\n}\n\n/* 1. 智能体基本信息区域 */\n.agent-info-section {\n  padding: 32px;\n  border-bottom: 1px solid rgba(226, 232, 240, 0.6);\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.agent-header {\n  display: flex;\n  align-items: flex-start;\n  gap: 24px;\n  position: relative;\n  z-index: 1;\n}\n\n.agent-avatar {\n  width: 96px;\n  height: 96px;\n  border-radius: 20px;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n  flex-shrink: 0;\n  border: 3px solid rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.agent-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f5f5f5;\n  color: #999;\n  font-size: 32px;\n}\n\n.agent-basic-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.agent-name {\n  margin: 0 0 12px 0;\n  font-size: 28px;\n  font-weight: 700;\n  color: white;\n  line-height: 1.2;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.agent-description {\n  margin: 0 0 20px 0;\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 16px;\n  line-height: 1.6;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.creator-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.creator-avatar {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.creator-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.creator-avatar .anticon {\n  font-size: 12px;\n  color: #999;\n}\n\n.creator-details {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.creator-name {\n  font-size: 13px;\n  font-weight: 500;\n  color: #2d3748;\n}\n\n.creator-type {\n  font-size: 12px;\n  color: #718096;\n}\n\n.price-section {\n  flex-shrink: 0;\n  text-align: right;\n}\n\n.price-container {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 4px;\n}\n\n.free-price {\n  font-size: 20px;\n  font-weight: 600;\n  color: #38a169;\n}\n\n.discount-price {\n  font-size: 20px;\n  font-weight: 600;\n  color: #e53e3e;\n}\n\n.original-price {\n  font-size: 14px;\n  color: #a0aec0;\n  text-decoration: line-through;\n}\n\n.current-price {\n  font-size: 20px;\n  font-weight: 600;\n  color: #2d3748;\n}\n\n/* 2. 演示视频区域 */\n.demo-video-section {\n  padding: 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.section-title {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #2d3748;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.section-title .anticon {\n  color: #4299e1;\n}\n\n.video-container {\n  border-radius: 12px;\n  overflow: hidden;\n  background: #000;\n  position: relative;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.video-wrapper {\n  position: relative;\n  width: 100%;\n  height: 300px;\n}\n\n.demo-video {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.demo-video:hover {\n  transform: scale(1.02);\n}\n\n/* 视频控制栏 */\n.video-controls {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  transition: opacity 0.3s ease;\n}\n\n.controls-left {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.controls-center {\n  flex: 1;\n  margin: 0 16px;\n}\n\n.controls-right {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.control-btn {\n  color: white !important;\n  border: none !important;\n  background: transparent !important;\n  padding: 4px 8px !important;\n  height: auto !important;\n  font-size: 16px;\n  transition: all 0.2s ease;\n}\n\n.control-btn:hover {\n  color: #4299e1 !important;\n  background: rgba(255, 255, 255, 0.1) !important;\n  transform: scale(1.1);\n}\n\n.time-display {\n  color: white;\n  font-size: 12px;\n  font-family: monospace;\n  min-width: 80px;\n}\n\n/* 进度条 */\n.progress-container {\n  cursor: pointer;\n  padding: 8px 0;\n}\n\n.progress-bar {\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  position: relative;\n  overflow: hidden;\n}\n\n.progress-filled {\n  height: 100%;\n  background: linear-gradient(90deg, #4299e1, #3182ce);\n  border-radius: 2px;\n  transition: width 0.1s ease;\n}\n\n.progress-thumb {\n  position: absolute;\n  top: 50%;\n  width: 12px;\n  height: 12px;\n  background: white;\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  transition: left 0.1s ease;\n}\n\n/* 音量控制 */\n.volume-control {\n  position: relative;\n}\n\n.volume-slider {\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.8);\n  padding: 12px 8px;\n  border-radius: 6px;\n  margin-bottom: 8px;\n  height: 100px;\n}\n\n.volume-range {\n  height: 80px;\n}\n\n.volume-range .ant-slider-rail {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.volume-range .ant-slider-track {\n  background: #4299e1;\n}\n\n.volume-range .ant-slider-handle {\n  border-color: #4299e1;\n}\n\n/* 视频加载状态 */\n.video-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  z-index: 10;\n}\n\n.video-loading p {\n  margin-top: 16px;\n  color: white;\n  font-size: 14px;\n}\n\n/* 视频错误占位符 */\n.video-error-placeholder {\n  width: 100%;\n  height: 300px;\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px dashed #cbd5e0;\n}\n\n.error-content {\n  text-align: center;\n  color: #718096;\n}\n\n.error-icon {\n  font-size: 48px;\n  color: #f56565;\n  margin-bottom: 16px;\n}\n\n.error-content h4 {\n  margin: 0 0 8px 0;\n  color: #2d3748;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.error-content p {\n  margin: 0 0 16px 0;\n  color: #718096;\n  font-size: 14px;\n}\n\n/* 3. 工作流列表区域 */\n.workflow-section {\n  padding: 24px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.workflow-count {\n  font-size: 14px;\n  color: #718096;\n  font-weight: normal;\n}\n\n.workflow-loading {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.workflow-list {\n  margin-top: 16px;\n}\n\n.workflow-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  background: #fff;\n  transition: all 0.2s ease;\n}\n\n.workflow-item:hover {\n  border-color: #cbd5e0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.workflow-item:last-child {\n  margin-bottom: 0;\n}\n\n.workflow-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex: 1;\n  min-width: 0;\n}\n\n.workflow-sequence {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background: #4299e1;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: 600;\n  flex-shrink: 0;\n}\n\n.workflow-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 6px;\n  overflow: hidden;\n  background: #f7fafc;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.workflow-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.workflow-avatar .anticon {\n  font-size: 16px;\n  color: #a0aec0;\n}\n\n.workflow-details {\n  flex: 1;\n  min-width: 0;\n}\n\n.workflow-name {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 500;\n  color: #2d3748;\n  line-height: 1.2;\n}\n\n.workflow-description {\n  margin: 0;\n  font-size: 12px;\n  color: #718096;\n  line-height: 1.3;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.workflow-actions {\n  flex-shrink: 0;\n  margin-left: 16px;\n}\n\n.workflow-empty {\n  padding: 40px 0;\n  text-align: center;\n}\n\n/* 4. 底部操作按钮区域 */\n.action-buttons {\n  padding: 20px 24px;\n  background: #f8fafc;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  border-top: 1px solid #e2e8f0;\n  position: sticky;\n  bottom: 0;\n  z-index: 10;\n  backdrop-filter: blur(10px);\n}\n\n.action-buttons .ant-btn {\n  min-width: 80px;\n}\n\n.close-btn {\n  margin-right: auto;\n}\n\n.purchase-btn {\n  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);\n  border-color: #4299e1;\n  color: white;\n  font-weight: 600;\n  box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);\n  transition: all 0.3s ease;\n}\n\n.purchase-btn:hover {\n  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);\n  border-color: #3182ce;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);\n}\n\n.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .agent-detail-modal {\n    top: 10px;\n    margin: 0 10px;\n  }\n\n  .agent-detail-modal .ant-modal {\n    max-width: none;\n    width: calc(100vw - 20px) !important;\n  }\n\n  .agent-header {\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .agent-avatar {\n    align-self: center;\n  }\n\n  .price-section {\n    text-align: center;\n  }\n\n  .workflow-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n\n  .workflow-actions {\n    margin-left: 0;\n    align-self: stretch;\n  }\n\n  .workflow-actions .ant-btn {\n    width: 100%;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n  }\n\n  .close-btn {\n    margin-right: 0;\n    order: 1;\n  }\n}\n\n/* ===== 现代化弹窗样式美化 ===== */\n\n/* 自定义弹窗样式 */\n.custom-modal :deep(.ant-modal) {\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n}\n\n.custom-modal :deep(.ant-modal-content) {\n  border-radius: 16px;\n  overflow: hidden;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n}\n\n.custom-modal :deep(.ant-modal-body) {\n  padding: 0;\n  border-radius: 16px;\n  overflow: hidden;\n}\n\n/* 自定义关闭按钮 */\n.custom-close-button {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  width: 40px;\n  height: 40px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 1000;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.custom-close-button:hover {\n  background: rgba(255, 255, 255, 1);\n  transform: scale(1.1);\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\n}\n\n.custom-close-button .anticon {\n  font-size: 18px;\n  color: #64748b;\n}\n\n/* 背景装饰 */\n.modal-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 0;\n}\n\n.bg-pattern {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 200px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  opacity: 0.05;\n}\n\n.bg-gradient {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100px;\n  background: linear-gradient(to top, rgba(102, 126, 234, 0.05) 0%, transparent 100%);\n}\n\n/* 现代化按钮样式 */\n.modern-actions {\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\n  border-top: 1px solid rgba(226, 232, 240, 0.6);\n  backdrop-filter: blur(20px);\n  padding: 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 16px;\n}\n\n.primary-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.modern-btn-primary {\n  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600 !important;\n  padding: 8px 24px !important;\n  height: 44px !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 14px rgba(79, 70, 229, 0.3) !important;\n  transition: all 0.3s ease !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 8px !important;\n}\n\n.modern-btn-primary:hover {\n  transform: translateY(-2px) !important;\n  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4) !important;\n  background: linear-gradient(135deg, #5b52f0 0%, #8b5cf6 100%) !important;\n}\n\n.modern-btn-outline {\n  background: rgba(255, 255, 255, 0.8) !important;\n  border: 2px solid rgba(79, 70, 229, 0.2) !important;\n  color: #4f46e5 !important;\n  font-weight: 500 !important;\n  padding: 8px 20px !important;\n  height: 44px !important;\n  border-radius: 12px !important;\n  backdrop-filter: blur(10px) !important;\n  transition: all 0.3s ease !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 8px !important;\n}\n\n.modern-btn-outline:hover {\n  background: rgba(79, 70, 229, 0.05) !important;\n  border-color: rgba(79, 70, 229, 0.4) !important;\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15) !important;\n}\n\n.modern-btn-secondary {\n  background: rgba(100, 116, 139, 0.1) !important;\n  border: 2px solid rgba(100, 116, 139, 0.2) !important;\n  color: #64748b !important;\n  font-weight: 500 !important;\n  padding: 8px 20px !important;\n  height: 44px !important;\n  border-radius: 12px !important;\n  backdrop-filter: blur(10px) !important;\n  transition: all 0.3s ease !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 8px !important;\n}\n\n.modern-btn-secondary:hover {\n  background: rgba(100, 116, 139, 0.15) !important;\n  border-color: rgba(100, 116, 139, 0.3) !important;\n  color: #475569 !important;\n}\n\n.modern-btn-outline.disabled,\n.modern-btn-secondary.disabled {\n  opacity: 0.5 !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n}\n\n.modern-btn-outline.disabled:hover,\n.modern-btn-secondary.disabled:hover {\n  transform: none !important;\n  box-shadow: none !important;\n}\n</style>\n"], "sourceRoot": "src/views/website/workflow/components"}]}