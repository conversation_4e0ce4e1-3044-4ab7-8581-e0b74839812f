{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue", "mtime": 1753973678761}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport WebsitePage from '@/components/website/WebsitePage.vue';\nimport CategoryFilter from './components/CategoryFilter.vue';\nimport SearchFilter from './components/SearchFilter.vue';\nimport PluginGrid from './components/PluginGrid.vue';\nimport marketApi from '@/api/market';\nimport { formatCategories, validatePluginData, getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils';\nimport { HeartbeatMixin } from '@/mixins/HeartbeatMixin';\nimport { getCurrentPageConfig } from '@/utils/heartbeatConfig';\nexport default {\n  name: 'Market',\n  // 确保组件名称与路由配置中的componentName一致\n  mixins: [HeartbeatMixin],\n  components: {\n    WebsitePage: WebsitePage,\n    CategoryFilter: CategoryFilter,\n    SearchFilter: SearchFilter,\n    PluginGrid: PluginGrid\n  },\n  data: function data() {\n    return {\n      // 心跳配置 - 商城页面使用中频心跳\n      heartbeatConfig: getCurrentPageConfig('market', {\n        apiKey: 'market-page-heartbeat-key',\n        // 商城页面专用API密钥\n        enableDebugLog: process.env.NODE_ENV === 'development'\n      }),\n      // 插件数据\n      allPlugins: [],\n      filteredPlugins: [],\n      currentPagePlugins: [],\n      originalPluginsData: [],\n      // 🔥 存储原始插件数据（用于搜索子插件）\n      // 分类数据\n      categories: [],\n      categoryCounts: {},\n      // 分页状态\n      currentPage: 1,\n      pageSize: 12,\n      // 筛选条件\n      currentFilters: {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      },\n      // 加载状态\n      loading: false,\n      error: null,\n      // 统计数据\n      totalPlugins: 0,\n      // 搜索相关\n      searchKeyword: '',\n      showSuggestions: false,\n      suggestions: ['文案生成', '图片处理', '视频剪辑', '数据分析', '代码助手'],\n      // 筛选面板折叠状态\n      collapsedSections: {\n        search: false,\n        category: false,\n        price: true,\n        sort: true\n      },\n      // 自定义价格范围\n      showCustomPrice: false,\n      customPriceMin: null,\n      customPriceMax: null,\n      // 🔥 组合插件弹窗\n      showCombinedModal: false,\n      selectedCombinedPlugin: null,\n      combinedModalLoading: false,\n      combinedSubPlugins: [],\n      // 🔥 悬浮式剪映小助手推广组件\n      showJianYingFloat: true // 是否显示悬浮组件\n\n    };\n  },\n  computed: {\n    hasActiveFilters: function hasActiveFilters() {\n      return this.currentFilters.category || this.currentFilters.keyword || this.currentFilters.priceRange || this.currentFilters.sortType !== 'default';\n    },\n    // 🔥 计算原始插件总数（包括组合插件的所有子插件）\n    totalOriginalPlugins: function totalOriginalPlugins() {\n      return this.originalPluginsData ? this.originalPluginsData.length : 0;\n    },\n    // 🔥 计算筛选后的总插件数量（包括组合插件的子插件）\n    filteredTotalPlugins: function filteredTotalPlugins() {\n      var _this = this;\n\n      var totalCount = 0;\n      this.filteredPlugins.forEach(function (plugin) {\n        if (plugin.isCombined === 1) {\n          // 组合插件：计算其子插件数量\n          var subPlugins = _this.getSubPluginsFromOriginalData(plugin.combinedName);\n\n          totalCount += subPlugins.length;\n        } else {\n          // 普通插件：计数为1\n          totalCount += 1;\n        }\n      });\n      return totalCount;\n    },\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage: function defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';\n    }\n  },\n  created: function () {\n    var _created = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              // 悬浮组件默认显示，不需要检查localStorage\n              this.showJianYingFloat = true;\n              console.log('🔥 悬浮组件默认显示'); // 只有在没有数据时才初始化，避免重复加载\n\n              if (!(this.allPlugins.length === 0)) {\n                _context.next = 7;\n                break;\n              }\n\n              _context.next = 5;\n              return this.initializeMarket();\n\n            case 5:\n              _context.next = 10;\n              break;\n\n            case 7:\n              // 如果已有数据，只恢复筛选状态\n              this.restoreMarketState();\n              this.applyFilters();\n              console.log('商城数据已存在，跳过重新加载');\n\n            case 10:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function created() {\n      return _created.apply(this, arguments);\n    }\n\n    return created;\n  }(),\n  // 组件销毁前恢复滚动\n  beforeDestroy: function beforeDestroy() {\n    document.body.style.overflow = '';\n  },\n  // 监听筛选条件变化，保存到localStorage\n  watch: {\n    currentFilters: {\n      handler: function handler(newFilters) {\n        this.saveMarketState(newFilters);\n      },\n      deep: true\n    },\n    searchKeyword: function searchKeyword(newKeyword) {\n      this.saveMarketState(_objectSpread(_objectSpread({}, this.currentFilters), {}, {\n        keyword: newKeyword\n      }));\n    }\n  },\n  methods: {\n    // 初始化商城\n    initializeMarket: function () {\n      var _initializeMarket = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                this.loading = true;\n                _context2.prev = 1;\n                // 恢复筛选状态\n                this.restoreMarketState(); // 并行加载分类和插件数据\n\n                _context2.next = 5;\n                return Promise.all([this.loadCategories(), this.loadPlugins()]);\n\n              case 5:\n                console.log('商城初始化完成');\n                _context2.next = 12;\n                break;\n\n              case 8:\n                _context2.prev = 8;\n                _context2.t0 = _context2[\"catch\"](1);\n                console.error('商城初始化失败:', _context2.t0);\n                this.error = '商城初始化失败，请刷新页面重试';\n\n              case 12:\n                _context2.prev = 12;\n                this.loading = false;\n                return _context2.finish(12);\n\n              case 15:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[1, 8, 12, 15]]);\n      }));\n\n      function initializeMarket() {\n        return _initializeMarket.apply(this, arguments);\n      }\n\n      return initializeMarket;\n    }(),\n    // 加载分类数据\n    loadCategories: function () {\n      var _loadCategories = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                _context3.next = 3;\n                return marketApi.getPluginCategories();\n\n              case 3:\n                response = _context3.sent;\n\n                if (response.success) {\n                  this.categories = formatCategories(response.result || []);\n                  console.log('分类数据加载成功:', this.categories);\n                }\n\n                _context3.next = 10;\n                break;\n\n              case 7:\n                _context3.prev = 7;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('加载分类数据失败:', _context3.t0);\n\n              case 10:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n\n      function loadCategories() {\n        return _loadCategories.apply(this, arguments);\n      }\n\n      return loadCategories;\n    }(),\n    // 加载插件数据\n    loadPlugins: function () {\n      var _loadPlugins = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var params, response, result, originalData, processedPlugins;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                params = {\n                  pageNo: 1,\n                  pageSize: 1000,\n                  // 获取所有数据，前端分页\n                  status: 1 // 只获取已上架的插件\n\n                };\n                _context4.next = 4;\n                return marketApi.getPluginList(params);\n\n              case 4:\n                response = _context4.sent;\n\n                if (!response.success) {\n                  _context4.next = 17;\n                  break;\n                }\n\n                result = response.result || response.data;\n                originalData = result.records || result || []; // 🔥 保存原始数据（用于搜索子插件）\n\n                this.originalPluginsData = _toConsumableArray(originalData); // 🔥 前端处理组合插件去重逻辑（使用统一工具函数）\n\n                processedPlugins = processPluginsWithCombined(originalData);\n                this.allPlugins = processedPlugins;\n                this.totalPlugins = processedPlugins.length; // 计算分类统计\n\n                this.calculateCategoryCounts(); // 应用筛选\n\n                this.applyFilters();\n                console.log('插件数据加载成功:', this.allPlugins.length, '个插件（包含组合插件）');\n                _context4.next = 18;\n                break;\n\n              case 17:\n                throw new Error(response.message || '获取插件数据失败');\n\n              case 18:\n                _context4.next = 24;\n                break;\n\n              case 20:\n                _context4.prev = 20;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('加载插件数据失败:', _context4.t0);\n                this.error = _context4.t0.message || '加载插件数据失败';\n\n              case 24:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 20]]);\n      }));\n\n      function loadPlugins() {\n        return _loadPlugins.apply(this, arguments);\n      }\n\n      return loadPlugins;\n    }(),\n    // 🔥 检查组合插件是否包含指定分类\n    combinedPluginHasCategory: function combinedPluginHasCategory(combinedPlugin, targetCategory) {\n      // 简化版本：直接检查组合插件本身的分类\n      // 在实际应用中，这里应该查询该组合插件的所有子插件\n      // 但为了避免复杂的异步查询，我们先用简化逻辑\n      return combinedPlugin.plubCategory === targetCategory;\n    },\n    // 🔥 计算分类统计（支持组合插件）\n    calculateCategoryCounts: function calculateCategoryCounts() {\n      var _this2 = this;\n\n      this.categoryCounts = {};\n      this.categories.forEach(function (category) {\n        _this2.categoryCounts[category.value] = 0;\n      }); // 统计组合插件数量\n\n      var combinedPluginCount = 0;\n      this.allPlugins.forEach(function (plugin) {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件计数\n          combinedPluginCount++; // 组合插件也按其分类计数（用于其他分类的统计）\n\n          if (plugin.plubCategory && _this2.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            _this2.categoryCounts[plugin.plubCategory]++;\n          }\n        } else {\n          // 普通插件计数\n          if (plugin.plubCategory && _this2.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            _this2.categoryCounts[plugin.plubCategory]++;\n          }\n        }\n      }); // 设置组合插件分类的数量\n\n      this.categoryCounts['combine'] = combinedPluginCount;\n      console.log('🔥 分类统计完成:', this.categoryCounts);\n    },\n    // 🔥 应用筛选条件（支持组合插件）\n    applyFilters: function applyFilters() {\n      var _this3 = this;\n\n      var filtered = _toConsumableArray(this.allPlugins); // 🔥 分类筛选（支持组合插件）\n\n\n      if (this.currentFilters.category) {\n        if (this.currentFilters.category === 'combine') {\n          // 只显示组合插件\n          filtered = filtered.filter(function (plugin) {\n            return plugin.isCombined === 1 || plugin.isCombined === '1';\n          });\n        } else {\n          // 显示指定分类的普通插件 + 包含该分类的组合插件\n          filtered = filtered.filter(function (plugin) {\n            // 普通插件：直接匹配分类\n            if (plugin.isCombined !== 1 && plugin.isCombined !== '1') {\n              return plugin.plubCategory === _this3.currentFilters.category;\n            } // 组合插件：需要检查是否包含该分类的子插件\n\n\n            return _this3.combinedPluginHasCategory(plugin, _this3.currentFilters.category);\n          });\n        }\n      } // 🔥 关键词筛选（支持组合插件及其子插件）\n\n\n      if (this.currentFilters.keyword) {\n        var keyword = this.currentFilters.keyword.toLowerCase();\n        filtered = this.filterByKeyword(filtered, keyword);\n      } // 价格范围筛选（只对普通插件有效）\n\n\n      if (this.currentFilters.priceRange) {\n        filtered = this.filterByPriceRange(filtered, this.currentFilters.priceRange);\n      } // 排序（包括默认排序）\n\n\n      filtered = this.sortPlugins(filtered, this.currentFilters.sortType);\n      this.filteredPlugins = filtered;\n      this.updateCurrentPagePlugins();\n    },\n    // 🔥 关键词筛选（支持搜索组合插件的子插件）\n    filterByKeyword: function filterByKeyword(plugins, keyword) {\n      var _this4 = this;\n\n      return plugins.filter(function (plugin) {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件：搜索组合插件名、描述和子插件名\n          // 1. 搜索组合插件本身的名称和描述\n          if (plugin.combinedName && plugin.combinedName.toLowerCase().includes(keyword) || plugin.combinedDescription && plugin.combinedDescription.toLowerCase().includes(keyword)) {\n            return true;\n          } // 2. 搜索组合插件的子插件名称\n          // 从原始数据中查找同名的组合插件的所有子插件\n\n\n          var subPlugins = _this4.getSubPluginsFromOriginalData(plugin.combinedName);\n\n          return subPlugins.some(function (subPlugin) {\n            return subPlugin.plubname && subPlugin.plubname.toLowerCase().includes(keyword);\n          });\n        } else {\n          // 普通插件：搜索插件名和描述\n          return plugin.plubname && plugin.plubname.toLowerCase().includes(keyword) || plugin.plubinfo && plugin.plubinfo.toLowerCase().includes(keyword);\n        }\n      });\n    },\n    // 🔥 从原始数据中获取组合插件的子插件\n    getSubPluginsFromOriginalData: function getSubPluginsFromOriginalData(combinedName) {\n      // 从loadPlugins时获取的原始数据中查找\n      // 这里需要访问处理前的原始插件数据\n      if (!this.originalPluginsData) {\n        return [];\n      }\n\n      return this.originalPluginsData.filter(function (plugin) {\n        return plugin.isCombined === 1 && plugin.combinedName === combinedName;\n      });\n    },\n    // 价格范围筛选\n    filterByPriceRange: function filterByPriceRange(plugins, priceRange) {\n      if (!priceRange) return plugins;\n      return plugins.filter(function (plugin) {\n        var price = parseFloat(plugin.neednum) || 0;\n\n        switch (priceRange) {\n          case '0-1':\n            return price >= 0 && price <= 1;\n\n          case '1-5':\n            return price > 1 && price <= 5;\n\n          case '5+':\n            return price > 5;\n\n          default:\n            // 自定义范围 格式: \"min-max\"\n            if (priceRange.includes('-')) {\n              var _priceRange$split$map = priceRange.split('-').map(function (p) {\n                return parseFloat(p) || 0;\n              }),\n                  _priceRange$split$map2 = _slicedToArray(_priceRange$split$map, 2),\n                  min = _priceRange$split$map2[0],\n                  max = _priceRange$split$map2[1];\n\n              if (max === 999) {\n                return price >= min;\n              } else {\n                return price >= min && price <= max;\n              }\n            }\n\n            return true;\n        }\n      });\n    },\n    // 插件排序\n    sortPlugins: function sortPlugins(plugins, sortType) {\n      var sorted = _toConsumableArray(plugins);\n\n      switch (sortType) {\n        case 'default':\n          // 默认排序：按照aigc_plub_shop表的sort_order字段排序（权重越小越靠前）\n          return sorted.sort(function (a, b) {\n            var weightA = parseFloat(a.sortOrder || a.sort_order || 999999); // 如果没有权重，设置为很大的数\n\n            var weightB = parseFloat(b.sortOrder || b.sort_order || 999999);\n            return weightA - weightB; // 权重小的在前\n          });\n\n        case 'newest':\n          // 按创建时间排序（假设有createTime字段，如果没有可以用id或其他字段）\n          return sorted.sort(function (a, b) {\n            var timeA = new Date(a.createTime || a.createBy || 0).getTime();\n            var timeB = new Date(b.createTime || b.createBy || 0).getTime();\n            return timeB - timeA; // 最新的在前\n          });\n\n        case 'price-asc':\n          // 价格从低到高\n          return sorted.sort(function (a, b) {\n            var priceA = parseFloat(a.neednum) || 0;\n            var priceB = parseFloat(b.neednum) || 0;\n            return priceA - priceB;\n          });\n\n        case 'price-desc':\n          // 价格从高到低\n          return sorted.sort(function (a, b) {\n            var priceA = parseFloat(a.neednum) || 0;\n            var priceB = parseFloat(b.neednum) || 0;\n            return priceB - priceA;\n          });\n\n        case 'name-asc':\n          // 名称A-Z\n          return sorted.sort(function (a, b) {\n            var nameA = (a.plubname || '').toLowerCase();\n            var nameB = (b.plubname || '').toLowerCase();\n            return nameA.localeCompare(nameB);\n          });\n\n        default:\n          // 如果是未知的排序类型，也使用默认排序\n          return sorted.sort(function (a, b) {\n            var weightA = parseFloat(a.sortWeight || a.sort_weight || 999999);\n            var weightB = parseFloat(b.sortWeight || b.sort_weight || 999999);\n            return weightA - weightB;\n          });\n      }\n    },\n    // 更新当前页插件\n    updateCurrentPagePlugins: function updateCurrentPagePlugins() {\n      var start = (this.currentPage - 1) * this.pageSize;\n      var end = start + this.pageSize;\n      this.currentPagePlugins = this.filteredPlugins.slice(start, end);\n    },\n    // 处理分类变更\n    handleCategoryChange: function handleCategoryChange(data) {\n      this.currentFilters.category = data.category;\n      this.currentPage = 1;\n      this.applyFilters();\n      console.log('分类筛选变更:', data);\n    },\n    // 处理搜索变更\n    handleSearchChange: function handleSearchChange(data) {\n      this.currentFilters.keyword = data.keyword;\n      this.currentPage = 1;\n      this.applyFilters();\n      console.log('搜索变更:', data);\n    },\n    // 处理筛选变更\n    handleFilterChange: function handleFilterChange(filters) {\n      this.currentFilters = _objectSpread(_objectSpread({}, this.currentFilters), filters);\n      this.currentPage = 1;\n      this.applyFilters();\n      console.log('筛选条件变更:', filters);\n    },\n    // 处理分页变更\n    handlePageChange: function handlePageChange(page, pageSize) {\n      this.currentPage = page;\n\n      if (pageSize) {\n        this.pageSize = pageSize;\n      }\n\n      this.updateCurrentPagePlugins(); // 滚动到顶部\n\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    },\n    // 处理页面大小变更\n    handlePageSizeChange: function handlePageSizeChange(page, pageSize) {\n      this.currentPage = page;\n      this.pageSize = pageSize;\n      this.updateCurrentPagePlugins();\n    },\n    // 处理插件使用 - 跳转到详情页\n    handlePluginUse: function handlePluginUse(plugin) {\n      console.log('使用插件:', plugin); // 检查插件数据有效性\n\n      if (!validatePluginData(plugin)) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        });\n        return;\n      } // 跳转到插件详情页\n\n\n      this.$router.push(\"/market/plugin/\".concat(plugin.id));\n    },\n    // 处理插件详情\n    handlePluginDetail: function handlePluginDetail(plugin) {\n      console.log('查看插件详情:', plugin); // 检查插件数据有效性\n\n      if (!plugin || !plugin.id) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        });\n        return;\n      } // 跳转到插件详情页\n\n\n      this.$router.push(\"/market/plugin/\".concat(plugin.id));\n    },\n    // 处理重试\n    handleRetry: function handleRetry() {\n      this.error = null;\n      this.loadPlugins();\n    },\n    // 🔥 获取子插件价格显示文本\n    getSubPluginPriceText: function getSubPluginPriceText(subPlugin) {\n      var price = subPlugin.neednum;\n      var isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1';\n\n      if (!price || price <= 0) {\n        return '免费';\n      }\n\n      if (isSvipFree) {\n        return \"SVIP\\u514D\\u8D39\\uFF0C\\u4F4E\\u81F3\\xA5\".concat(price, \"/\\u6B21\");\n      } else {\n        return \"\\u4F4E\\u81F3\\xA5\".concat(price, \"/\\u6B21\");\n      }\n    },\n    // 清空所有筛选\n    clearAllFilters: function clearAllFilters() {\n      this.currentFilters = {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      };\n      this.searchKeyword = ''; // 清空自定义价格输入框\n\n      this.customPriceMin = null;\n      this.customPriceMax = null;\n      this.currentPage = 1; // 清空localStorage中的筛选状态\n\n      this.clearMarketState(); // 重置子组件\n\n      if (this.$refs.categoryFilter) {\n        this.$refs.categoryFilter.resetCategory();\n      }\n\n      if (this.$refs.searchFilter) {\n        this.$refs.searchFilter.resetFilters();\n      }\n\n      this.applyFilters();\n      this.$notification.info({\n        message: '筛选已清空',\n        description: '已清空所有筛选条件',\n        placement: 'topRight'\n      });\n    },\n    // 实时搜索输入处理\n    handleSearchInput: function handleSearchInput() {\n      // 实时更新搜索关键词\n      this.currentFilters.keyword = this.searchKeyword;\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    // 🔥 搜索按钮点击或回车搜索\n    handleSearch: function handleSearch() {\n      this.currentFilters.keyword = this.searchKeyword;\n      this.currentPage = 1;\n      this.applyFilters();\n\n      if (this.searchKeyword) {\n        this.$notification.success({\n          message: '搜索执行中',\n          description: \"\\u6B63\\u5728\\u641C\\u7D22\\\"\".concat(this.searchKeyword, \"\\\"\\u76F8\\u5173\\u63D2\\u4EF6\"),\n          placement: 'topRight'\n        });\n      }\n    },\n    // 🔥 清空搜索框\n    clearSearchInput: function clearSearchInput() {\n      this.searchKeyword = '';\n      this.currentFilters.keyword = '';\n      this.currentPage = 1;\n      this.applyFilters();\n      this.$notification.info({\n        message: '搜索已清空',\n        description: '已清空搜索关键词',\n        placement: 'topRight'\n      });\n    },\n    selectCategory: function selectCategory(category) {\n      this.currentFilters.category = category;\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    selectPriceRange: function selectPriceRange(range) {\n      this.currentFilters.priceRange = range;\n      this.currentPage = 1; // 选择预设价格范围时清空自定义输入\n\n      this.customPriceMin = null;\n      this.customPriceMax = null;\n      this.applyFilters();\n    },\n    selectSort: function selectSort(sortType) {\n      this.currentFilters.sortType = sortType;\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    getCategoryIcon: function getCategoryIcon(category) {\n      var icons = {\n        // 按分类值匹配\n        'ai-chat': '💬',\n        'ai-image': '🎨',\n        'ai-video': '🎬',\n        'ai-audio': '🎵',\n        'social-share': '📱',\n        'tools': '⚙️',\n        'entertainment': '🎮',\n        'combine': '🔗',\n        // 🔥 组合插件图标\n        'other': '🔧',\n        // 按分类文本匹配（兼容旧数据）\n        '内容生成': '✍️',\n        '图片生成': '🎨',\n        '视频处理': '🎬',\n        '数据分析': '📊',\n        '开发工具': '⚙️',\n        '设计创意': '🎭',\n        '营销工具': '📈',\n        'AI对话': '💬',\n        'AI绘画': '🎨',\n        'AI视频': '🎬',\n        'AI音频': '🎵',\n        '社交分享': '📱',\n        '工具类': '⚙️',\n        '娱乐': '🎮',\n        '组合插件': '🔗',\n        // 🔥 组合插件图标\n        '其他': '🔧'\n      };\n      return icons[category] || '🔧';\n    },\n    getCategoryText: function getCategoryText(categoryValue) {\n      var category = this.categories.find(function (cat) {\n        return cat.value === categoryValue;\n      });\n      return category ? category.text : categoryValue;\n    },\n    // 获取价格范围显示文字\n    getPriceRangeText: function getPriceRangeText(priceRange) {\n      var priceTexts = {\n        '0-1': '¥0 - ¥1',\n        '1-5': '¥1 - ¥5',\n        '5+': '¥5以上'\n      }; // 如果是预设范围，返回对应文字\n\n      if (priceTexts[priceRange]) {\n        return priceTexts[priceRange];\n      } // 如果是自定义范围，格式化显示\n\n\n      if (priceRange && priceRange.includes('-')) {\n        var _priceRange$split = priceRange.split('-'),\n            _priceRange$split2 = _slicedToArray(_priceRange$split, 2),\n            min = _priceRange$split2[0],\n            max = _priceRange$split2[1];\n\n        if (max === '999') {\n          return \"\\xA5\".concat(min, \"\\u4EE5\\u4E0A\");\n        } else {\n          return \"\\xA5\".concat(min, \" - \\xA5\").concat(max);\n        }\n      }\n\n      return priceRange;\n    },\n    // 获取排序方式显示文字\n    getSortTypeText: function getSortTypeText(sortType) {\n      var sortTexts = {\n        'newest': '最新发布',\n        'price-asc': '价格从低到高',\n        'price-desc': '价格从高到低',\n        'name-asc': '名称A-Z'\n      };\n      return sortTexts[sortType] || sortType;\n    },\n    clearCategoryFilter: function clearCategoryFilter() {\n      this.currentFilters.category = '';\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    clearKeywordFilter: function clearKeywordFilter() {\n      this.currentFilters.keyword = '';\n      this.searchKeyword = '';\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    clearPriceFilter: function clearPriceFilter() {\n      this.currentFilters.priceRange = '';\n      this.showCustomPrice = false;\n      this.customPriceMin = null;\n      this.customPriceMax = null;\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    clearSortFilter: function clearSortFilter() {\n      this.currentFilters.sortType = 'default';\n      this.currentPage = 1;\n      this.applyFilters();\n    },\n    // 切换筛选面板折叠状态\n    toggleSection: function toggleSection(section) {\n      this.collapsedSections[section] = !this.collapsedSections[section];\n    },\n    // 应用自定义价格\n    applyCustomPrice: function applyCustomPrice() {\n      if (this.customPriceMin !== null || this.customPriceMax !== null) {\n        var min = this.customPriceMin || 0;\n        var max = this.customPriceMax || 999;\n        this.currentFilters.priceRange = \"\".concat(min, \"-\").concat(max);\n        this.currentPage = 1;\n        this.applyFilters();\n        this.$notification.success({\n          message: '价格筛选已应用',\n          description: \"\\u5DF2\\u5E94\\u7528\\u4EF7\\u683C\\u8303\\u56F4\\uFF1A\\xA5\".concat(min, \" - \").concat(max === 999 ? '以上' : '¥' + max),\n          placement: 'topRight'\n        });\n      }\n    },\n    // 切换自定义价格\n    toggleCustomPrice: function toggleCustomPrice() {\n      this.showCustomPrice = !this.showCustomPrice;\n\n      if (this.showCustomPrice) {// 打开自定义价格时，设置一个特殊标识，不清除筛选\n        // 这样\"全部\"就不会亮起，但也不会有实际的价格筛选\n      } else {\n        // 如果关闭自定义价格，清除自定义筛选\n        if (this.currentFilters.priceRange && this.currentFilters.priceRange.includes('-') && !['0-1', '1-5', '5+'].includes(this.currentFilters.priceRange)) {\n          this.currentFilters.priceRange = '';\n          this.applyFilters();\n        }\n      }\n    },\n    // 保存商城筛选状态到localStorage\n    saveMarketState: function saveMarketState(filters) {\n      try {\n        var state = {\n          category: filters.category || '',\n          search: filters.keyword || this.searchKeyword || '',\n          priceRange: filters.priceRange || '',\n          sortBy: filters.sortType || 'default',\n          timestamp: Date.now()\n        };\n        localStorage.setItem('market_filter_state', JSON.stringify(state));\n      } catch (error) {\n        console.warn('保存商城筛选状态失败:', error);\n      }\n    },\n    // 恢复商城筛选状态\n    restoreMarketState: function restoreMarketState() {\n      try {\n        var stateStr = localStorage.getItem('market_filter_state');\n        if (!stateStr) return;\n        var state = JSON.parse(stateStr); // 检查状态是否过期（24小时）\n\n        var now = Date.now();\n        var stateAge = now - (state.timestamp || 0);\n        var maxAge = 24 * 60 * 60 * 1000; // 24小时\n\n        if (stateAge > maxAge) {\n          localStorage.removeItem('market_filter_state');\n          return;\n        } // 恢复筛选状态\n\n\n        if (state.category) {\n          this.currentFilters.category = state.category;\n        }\n\n        if (state.search) {\n          this.currentFilters.keyword = state.search;\n          this.searchKeyword = state.search;\n        }\n\n        if (state.priceRange) {\n          this.currentFilters.priceRange = state.priceRange;\n        }\n\n        if (state.sortBy && state.sortBy !== 'default') {\n          this.currentFilters.sortType = state.sortBy;\n        }\n\n        console.log('已恢复商城筛选状态:', state);\n      } catch (error) {\n        console.warn('恢复商城筛选状态失败:', error);\n        localStorage.removeItem('market_filter_state');\n      }\n    },\n    // 清空筛选状态\n    clearMarketState: function clearMarketState() {\n      try {\n        localStorage.removeItem('market_filter_state');\n      } catch (error) {\n        console.warn('清空商城筛选状态失败:', error);\n      }\n    },\n    // 🔥 查看组合插件详情（显示子插件选择弹窗）\n    viewCombinedPluginDetails: function () {\n      var _viewCombinedPluginDetails = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(plugin) {\n        var subPlugins;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                console.log('🔗 查看组合插件详情:', plugin.combinedName);\n                this.selectedCombinedPlugin = plugin;\n                this.showCombinedModal = true;\n                this.combinedModalLoading = true;\n                this.combinedSubPlugins = []; // 禁止背景滚动\n\n                document.body.style.overflow = 'hidden'; // 加载子插件\n\n                try {\n                  subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName);\n                  this.combinedSubPlugins = subPlugins;\n                  console.log('🔗 加载子插件成功:', subPlugins.length);\n                } catch (error) {\n                  console.error('🔗 加载子插件失败:', error);\n                  this.$notification.error({\n                    message: '加载失败',\n                    description: '获取子插件列表失败',\n                    placement: 'topRight'\n                  });\n                } finally {\n                  this.combinedModalLoading = false;\n                }\n\n              case 7:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this);\n      }));\n\n      function viewCombinedPluginDetails(_x) {\n        return _viewCombinedPluginDetails.apply(this, arguments);\n      }\n\n      return viewCombinedPluginDetails;\n    }(),\n    // 🔥 选择子插件（跳转到具体插件详情页）\n    handleSelectSubPlugin: function handleSelectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname);\n      this.showCombinedModal = false; // 跳转到插件详情页\n\n      this.$router.push({\n        name: 'PluginDetail',\n        params: {\n          id: subPlugin.id\n        }\n      });\n    },\n    // 🔥 关闭组合插件弹窗\n    closeCombinedModal: function closeCombinedModal() {\n      this.showCombinedModal = false;\n      this.selectedCombinedPlugin = null;\n      this.combinedSubPlugins = []; // 恢复背景滚动\n\n      document.body.style.overflow = '';\n    },\n    // 🔥 选择子插件\n    selectSubPlugin: function selectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname);\n      this.closeCombinedModal(); // 跳转到插件详情页\n\n      this.$router.push({\n        name: 'PluginDetail',\n        params: {\n          id: subPlugin.id\n        }\n      });\n    },\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage: function getPluginImage(plugin) {\n      return getPluginImageUrl(plugin, this.defaultPluginImage);\n    },\n    // 🔥 跳转到剪映小助手页面\n    goToJianYingDraft: function goToJianYingDraft() {\n      this.$router.push('/JianYingDraft');\n      console.log('跳转到剪映小助手页面');\n    },\n    // 🔥 悬浮组件交互方法\n    // 隐藏悬浮组件（仅在当前会话中隐藏，刷新页面后重新显示）\n    hideJianYingFloat: function hideJianYingFloat() {\n      this.showJianYingFloat = false;\n      console.log('🔥 隐藏悬浮组件（仅当前会话）');\n    }\n  }\n};", {"version": 3, "sources": ["Market.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8aA,OAAA,WAAA,MAAA,sCAAA;AACA,OAAA,cAAA,MAAA,iCAAA;AACA,OAAA,YAAA,MAAA,+BAAA;AACA,OAAA,UAAA,MAAA,6BAAA;AACA,OAAA,SAAA,MAAA,cAAA;AACA,SAAA,gBAAA,EAAA,kBAAA,EAAA,iBAAA,EAAA,0BAAA,QAAA,qBAAA;AACA,SAAA,cAAA,QAAA,yBAAA;AACA,SAAA,oBAAA,QAAA,yBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,QADA;AACA;AACA,EAAA,MAAA,EAAA,CAAA,cAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA,WADA;AAEA,IAAA,cAAA,EAAA,cAFA;AAGA,IAAA,YAAA,EAAA,YAHA;AAIA,IAAA,UAAA,EAAA;AAJA,GAHA;AAUA,EAAA,IAVA,kBAUA;AACA,WAAA;AACA;AACA,MAAA,eAAA,EAAA,oBAAA,CAAA,QAAA,EAAA;AACA,QAAA,MAAA,EAAA,2BADA;AACA;AACA,QAAA,cAAA,EAAA,OAAA,CAAA,GAAA,CAAA,QAAA,KAAA;AAFA,OAAA,CAFA;AAOA;AACA,MAAA,UAAA,EAAA,EARA;AASA,MAAA,eAAA,EAAA,EATA;AAUA,MAAA,kBAAA,EAAA,EAVA;AAWA,MAAA,mBAAA,EAAA,EAXA;AAWA;AAEA;AACA,MAAA,UAAA,EAAA,EAdA;AAeA,MAAA,cAAA,EAAA,EAfA;AAiBA;AACA,MAAA,WAAA,EAAA,CAlBA;AAmBA,MAAA,QAAA,EAAA,EAnBA;AAqBA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAtBA;AA8BA;AACA,MAAA,OAAA,EAAA,KA/BA;AAgCA,MAAA,KAAA,EAAA,IAhCA;AAkCA;AACA,MAAA,YAAA,EAAA,CAnCA;AAqCA;AACA,MAAA,aAAA,EAAA,EAtCA;AAuCA,MAAA,eAAA,EAAA,KAvCA;AAwCA,MAAA,WAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAxCA;AA0CA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,MAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,KAFA;AAGA,QAAA,KAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OA3CA;AAkDA;AACA,MAAA,eAAA,EAAA,KAnDA;AAoDA,MAAA,cAAA,EAAA,IApDA;AAqDA,MAAA,cAAA,EAAA,IArDA;AAuDA;AACA,MAAA,iBAAA,EAAA,KAxDA;AAyDA,MAAA,sBAAA,EAAA,IAzDA;AA0DA,MAAA,oBAAA,EAAA,KA1DA;AA2DA,MAAA,kBAAA,EAAA,EA3DA;AA6DA;AACA,MAAA,iBAAA,EAAA,IA9DA,CA8DA;;AA9DA,KAAA;AAgEA,GA3EA;AA6EA,EAAA,QAAA,EAAA;AACA,IAAA,gBADA,8BACA;AACA,aAAA,KAAA,cAAA,CAAA,QAAA,IACA,KAAA,cAAA,CAAA,OADA,IAEA,KAAA,cAAA,CAAA,UAFA,IAGA,KAAA,cAAA,CAAA,QAAA,KAAA,SAHA;AAIA,KANA;AAQA;AACA,IAAA,oBATA,kCASA;AACA,aAAA,KAAA,mBAAA,GAAA,KAAA,mBAAA,CAAA,MAAA,GAAA,CAAA;AACA,KAXA;AAaA;AACA,IAAA,oBAdA,kCAcA;AAAA;;AACA,UAAA,UAAA,GAAA,CAAA;AAEA,WAAA,eAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA;AACA,cAAA,UAAA,GAAA,KAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;AACA,UAAA,UAAA,IAAA,UAAA,CAAA,MAAA;AACA,SAJA,MAIA;AACA;AACA,UAAA,UAAA,IAAA,CAAA;AACA;AACA,OATA;AAWA,aAAA,UAAA;AACA,KA7BA;AA+BA;AACA,IAAA,kBAhCA,gCAgCA;AACA,aAAA,2DAAA;AACA;AAlCA,GA7EA;AAkHA,EAAA,OAlHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmHA;AACA,mBAAA,iBAAA,GAAA,IAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EArHA,CAuHA;;AAvHA,oBAwHA,KAAA,UAAA,CAAA,MAAA,KAAA,CAxHA;AAAA;AAAA;AAAA;;AAAA;AAAA,qBAyHA,KAAA,gBAAA,EAzHA;;AAAA;AAAA;AAAA;;AAAA;AA2HA;AACA,mBAAA,kBAAA;AACA,mBAAA,YAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;;AA9HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkIA;AACA,EAAA,aAnIA,2BAmIA;AACA,IAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,EAAA;AACA,GArIA;AAuIA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,cAAA,EAAA;AACA,MAAA,OADA,mBACA,UADA,EACA;AACA,aAAA,eAAA,CAAA,UAAA;AACA,OAHA;AAIA,MAAA,IAAA,EAAA;AAJA,KADA;AAQA,IAAA,aARA,yBAQA,UARA,EAQA;AACA,WAAA,eAAA,iCAAA,KAAA,cAAA;AAAA,QAAA,OAAA,EAAA;AAAA;AACA;AAVA,GAxIA;AAqJA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,gBAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAA,OAAA,GAAA,IAAA;AAHA;AAMA;AACA,qBAAA,kBAAA,GAPA,CASA;;AATA;AAAA,uBAUA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,cAAA,EADA,EAEA,KAAA,WAAA,EAFA,CAAA,CAVA;;AAAA;AAeA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA;AAfA;AAAA;;AAAA;AAAA;AAAA;AAkBA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,qBAAA,KAAA,GAAA,iBAAA;;AAnBA;AAAA;AAqBA,qBAAA,OAAA,GAAA,KAAA;AArBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyBA;AACA,IAAA,cA1BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBA4BA,SAAA,CAAA,mBAAA,EA5BA;;AAAA;AA4BA,gBAAA,QA5BA;;AA6BA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,UAAA,GAAA,gBAAA,CAAA,QAAA,CAAA,MAAA,IAAA,EAAA,CAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,UAAA;AACA;;AAhCA;AAAA;;AAAA;AAAA;AAAA;AAkCA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;;AAlCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsCA;AACA,IAAA,WAvCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyCA,gBAAA,MAzCA,GAyCA;AACA,kBAAA,MAAA,EAAA,CADA;AAEA,kBAAA,QAAA,EAAA,IAFA;AAEA;AACA,kBAAA,MAAA,EAAA,CAHA,CAGA;;AAHA,iBAzCA;AAAA;AAAA,uBA+CA,SAAA,CAAA,aAAA,CAAA,MAAA,CA/CA;;AAAA;AA+CA,gBAAA,QA/CA;;AAAA,qBAiDA,QAAA,CAAA,OAjDA;AAAA;AAAA;AAAA;;AAkDA,gBAAA,MAlDA,GAkDA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IAlDA;AAmDA,gBAAA,YAnDA,GAmDA,MAAA,CAAA,OAAA,IAAA,MAAA,IAAA,EAnDA,EAqDA;;AACA,qBAAA,mBAAA,sBAAA,YAAA,EAtDA,CAwDA;;AACA,gBAAA,gBAzDA,GAyDA,0BAAA,CAAA,YAAA,CAzDA;AA2DA,qBAAA,UAAA,GAAA,gBAAA;AACA,qBAAA,YAAA,GAAA,gBAAA,CAAA,MAAA,CA5DA,CA8DA;;AACA,qBAAA,uBAAA,GA/DA,CAiEA;;AACA,qBAAA,YAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,UAAA,CAAA,MAAA,EAAA,aAAA;AApEA;AAAA;;AAAA;AAAA,sBAsEA,IAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,UAAA,CAtEA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA0EA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,KAAA,GAAA,aAAA,OAAA,IAAA,UAAA;;AA3EA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiFA;AACA,IAAA,yBAlFA,qCAkFA,cAlFA,EAkFA,cAlFA,EAkFA;AACA;AACA;AACA;AACA,aAAA,cAAA,CAAA,YAAA,KAAA,cAAA;AACA,KAvFA;AAyFA;AACA,IAAA,uBA1FA,qCA0FA;AAAA;;AACA,WAAA,cAAA,GAAA,EAAA;AAEA,WAAA,UAAA,CAAA,OAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,MAAA,CAAA,cAAA,CAAA,QAAA,CAAA,KAAA,IAAA,CAAA;AACA,OAFA,EAHA,CAOA;;AACA,UAAA,mBAAA,GAAA,CAAA;AAEA,WAAA,UAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,UAAA,mBAAA,GAFA,CAIA;;AACA,cAAA,MAAA,CAAA,YAAA,IAAA,MAAA,CAAA,cAAA,CAAA,cAAA,CAAA,MAAA,CAAA,YAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,cAAA,CAAA,MAAA,CAAA,YAAA;AACA;AACA,SARA,MAQA;AACA;AACA,cAAA,MAAA,CAAA,YAAA,IAAA,MAAA,CAAA,cAAA,CAAA,cAAA,CAAA,MAAA,CAAA,YAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,cAAA,CAAA,MAAA,CAAA,YAAA;AACA;AACA;AACA,OAfA,EAVA,CA2BA;;AACA,WAAA,cAAA,CAAA,SAAA,IAAA,mBAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,cAAA;AACA,KAzHA;AA2HA;AACA,IAAA,YA5HA,0BA4HA;AAAA;;AACA,UAAA,QAAA,sBAAA,KAAA,UAAA,CAAA,CADA,CAGA;;;AACA,UAAA,KAAA,cAAA,CAAA,QAAA,EAAA;AACA,YAAA,KAAA,cAAA,CAAA,QAAA,KAAA,SAAA,EAAA;AACA;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,UAAA,MAAA;AAAA,mBAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,KAAA,GAAA;AAAA,WAAA,CAAA;AACA,SAHA,MAGA;AACA;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA;AACA;AACA,gBAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,qBAAA,MAAA,CAAA,YAAA,KAAA,MAAA,CAAA,cAAA,CAAA,QAAA;AACA,aAJA,CAKA;;;AACA,mBAAA,MAAA,CAAA,yBAAA,CAAA,MAAA,EAAA,MAAA,CAAA,cAAA,CAAA,QAAA,CAAA;AACA,WAPA,CAAA;AAQA;AACA,OAnBA,CAqBA;;;AACA,UAAA,KAAA,cAAA,CAAA,OAAA,EAAA;AACA,YAAA,OAAA,GAAA,KAAA,cAAA,CAAA,OAAA,CAAA,WAAA,EAAA;AACA,QAAA,QAAA,GAAA,KAAA,eAAA,CAAA,QAAA,EAAA,OAAA,CAAA;AACA,OAzBA,CA2BA;;;AACA,UAAA,KAAA,cAAA,CAAA,UAAA,EAAA;AACA,QAAA,QAAA,GAAA,KAAA,kBAAA,CAAA,QAAA,EAAA,KAAA,cAAA,CAAA,UAAA,CAAA;AACA,OA9BA,CAgCA;;;AACA,MAAA,QAAA,GAAA,KAAA,WAAA,CAAA,QAAA,EAAA,KAAA,cAAA,CAAA,QAAA,CAAA;AAEA,WAAA,eAAA,GAAA,QAAA;AACA,WAAA,wBAAA;AACA,KAjKA;AAmKA;AACA,IAAA,eApKA,2BAoKA,OApKA,EAoKA,OApKA,EAoKA;AAAA;;AACA,aAAA,OAAA,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AAEA;AACA,cAAA,MAAA,CAAA,YAAA,IAAA,MAAA,CAAA,YAAA,CAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CAAA,IACA,MAAA,CAAA,mBAAA,IAAA,MAAA,CAAA,mBAAA,CAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CADA,EACA;AACA,mBAAA,IAAA;AACA,WAPA,CASA;AACA;;;AACA,cAAA,UAAA,GAAA,MAAA,CAAA,6BAAA,CAAA,MAAA,CAAA,YAAA,CAAA;;AACA,iBAAA,UAAA,CAAA,IAAA,CAAA,UAAA,SAAA;AAAA,mBACA,SAAA,CAAA,QAAA,IAAA,SAAA,CAAA,QAAA,CAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CADA;AAAA,WAAA,CAAA;AAGA,SAfA,MAeA;AACA;AACA,iBAAA,MAAA,CAAA,QAAA,IAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CAAA,IACA,MAAA,CAAA,QAAA,IAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,QAAA,CAAA,OAAA,CADA;AAEA;AACA,OArBA,CAAA;AAsBA,KA3LA;AA6LA;AACA,IAAA,6BA9LA,yCA8LA,YA9LA,EA8LA;AACA;AACA;AACA,UAAA,CAAA,KAAA,mBAAA,EAAA;AACA,eAAA,EAAA;AACA;;AAEA,aAAA,KAAA,mBAAA,CAAA,MAAA,CAAA,UAAA,MAAA;AAAA,eACA,MAAA,CAAA,UAAA,KAAA,CAAA,IACA,MAAA,CAAA,YAAA,KAAA,YAFA;AAAA,OAAA,CAAA;AAIA,KAzMA;AA2MA;AACA,IAAA,kBA5MA,8BA4MA,OA5MA,EA4MA,UA5MA,EA4MA;AACA,UAAA,CAAA,UAAA,EAAA,OAAA,OAAA;AAEA,aAAA,OAAA,CAAA,MAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,KAAA,GAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;;AAEA,gBAAA,UAAA;AACA,eAAA,KAAA;AACA,mBAAA,KAAA,IAAA,CAAA,IAAA,KAAA,IAAA,CAAA;;AACA,eAAA,KAAA;AACA,mBAAA,KAAA,GAAA,CAAA,IAAA,KAAA,IAAA,CAAA;;AACA,eAAA,IAAA;AACA,mBAAA,KAAA,GAAA,CAAA;;AACA;AACA;AACA,gBAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AAAA,0CACA,UAAA,CAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAA,UAAA,CAAA;AAAA,uBAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA;AAAA,eAAA,CADA;AAAA;AAAA,kBACA,GADA;AAAA,kBACA,GADA;;AAEA,kBAAA,GAAA,KAAA,GAAA,EAAA;AACA,uBAAA,KAAA,IAAA,GAAA;AACA,eAFA,MAEA;AACA,uBAAA,KAAA,IAAA,GAAA,IAAA,KAAA,IAAA,GAAA;AACA;AACA;;AACA,mBAAA,IAAA;AAjBA;AAmBA,OAtBA,CAAA;AAuBA,KAtOA;AAwOA;AACA,IAAA,WAzOA,uBAyOA,OAzOA,EAyOA,QAzOA,EAyOA;AACA,UAAA,MAAA,sBAAA,OAAA,CAAA;;AAEA,cAAA,QAAA;AACA,aAAA,SAAA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,OAAA,GAAA,UAAA,CAAA,CAAA,CAAA,SAAA,IAAA,CAAA,CAAA,UAAA,IAAA,MAAA,CAAA,CADA,CACA;;AACA,gBAAA,OAAA,GAAA,UAAA,CAAA,CAAA,CAAA,SAAA,IAAA,CAAA,CAAA,UAAA,IAAA,MAAA,CAAA;AACA,mBAAA,OAAA,GAAA,OAAA,CAHA,CAGA;AACA,WAJA,CAAA;;AAKA,aAAA,QAAA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,CAAA,CAAA,CAAA,UAAA,IAAA,CAAA,CAAA,QAAA,IAAA,CAAA,EAAA,OAAA,EAAA;AACA,gBAAA,KAAA,GAAA,IAAA,IAAA,CAAA,CAAA,CAAA,UAAA,IAAA,CAAA,CAAA,QAAA,IAAA,CAAA,EAAA,OAAA,EAAA;AACA,mBAAA,KAAA,GAAA,KAAA,CAHA,CAGA;AACA,WAJA,CAAA;;AAKA,aAAA,WAAA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,MAAA,GAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,gBAAA,MAAA,GAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,mBAAA,MAAA,GAAA,MAAA;AACA,WAJA,CAAA;;AAKA,aAAA,YAAA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,MAAA,GAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,gBAAA,MAAA,GAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,mBAAA,MAAA,GAAA,MAAA;AACA,WAJA,CAAA;;AAKA,aAAA,UAAA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,KAAA,GAAA,CAAA,CAAA,CAAA,QAAA,IAAA,EAAA,EAAA,WAAA,EAAA;AACA,gBAAA,KAAA,GAAA,CAAA,CAAA,CAAA,QAAA,IAAA,EAAA,EAAA,WAAA,EAAA;AACA,mBAAA,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,WAJA,CAAA;;AAKA;AACA;AACA,iBAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,OAAA,GAAA,UAAA,CAAA,CAAA,CAAA,UAAA,IAAA,CAAA,CAAA,WAAA,IAAA,MAAA,CAAA;AACA,gBAAA,OAAA,GAAA,UAAA,CAAA,CAAA,CAAA,UAAA,IAAA,CAAA,CAAA,WAAA,IAAA,MAAA,CAAA;AACA,mBAAA,OAAA,GAAA,OAAA;AACA,WAJA,CAAA;AAtCA;AA4CA,KAxRA;AA0RA;AACA,IAAA,wBA3RA,sCA2RA;AACA,UAAA,KAAA,GAAA,CAAA,KAAA,WAAA,GAAA,CAAA,IAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA,KAAA,GAAA,KAAA,QAAA;AACA,WAAA,kBAAA,GAAA,KAAA,eAAA,CAAA,KAAA,CAAA,KAAA,EAAA,GAAA,CAAA;AACA,KA/RA;AAiSA;AACA,IAAA,oBAlSA,gCAkSA,IAlSA,EAkSA;AACA,WAAA,cAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;AACA,KAvSA;AAySA;AACA,IAAA,kBA1SA,8BA0SA,IA1SA,EA0SA;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,IAAA;AACA,KA/SA;AAiTA;AACA,IAAA,kBAlTA,8BAkTA,OAlTA,EAkTA;AACA,WAAA,cAAA,mCAAA,KAAA,cAAA,GAAA,OAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA;AACA,KAvTA;AAyTA;AACA,IAAA,gBA1TA,4BA0TA,IA1TA,EA0TA,QA1TA,EA0TA;AACA,WAAA,WAAA,GAAA,IAAA;;AACA,UAAA,QAAA,EAAA;AACA,aAAA,QAAA,GAAA,QAAA;AACA;;AACA,WAAA,wBAAA,GALA,CAOA;;AACA,MAAA,MAAA,CAAA,QAAA,CAAA;AAAA,QAAA,GAAA,EAAA,CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,KAnUA;AAqUA;AACA,IAAA,oBAtUA,gCAsUA,IAtUA,EAsUA,QAtUA,EAsUA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,QAAA;AACA,WAAA,wBAAA;AACA,KA1UA;AA4UA;AACA,IAAA,eA7UA,2BA6UA,MA7UA,EA6UA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,MAAA,EADA,CAGA;;AACA,UAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,EAAA;AACA,aAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,QADA;AAEA,UAAA,WAAA,EAAA,QAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,OAXA,CAaA;;;AACA,WAAA,OAAA,CAAA,IAAA,0BAAA,MAAA,CAAA,EAAA;AACA,KA5VA;AA8VA;AACA,IAAA,kBA/VA,8BA+VA,MA/VA,EA+VA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,EADA,CAGA;;AACA,UAAA,CAAA,MAAA,IAAA,CAAA,MAAA,CAAA,EAAA,EAAA;AACA,aAAA,aAAA,CAAA,KAAA,CAAA;AACA,UAAA,OAAA,EAAA,QADA;AAEA,UAAA,WAAA,EAAA,QAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,OAXA,CAaA;;;AACA,WAAA,OAAA,CAAA,IAAA,0BAAA,MAAA,CAAA,EAAA;AACA,KA9WA;AAgXA;AACA,IAAA,WAjXA,yBAiXA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,WAAA;AACA,KApXA;AAsXA;AACA,IAAA,qBAvXA,iCAuXA,SAvXA,EAuXA;AACA,UAAA,KAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,UAAA,GAAA,SAAA,CAAA,UAAA,KAAA,CAAA,IAAA,SAAA,CAAA,UAAA,KAAA,GAAA;;AAEA,UAAA,CAAA,KAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,eAAA,IAAA;AACA;;AAEA,UAAA,UAAA,EAAA;AACA,+DAAA,KAAA;AACA,OAFA,MAEA;AACA,yCAAA,KAAA;AACA;AACA,KApYA;AAsYA;AACA,IAAA,eAvYA,6BAuYA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAAA;AAOA,WAAA,aAAA,GAAA,EAAA,CARA,CASA;;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,CAAA,CAZA,CAcA;;AACA,WAAA,gBAAA,GAfA,CAiBA;;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,EAAA;AACA,aAAA,KAAA,CAAA,cAAA,CAAA,aAAA;AACA;;AACA,UAAA,KAAA,KAAA,CAAA,YAAA,EAAA;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,YAAA;AACA;;AAEA,WAAA,YAAA;AACA,WAAA,aAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,WAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,KAtaA;AAwaA;AACA,IAAA,iBAzaA,+BAyaA;AACA;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,KAAA,aAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KA9aA;AAgbA;AACA,IAAA,YAjbA,0BAibA;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,KAAA,aAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;;AAEA,UAAA,KAAA,aAAA,EAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,OADA;AAEA,UAAA,WAAA,sCAAA,KAAA,aAAA,+BAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,KA7bA;AA+bA;AACA,IAAA,gBAhcA,8BAgcA;AACA,WAAA,aAAA,GAAA,EAAA;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AAEA,WAAA,aAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,UAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,KA3cA;AA6cA,IAAA,cA7cA,0BA6cA,QA7cA,EA6cA;AACA,WAAA,cAAA,CAAA,QAAA,GAAA,QAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAjdA;AAmdA,IAAA,gBAndA,4BAmdA,KAndA,EAmdA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,KAAA;AACA,WAAA,WAAA,GAAA,CAAA,CAFA,CAGA;;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,YAAA;AACA,KA1dA;AA4dA,IAAA,UA5dA,sBA4dA,QA5dA,EA4dA;AACA,WAAA,cAAA,CAAA,QAAA,GAAA,QAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAheA;AAkeA,IAAA,eAleA,2BAkeA,QAleA,EAkeA;AACA,UAAA,KAAA,GAAA;AACA;AACA,mBAAA,IAFA;AAGA,oBAAA,IAHA;AAIA,oBAAA,IAJA;AAKA,oBAAA,IALA;AAMA,wBAAA,IANA;AAOA,iBAAA,IAPA;AAQA,yBAAA,IARA;AASA,mBAAA,IATA;AASA;AACA,iBAAA,IAVA;AAYA;AACA,gBAAA,IAbA;AAcA,gBAAA,IAdA;AAeA,gBAAA,IAfA;AAgBA,gBAAA,IAhBA;AAiBA,gBAAA,IAjBA;AAkBA,gBAAA,IAlBA;AAmBA,gBAAA,IAnBA;AAoBA,gBAAA,IApBA;AAqBA,gBAAA,IArBA;AAsBA,gBAAA,IAtBA;AAuBA,gBAAA,IAvBA;AAwBA,gBAAA,IAxBA;AAyBA,eAAA,IAzBA;AA0BA,cAAA,IA1BA;AA2BA,gBAAA,IA3BA;AA2BA;AACA,cAAA;AA5BA,OAAA;AA8BA,aAAA,KAAA,CAAA,QAAA,CAAA,IAAA,IAAA;AACA,KAlgBA;AAogBA,IAAA,eApgBA,2BAogBA,aApgBA,EAogBA;AACA,UAAA,QAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,UAAA,GAAA;AAAA,eAAA,GAAA,CAAA,KAAA,KAAA,aAAA;AAAA,OAAA,CAAA;AACA,aAAA,QAAA,GAAA,QAAA,CAAA,IAAA,GAAA,aAAA;AACA,KAvgBA;AAygBA;AACA,IAAA,iBA1gBA,6BA0gBA,UA1gBA,EA0gBA;AACA,UAAA,UAAA,GAAA;AACA,eAAA,SADA;AAEA,eAAA,SAFA;AAGA,cAAA;AAHA,OAAA,CADA,CAOA;;AACA,UAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,UAAA,CAAA;AACA,OAVA,CAYA;;;AACA,UAAA,UAAA,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AAAA,gCACA,UAAA,CAAA,KAAA,CAAA,GAAA,CADA;AAAA;AAAA,YACA,GADA;AAAA,YACA,GADA;;AAEA,YAAA,GAAA,KAAA,KAAA,EAAA;AACA,+BAAA,GAAA;AACA,SAFA,MAEA;AACA,+BAAA,GAAA,oBAAA,GAAA;AACA;AACA;;AAEA,aAAA,UAAA;AACA,KAjiBA;AAmiBA;AACA,IAAA,eApiBA,2BAoiBA,QApiBA,EAoiBA;AACA,UAAA,SAAA,GAAA;AACA,kBAAA,MADA;AAEA,qBAAA,QAFA;AAGA,sBAAA,QAHA;AAIA,oBAAA;AAJA,OAAA;AAMA,aAAA,SAAA,CAAA,QAAA,CAAA,IAAA,QAAA;AACA,KA5iBA;AA8iBA,IAAA,mBA9iBA,iCA8iBA;AACA,WAAA,cAAA,CAAA,QAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAljBA;AAojBA,IAAA,kBApjBA,gCAojBA;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAzjBA;AA2jBA,IAAA,gBA3jBA,8BA2jBA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAlkBA;AAokBA,IAAA,eApkBA,6BAokBA;AACA,WAAA,cAAA,CAAA,QAAA,GAAA,SAAA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,YAAA;AACA,KAxkBA;AA0kBA;AACA,IAAA,aA3kBA,yBA2kBA,OA3kBA,EA2kBA;AACA,WAAA,iBAAA,CAAA,OAAA,IAAA,CAAA,KAAA,iBAAA,CAAA,OAAA,CAAA;AACA,KA7kBA;AA+kBA;AACA,IAAA,gBAhlBA,8BAglBA;AACA,UAAA,KAAA,cAAA,KAAA,IAAA,IAAA,KAAA,cAAA,KAAA,IAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,cAAA,IAAA,CAAA;AACA,YAAA,GAAA,GAAA,KAAA,cAAA,IAAA,GAAA;AACA,aAAA,cAAA,CAAA,UAAA,aAAA,GAAA,cAAA,GAAA;AACA,aAAA,WAAA,GAAA,CAAA;AACA,aAAA,YAAA;AACA,aAAA,aAAA,CAAA,OAAA,CAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,WAAA,gEAAA,GAAA,gBAAA,GAAA,KAAA,GAAA,GAAA,IAAA,GAAA,MAAA,GAAA,CAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SAAA;AAKA;AACA,KA7lBA;AA+lBA;AACA,IAAA,iBAhmBA,+BAgmBA;AACA,WAAA,eAAA,GAAA,CAAA,KAAA,eAAA;;AACA,UAAA,KAAA,eAAA,EAAA,CACA;AACA;AACA,OAHA,MAGA;AACA;AACA,YAAA,KAAA,cAAA,CAAA,UAAA,IAAA,KAAA,cAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA,QAAA,CAAA,KAAA,cAAA,CAAA,UAAA,CAAA,EAAA;AACA,eAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,eAAA,YAAA;AACA;AACA;AACA,KA5mBA;AA8mBA;AACA,IAAA,eA/mBA,2BA+mBA,OA/mBA,EA+mBA;AACA,UAAA;AACA,YAAA,KAAA,GAAA;AACA,UAAA,QAAA,EAAA,OAAA,CAAA,QAAA,IAAA,EADA;AAEA,UAAA,MAAA,EAAA,OAAA,CAAA,OAAA,IAAA,KAAA,aAAA,IAAA,EAFA;AAGA,UAAA,UAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAHA;AAIA,UAAA,MAAA,EAAA,OAAA,CAAA,QAAA,IAAA,SAJA;AAKA,UAAA,SAAA,EAAA,IAAA,CAAA,GAAA;AALA,SAAA;AAQA,QAAA,YAAA,CAAA,OAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA;AACA,OAVA,CAUA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,IAAA,CAAA,aAAA,EAAA,KAAA;AACA;AACA,KA7nBA;AA+nBA;AACA,IAAA,kBAhoBA,gCAgoBA;AACA,UAAA;AACA,YAAA,QAAA,GAAA,YAAA,CAAA,OAAA,CAAA,qBAAA,CAAA;AACA,YAAA,CAAA,QAAA,EAAA;AAEA,YAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAJA,CAMA;;AACA,YAAA,GAAA,GAAA,IAAA,CAAA,GAAA,EAAA;AACA,YAAA,QAAA,GAAA,GAAA,IAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA;AACA,YAAA,MAAA,GAAA,KAAA,EAAA,GAAA,EAAA,GAAA,IAAA,CATA,CASA;;AAEA,YAAA,QAAA,GAAA,MAAA,EAAA;AACA,UAAA,YAAA,CAAA,UAAA,CAAA,qBAAA;AACA;AACA,SAdA,CAgBA;;;AACA,YAAA,KAAA,CAAA,QAAA,EAAA;AACA,eAAA,cAAA,CAAA,QAAA,GAAA,KAAA,CAAA,QAAA;AACA;;AACA,YAAA,KAAA,CAAA,MAAA,EAAA;AACA,eAAA,cAAA,CAAA,OAAA,GAAA,KAAA,CAAA,MAAA;AACA,eAAA,aAAA,GAAA,KAAA,CAAA,MAAA;AACA;;AACA,YAAA,KAAA,CAAA,UAAA,EAAA;AACA,eAAA,cAAA,CAAA,UAAA,GAAA,KAAA,CAAA,UAAA;AACA;;AACA,YAAA,KAAA,CAAA,MAAA,IAAA,KAAA,CAAA,MAAA,KAAA,SAAA,EAAA;AACA,eAAA,cAAA,CAAA,QAAA,GAAA,KAAA,CAAA,MAAA;AACA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA;AAEA,OAjCA,CAiCA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,IAAA,CAAA,aAAA,EAAA,KAAA;AACA,QAAA,YAAA,CAAA,UAAA,CAAA,qBAAA;AACA;AACA,KAtqBA;AAwqBA;AACA,IAAA,gBAzqBA,8BAyqBA;AACA,UAAA;AACA,QAAA,YAAA,CAAA,UAAA,CAAA,qBAAA;AACA,OAFA,CAEA,OAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,IAAA,CAAA,aAAA,EAAA,KAAA;AACA;AACA,KA/qBA;AAirBA;AACA,IAAA,yBAlrBA;AAAA,kHAkrBA,MAlrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmrBA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,MAAA,CAAA,YAAA;AAEA,qBAAA,sBAAA,GAAA,MAAA;AACA,qBAAA,iBAAA,GAAA,IAAA;AACA,qBAAA,oBAAA,GAAA,IAAA;AACA,qBAAA,kBAAA,GAAA,EAAA,CAxrBA,CA0rBA;;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CA3rBA,CA6rBA;;AACA,oBAAA;AACA,kBAAA,UADA,GACA,KAAA,6BAAA,CAAA,MAAA,CAAA,YAAA,CADA;AAEA,uBAAA,kBAAA,GAAA,UAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,UAAA,CAAA,MAAA;AACA,iBAJA,CAIA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA,KAAA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,WAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,iBAXA,SAWA;AACA,uBAAA,oBAAA,GAAA,KAAA;AACA;;AA3sBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8sBA;AACA,IAAA,qBA/sBA,iCA+sBA,SA/sBA,EA+sBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,SAAA,CAAA,QAAA;AACA,WAAA,iBAAA,GAAA,KAAA,CAFA,CAIA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,cADA;AAEA,QAAA,MAAA,EAAA;AAAA,UAAA,EAAA,EAAA,SAAA,CAAA;AAAA;AAFA,OAAA;AAIA,KAxtBA;AA0tBA;AACA,IAAA,kBA3tBA,gCA2tBA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,WAAA,sBAAA,GAAA,IAAA;AACA,WAAA,kBAAA,GAAA,EAAA,CAHA,CAKA;;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,EAAA;AACA,KAluBA;AAouBA;AACA,IAAA,eAruBA,2BAquBA,SAruBA,EAquBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,SAAA,CAAA,QAAA;AACA,WAAA,kBAAA,GAFA,CAIA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,cADA;AAEA,QAAA,MAAA,EAAA;AAAA,UAAA,EAAA,EAAA,SAAA,CAAA;AAAA;AAFA,OAAA;AAIA,KA9uBA;AAgvBA;AACA,IAAA,cAjvBA,0BAivBA,MAjvBA,EAivBA;AACA,aAAA,iBAAA,CAAA,MAAA,EAAA,KAAA,kBAAA,CAAA;AACA,KAnvBA;AAqvBA;AACA,IAAA,iBAtvBA,+BAsvBA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,gBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA,KAzvBA;AA2vBA;AACA;AACA,IAAA,iBA7vBA,+BA6vBA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,kBAAA;AACA;AAhwBA;AArJA,CAAA", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"market-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">AI插件中心</h1>\n        <p class=\"simple-subtitle\">发现优质AI插件，提升创作效率，让每个想法都能完美实现</p>\n      </div>\n\n      <!-- 🔥 搜索区域 -->\n      <section class=\"search-section\">\n        <div class=\"container\">\n          <div class=\"search-layout\">\n            <div class=\"search-input-group\">\n              <!-- 🔥 使用原生输入框，完全自定义样式 -->\n              <div class=\"custom-search-input\">\n                <div class=\"search-icon\">\n                  <a-icon type=\"search\" />\n                </div>\n                <input\n                  v-model=\"searchKeyword\"\n                  @input=\"handleSearchInput\"\n                  @keyup.enter=\"handleSearch\"\n                  placeholder=\"搜索插件名称、描述...\"\n                  class=\"search-input-native\"\n                />\n                <!-- 🔥 清空搜索框图标 -->\n                <div\n                  v-if=\"searchKeyword\"\n                  class=\"clear-search-icon\"\n                  @click=\"clearSearchInput\"\n                  title=\"清空搜索\"\n                >\n                  <a-icon type=\"close-circle\" />\n                </div>\n              </div>\n\n              <!-- 🔥 清空筛选按钮移到搜索框右边 -->\n              <a-button\n                @click=\"clearAllFilters\"\n                size=\"large\"\n                class=\"clear-filters-btn\"\n                :disabled=\"!hasActiveFilters\">\n                <a-icon type=\"clear\" />\n                清空所有筛选\n              </a-button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 主内容区域 -->\n      <section class=\"main-content\">\n        <div class=\"container\">\n          <div class=\"content-layout\">\n            <!-- 左侧筛选栏 -->\n            <aside class=\"sidebar\">\n              <div class=\"filter-panel\">\n\n                <!-- 分类筛选 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('category')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"appstore\" class=\"filter-icon\" />\n                      插件分类\n                      <span class=\"filter-badge\" v-if=\"currentFilters.category\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.category ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.category\" class=\"filter-content\">\n                      <div class=\"category-grid\">\n                        <div\n                          class=\"category-tag\"\n                          :class=\"{ active: currentFilters.category === '' }\"\n                          @click=\"selectCategory('')\"\n                        >\n                          <span class=\"tag-icon\">🌟</span>\n                          <span class=\"tag-text\">全部</span>\n                          <span class=\"tag-count\">{{ totalPlugins }}</span>\n                        </div>\n                        <div\n                          v-for=\"category in categories\"\n                          :key=\"category.value\"\n                          class=\"category-tag\"\n                          :class=\"{ active: currentFilters.category === category.value }\"\n                          @click=\"selectCategory(category.value)\"\n                        >\n                          <span class=\"tag-icon\">{{ getCategoryIcon(category.value) }}</span>\n                          <span class=\"tag-text\">{{ category.text }}</span>\n                          <span class=\"tag-count\">{{ categoryCounts[category.value] || 0 }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n                <!-- 价格筛选 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('price')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"dollar\" class=\"filter-icon\" />\n                      价格范围\n                      <span class=\"filter-badge\" v-if=\"currentFilters.priceRange\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.price ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.price\" class=\"filter-content\">\n                      <div class=\"price-grid\">\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '' && !showCustomPrice }\"\n                          @click=\"selectPriceRange('')\"\n                        >\n                          <span class=\"tag-icon\">💰</span>\n                          <span class=\"tag-text\">全部</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '0-1' }\"\n                          @click=\"selectPriceRange('0-1')\"\n                        >\n                          <span class=\"tag-icon\">🪙</span>\n                          <span class=\"tag-text\">¥0-1</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '1-5' }\"\n                          @click=\"selectPriceRange('1-5')\"\n                        >\n                          <span class=\"tag-icon\">💵</span>\n                          <span class=\"tag-text\">¥1-5</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '5+' }\"\n                          @click=\"selectPriceRange('5+')\"\n                        >\n                          <span class=\"tag-icon\">💎</span>\n                          <span class=\"tag-text\">¥5+</span>\n                        </div>\n                      </div>\n                      <!-- 自定义价格范围 -->\n                      <div class=\"custom-price-range\">\n                        <div class=\"custom-price-header\">\n                          <span class=\"custom-price-icon\">⚙️</span>\n                          <span class=\"custom-price-label\">自定义价格</span>\n                        </div>\n                        <div class=\"price-inputs\">\n                          <div class=\"price-input-row\">\n                            <label class=\"input-label\">最低价格</label>\n                            <a-input-number\n                              v-model=\"customPriceMin\"\n                              :min=\"0\"\n                              :max=\"999\"\n                              placeholder=\"请输入最低价格\"\n                              size=\"default\"\n                              class=\"price-input\"\n                              @pressEnter=\"applyCustomPrice\"\n                            />\n                          </div>\n                          <div class=\"price-input-row\">\n                            <label class=\"input-label\">最高价格</label>\n                            <a-input-number\n                              v-model=\"customPriceMax\"\n                              :min=\"customPriceMin || 0\"\n                              :max=\"999\"\n                              placeholder=\"请输入最高价格\"\n                              size=\"default\"\n                              class=\"price-input\"\n                              @pressEnter=\"applyCustomPrice\"\n                            />\n                          </div>\n                          <a-button\n                            type=\"primary\"\n                            @click=\"applyCustomPrice\"\n                            :disabled=\"!customPriceMin && !customPriceMax\"\n                            class=\"apply-custom-btn\"\n                            block\n                          >\n                            确定筛选\n                          </a-button>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n                <!-- 排序方式 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('sort')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"sort-ascending\" class=\"filter-icon\" />\n                      排序方式\n                      <span class=\"filter-badge\" v-if=\"currentFilters.sortType !== 'default'\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.sort ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.sort\" class=\"filter-content\">\n                      <div class=\"sort-grid\">\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'default' }\"\n                          @click=\"selectSort('default')\"\n                        >\n                          <span class=\"tag-icon\">🌟</span>\n                          <span class=\"tag-text\">默认</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'newest' }\"\n                          @click=\"selectSort('newest')\"\n                        >\n                          <span class=\"tag-icon\">⏰</span>\n                          <span class=\"tag-text\">最新</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'price-asc' }\"\n                          @click=\"selectSort('price-asc')\"\n                        >\n                          <span class=\"tag-icon\">📈</span>\n                          <span class=\"tag-text\">价格↑</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'price-desc' }\"\n                          @click=\"selectSort('price-desc')\"\n                        >\n                          <span class=\"tag-icon\">📉</span>\n                          <span class=\"tag-text\">价格↓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n\n              </div>\n            </aside>\n\n            <!-- 右侧主内容 -->\n            <main class=\"main-area\">\n              <!-- 结果头部 -->\n              <div class=\"results-header\">\n                <div class=\"header-left\">\n                  <h2 class=\"results-title\">\n                    <span v-if=\"currentFilters.category\">{{ getCategoryText(currentFilters.category) }}插件</span>\n                    <span v-else-if=\"currentFilters.keyword\">搜索结果</span>\n                    <span v-else>全部插件</span>\n                  </h2>\n                  <div class=\"active-filters\" v-if=\"currentFilters.keyword || currentFilters.priceRange || currentFilters.sortType !== 'default'\">\n                    <a-tag\n                      v-if=\"currentFilters.keyword\"\n                      closable\n                      color=\"green\"\n                      @close=\"clearKeywordFilter\"\n                      class=\"filter-tag\"\n                    >\n                      \"{{ currentFilters.keyword }}\"\n                    </a-tag>\n                    <a-tag\n                      v-if=\"currentFilters.priceRange\"\n                      closable\n                      color=\"orange\"\n                      @close=\"clearPriceFilter\"\n                      class=\"filter-tag\"\n                    >\n                      {{ getPriceRangeText(currentFilters.priceRange) }}\n                    </a-tag>\n                    <a-tag\n                      v-if=\"currentFilters.sortType !== 'default'\"\n                      closable\n                      color=\"purple\"\n                      @close=\"clearSortFilter\"\n                      class=\"filter-tag\"\n                    >\n                      {{ getSortTypeText(currentFilters.sortType) }}\n                    </a-tag>\n                  </div>\n                </div>\n                <div class=\"header-right\">\n                  <div class=\"results-count\">\n                    <span class=\"count-number\">{{ filteredPlugins.length }}</span> 个一级插件，共 <span class=\"count-number\">{{ filteredTotalPlugins }}</span> 个插件\n                  </div>\n                </div>\n              </div>\n\n              <!-- 插件网格 -->\n              <div class=\"plugins-grid-wrapper\">\n                <PluginGrid\n                  :plugins=\"currentPagePlugins\"\n                  :loading=\"loading\"\n                  :error=\"error\"\n                  @plugin-use=\"handlePluginUse\"\n                  @plugin-detail=\"handlePluginDetail\"\n                  @combined-plugin-detail=\"viewCombinedPluginDetails\"\n                  @retry=\"handleRetry\"\n                  @clear-filters=\"clearAllFilters\"\n                />\n              </div>\n\n              <!-- 分页 -->\n              <div class=\"pagination-wrapper\" v-if=\"filteredPlugins.length > pageSize\">\n                <a-pagination\n                  :current=\"currentPage\"\n                  :total=\"filteredPlugins.length\"\n                  :page-size=\"pageSize\"\n                  :page-size-options=\"['8', '12', '16', '24']\"\n                  :show-size-changer=\"true\"\n                  :show-quick-jumper=\"true\"\n                  :show-total=\"(total, range) => `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n                  @change=\"handlePageChange\"\n                  @showSizeChange=\"handlePageSizeChange\"\n                />\n              </div>\n            </main>\n          </div>\n        </div>\n      </section>\n    </div>\n\n    <!-- 🔥 组合插件子插件选择弹窗 -->\n    <div v-if=\"showCombinedModal\" class=\"combined-modal-overlay\" @click=\"closeCombinedModal\">\n      <div class=\"combined-modal-content\" @click.stop>\n        <div class=\"combined-modal-header\">\n          <div class=\"header-content1\">\n            <div class=\"header-icon\">🔗</div>\n            <div class=\"header-text\">\n              <h2>选择插件</h2>\n              <p class=\"header-subtitle\">{{ selectedCombinedPlugin && selectedCombinedPlugin.combinedName }}</p>\n            </div>\n          </div>\n          <button class=\"combined-modal-close\" @click=\"closeCombinedModal\">×</button>\n        </div>\n        <div class=\"combined-modal-body\">\n          <div v-if=\"combinedModalLoading\" class=\"loading-state\">\n            <a-spin size=\"large\" />\n            <p>正在加载子插件...</p>\n          </div>\n          <div v-else-if=\"combinedSubPlugins.length === 0\" class=\"empty-state\">\n            <p>暂无子插件</p>\n          </div>\n          <div v-else class=\"sub-plugins-grid\">\n            <div\n              v-for=\"subPlugin in combinedSubPlugins\"\n              :key=\"subPlugin.id\"\n              class=\"sub-plugin-card\"\n              @click=\"selectSubPlugin(subPlugin)\">\n              <!-- 🔥 插件图片区域 -->\n              <div class=\"sub-plugin-image\">\n                <img :src=\"getPluginImage(subPlugin)\" :alt=\"subPlugin.plubname\" />\n                <!-- 分类标签 -->\n                <div class=\"sub-plugin-category\">\n                  <span class=\"category-icon\">🔧</span>\n                  <span>{{ subPlugin.plubCategory_dictText || '其他' }}</span>\n                </div>\n                <!-- 状态标签 -->\n                <div class=\"sub-plugin-status\">上架</div>\n              </div>\n\n              <!-- 🔥 插件内容区域 -->\n              <div class=\"sub-plugin-content\">\n                <div class=\"sub-plugin-header\">\n                  <h3 class=\"sub-plugin-title\">{{ subPlugin.plubname }}</h3>\n                  <div class=\"sub-plugin-author\">\n                    <span class=\"author-icon\">👤</span>\n                    <span>创作者 {{ subPlugin.plubwrite_dictText || '未知' }}</span>\n                  </div>\n                </div>\n\n                <p class=\"sub-plugin-description\">{{ subPlugin.plubinfo || '暂无描述' }}</p>\n\n                <div class=\"sub-plugin-footer\">\n                  <div class=\"sub-plugin-price\">\n                    <span class=\"price-amount\">{{ getSubPluginPriceText(subPlugin) }}</span>\n                  </div>\n                  <button class=\"sub-plugin-btn\">\n                    <span class=\"btn-icon\">👁</span>\n                    <span>查看详情</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 🔥 悬浮式剪映小助手推广组件 -->\n    <div v-if=\"showJianYingFloat\" class=\"jianying-float-container\">\n      <button class=\"jianying-float-btn\" @click=\"goToJianYingDraft\">\n        <!-- 关闭按钮 -->\n        <div class=\"float-close-btn\" @click.stop=\"hideJianYingFloat\">\n          <a-icon type=\"close\" />\n        </div>\n\n        <!-- 按钮图标 -->\n        <div class=\"btn-icon\">\n          <a-icon type=\"download\" />\n        </div>\n\n        <!-- 按钮文字 -->\n        <div class=\"btn-text\">剪映小助手下载</div>\n\n        <!-- 发光效果 -->\n        <div class=\"btn-glow\"></div>\n\n        <!-- 粒子效果容器 -->\n        <div class=\"btn-particles\" ref=\"particles\"></div>\n\n        <!-- 波纹效果 -->\n        <div class=\"jianying-waves\"></div>\n      </button>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport CategoryFilter from './components/CategoryFilter.vue'\nimport SearchFilter from './components/SearchFilter.vue'\nimport PluginGrid from './components/PluginGrid.vue'\nimport marketApi from '@/api/market'\nimport { formatCategories, validatePluginData, getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils'\nimport { HeartbeatMixin } from '@/mixins/HeartbeatMixin'\nimport { getCurrentPageConfig } from '@/utils/heartbeatConfig'\n\nexport default {\n  name: 'Market', // 确保组件名称与路由配置中的componentName一致\n  mixins: [HeartbeatMixin],\n  components: {\n    WebsitePage,\n    CategoryFilter,\n    SearchFilter,\n    PluginGrid\n  },\n\n  data() {\n    return {\n      // 心跳配置 - 商城页面使用中频心跳\n      heartbeatConfig: getCurrentPageConfig('market', {\n        apiKey: 'market-page-heartbeat-key', // 商城页面专用API密钥\n        enableDebugLog: process.env.NODE_ENV === 'development',\n      }),\n\n      // 插件数据\n      allPlugins: [],\n      filteredPlugins: [],\n      currentPagePlugins: [],\n      originalPluginsData: [], // 🔥 存储原始插件数据（用于搜索子插件）\n\n      // 分类数据\n      categories: [],\n      categoryCounts: {},\n\n      // 分页状态\n      currentPage: 1,\n      pageSize: 12,\n\n      // 筛选条件\n      currentFilters: {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      },\n\n      // 加载状态\n      loading: false,\n      error: null,\n\n      // 统计数据\n      totalPlugins: 0,\n\n      // 搜索相关\n      searchKeyword: '',\n      showSuggestions: false,\n      suggestions: ['文案生成', '图片处理', '视频剪辑', '数据分析', '代码助手'],\n\n      // 筛选面板折叠状态\n      collapsedSections: {\n        search: false,\n        category: false,\n        price: true,\n        sort: true\n      },\n\n      // 自定义价格范围\n      showCustomPrice: false,\n      customPriceMin: null,\n      customPriceMax: null,\n\n      // 🔥 组合插件弹窗\n      showCombinedModal: false,\n      selectedCombinedPlugin: null,\n      combinedModalLoading: false,\n      combinedSubPlugins: [],\n\n      // 🔥 悬浮式剪映小助手推广组件\n      showJianYingFloat: true // 是否显示悬浮组件\n    }\n  },\n\n  computed: {\n    hasActiveFilters() {\n      return this.currentFilters.category ||\n             this.currentFilters.keyword ||\n             this.currentFilters.priceRange ||\n             this.currentFilters.sortType !== 'default'\n    },\n\n    // 🔥 计算原始插件总数（包括组合插件的所有子插件）\n    totalOriginalPlugins() {\n      return this.originalPluginsData ? this.originalPluginsData.length : 0\n    },\n\n    // 🔥 计算筛选后的总插件数量（包括组合插件的子插件）\n    filteredTotalPlugins() {\n      let totalCount = 0\n\n      this.filteredPlugins.forEach(plugin => {\n        if (plugin.isCombined === 1) {\n          // 组合插件：计算其子插件数量\n          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n          totalCount += subPlugins.length\n        } else {\n          // 普通插件：计数为1\n          totalCount += 1\n        }\n      })\n\n      return totalCount\n    },\n\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    }\n  },\n\n  async created() {\n    // 悬浮组件默认显示，不需要检查localStorage\n    this.showJianYingFloat = true\n    console.log('🔥 悬浮组件默认显示')\n\n    // 只有在没有数据时才初始化，避免重复加载\n    if (this.allPlugins.length === 0) {\n      await this.initializeMarket()\n    } else {\n      // 如果已有数据，只恢复筛选状态\n      this.restoreMarketState()\n      this.applyFilters()\n      console.log('商城数据已存在，跳过重新加载')\n    }\n  },\n\n  // 组件销毁前恢复滚动\n  beforeDestroy() {\n    document.body.style.overflow = ''\n  },\n\n  // 监听筛选条件变化，保存到localStorage\n  watch: {\n    currentFilters: {\n      handler(newFilters) {\n        this.saveMarketState(newFilters);\n      },\n      deep: true\n    },\n\n    searchKeyword(newKeyword) {\n      this.saveMarketState({ ...this.currentFilters, keyword: newKeyword });\n    }\n  },\n\n  methods: {\n    // 初始化商城\n    async initializeMarket() {\n      this.loading = true\n\n      try {\n        // 恢复筛选状态\n        this.restoreMarketState();\n\n        // 并行加载分类和插件数据\n        await Promise.all([\n          this.loadCategories(),\n          this.loadPlugins()\n        ])\n\n        console.log('商城初始化完成')\n\n      } catch (error) {\n        console.error('商城初始化失败:', error)\n        this.error = '商城初始化失败，请刷新页面重试'\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载分类数据\n    async loadCategories() {\n      try {\n        const response = await marketApi.getPluginCategories()\n        if (response.success) {\n          this.categories = formatCategories(response.result || [])\n          console.log('分类数据加载成功:', this.categories)\n        }\n      } catch (error) {\n        console.error('加载分类数据失败:', error)\n      }\n    },\n\n    // 加载插件数据\n    async loadPlugins() {\n      try {\n        const params = {\n          pageNo: 1,\n          pageSize: 1000, // 获取所有数据，前端分页\n          status: 1 // 只获取已上架的插件\n        }\n\n        const response = await marketApi.getPluginList(params)\n\n        if (response.success) {\n          const result = response.result || response.data\n          const originalData = result.records || result || []\n\n          // 🔥 保存原始数据（用于搜索子插件）\n          this.originalPluginsData = [...originalData]\n\n          // 🔥 前端处理组合插件去重逻辑（使用统一工具函数）\n          const processedPlugins = processPluginsWithCombined(originalData)\n\n          this.allPlugins = processedPlugins\n          this.totalPlugins = processedPlugins.length\n\n          // 计算分类统计\n          this.calculateCategoryCounts()\n\n          // 应用筛选\n          this.applyFilters()\n\n          console.log('插件数据加载成功:', this.allPlugins.length, '个插件（包含组合插件）')\n        } else {\n          throw new Error(response.message || '获取插件数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载插件数据失败:', error)\n        this.error = error.message || '加载插件数据失败'\n      }\n    },\n\n\n\n    // 🔥 检查组合插件是否包含指定分类\n    combinedPluginHasCategory(combinedPlugin, targetCategory) {\n      // 简化版本：直接检查组合插件本身的分类\n      // 在实际应用中，这里应该查询该组合插件的所有子插件\n      // 但为了避免复杂的异步查询，我们先用简化逻辑\n      return combinedPlugin.plubCategory === targetCategory\n    },\n\n    // 🔥 计算分类统计（支持组合插件）\n    calculateCategoryCounts() {\n      this.categoryCounts = {}\n\n      this.categories.forEach(category => {\n        this.categoryCounts[category.value] = 0\n      })\n\n      // 统计组合插件数量\n      let combinedPluginCount = 0\n\n      this.allPlugins.forEach(plugin => {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件计数\n          combinedPluginCount++\n\n          // 组合插件也按其分类计数（用于其他分类的统计）\n          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            this.categoryCounts[plugin.plubCategory]++\n          }\n        } else {\n          // 普通插件计数\n          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            this.categoryCounts[plugin.plubCategory]++\n          }\n        }\n      })\n\n      // 设置组合插件分类的数量\n      this.categoryCounts['combine'] = combinedPluginCount\n\n      console.log('🔥 分类统计完成:', this.categoryCounts)\n    },\n\n    // 🔥 应用筛选条件（支持组合插件）\n    applyFilters() {\n      let filtered = [...this.allPlugins]\n\n      // 🔥 分类筛选（支持组合插件）\n      if (this.currentFilters.category) {\n        if (this.currentFilters.category === 'combine') {\n          // 只显示组合插件\n          filtered = filtered.filter(plugin => plugin.isCombined === 1 || plugin.isCombined === '1')\n        } else {\n          // 显示指定分类的普通插件 + 包含该分类的组合插件\n          filtered = filtered.filter(plugin => {\n            // 普通插件：直接匹配分类\n            if (plugin.isCombined !== 1 && plugin.isCombined !== '1') {\n              return plugin.plubCategory === this.currentFilters.category\n            }\n            // 组合插件：需要检查是否包含该分类的子插件\n            return this.combinedPluginHasCategory(plugin, this.currentFilters.category)\n          })\n        }\n      }\n\n      // 🔥 关键词筛选（支持组合插件及其子插件）\n      if (this.currentFilters.keyword) {\n        const keyword = this.currentFilters.keyword.toLowerCase()\n        filtered = this.filterByKeyword(filtered, keyword)\n      }\n\n      // 价格范围筛选（只对普通插件有效）\n      if (this.currentFilters.priceRange) {\n        filtered = this.filterByPriceRange(filtered, this.currentFilters.priceRange)\n      }\n\n      // 排序（包括默认排序）\n      filtered = this.sortPlugins(filtered, this.currentFilters.sortType)\n\n      this.filteredPlugins = filtered\n      this.updateCurrentPagePlugins()\n    },\n\n    // 🔥 关键词筛选（支持搜索组合插件的子插件）\n    filterByKeyword(plugins, keyword) {\n      return plugins.filter(plugin => {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件：搜索组合插件名、描述和子插件名\n\n          // 1. 搜索组合插件本身的名称和描述\n          if ((plugin.combinedName && plugin.combinedName.toLowerCase().includes(keyword)) ||\n              (plugin.combinedDescription && plugin.combinedDescription.toLowerCase().includes(keyword))) {\n            return true\n          }\n\n          // 2. 搜索组合插件的子插件名称\n          // 从原始数据中查找同名的组合插件的所有子插件\n          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n          return subPlugins.some(subPlugin =>\n            subPlugin.plubname && subPlugin.plubname.toLowerCase().includes(keyword)\n          )\n        } else {\n          // 普通插件：搜索插件名和描述\n          return (plugin.plubname && plugin.plubname.toLowerCase().includes(keyword)) ||\n                 (plugin.plubinfo && plugin.plubinfo.toLowerCase().includes(keyword))\n        }\n      })\n    },\n\n    // 🔥 从原始数据中获取组合插件的子插件\n    getSubPluginsFromOriginalData(combinedName) {\n      // 从loadPlugins时获取的原始数据中查找\n      // 这里需要访问处理前的原始插件数据\n      if (!this.originalPluginsData) {\n        return []\n      }\n\n      return this.originalPluginsData.filter(plugin =>\n        plugin.isCombined === 1 &&\n        plugin.combinedName === combinedName\n      )\n    },\n\n    // 价格范围筛选\n    filterByPriceRange(plugins, priceRange) {\n      if (!priceRange) return plugins\n\n      return plugins.filter(plugin => {\n        const price = parseFloat(plugin.neednum) || 0\n\n        switch (priceRange) {\n          case '0-1':\n            return price >= 0 && price <= 1\n          case '1-5':\n            return price > 1 && price <= 5\n          case '5+':\n            return price > 5\n          default:\n            // 自定义范围 格式: \"min-max\"\n            if (priceRange.includes('-')) {\n              const [min, max] = priceRange.split('-').map(p => parseFloat(p) || 0)\n              if (max === 999) {\n                return price >= min\n              } else {\n                return price >= min && price <= max\n              }\n            }\n            return true\n        }\n      })\n    },\n\n    // 插件排序\n    sortPlugins(plugins, sortType) {\n      const sorted = [...plugins]\n\n      switch (sortType) {\n        case 'default':\n          // 默认排序：按照aigc_plub_shop表的sort_order字段排序（权重越小越靠前）\n          return sorted.sort((a, b) => {\n            const weightA = parseFloat(a.sortOrder || a.sort_order || 999999) // 如果没有权重，设置为很大的数\n            const weightB = parseFloat(b.sortOrder || b.sort_order || 999999)\n            return weightA - weightB // 权重小的在前\n          })\n        case 'newest':\n          // 按创建时间排序（假设有createTime字段，如果没有可以用id或其他字段）\n          return sorted.sort((a, b) => {\n            const timeA = new Date(a.createTime || a.createBy || 0).getTime()\n            const timeB = new Date(b.createTime || b.createBy || 0).getTime()\n            return timeB - timeA // 最新的在前\n          })\n        case 'price-asc':\n          // 价格从低到高\n          return sorted.sort((a, b) => {\n            const priceA = parseFloat(a.neednum) || 0\n            const priceB = parseFloat(b.neednum) || 0\n            return priceA - priceB\n          })\n        case 'price-desc':\n          // 价格从高到低\n          return sorted.sort((a, b) => {\n            const priceA = parseFloat(a.neednum) || 0\n            const priceB = parseFloat(b.neednum) || 0\n            return priceB - priceA\n          })\n        case 'name-asc':\n          // 名称A-Z\n          return sorted.sort((a, b) => {\n            const nameA = (a.plubname || '').toLowerCase()\n            const nameB = (b.plubname || '').toLowerCase()\n            return nameA.localeCompare(nameB)\n          })\n        default:\n          // 如果是未知的排序类型，也使用默认排序\n          return sorted.sort((a, b) => {\n            const weightA = parseFloat(a.sortWeight || a.sort_weight || 999999)\n            const weightB = parseFloat(b.sortWeight || b.sort_weight || 999999)\n            return weightA - weightB\n          })\n      }\n    },\n\n    // 更新当前页插件\n    updateCurrentPagePlugins() {\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      this.currentPagePlugins = this.filteredPlugins.slice(start, end)\n    },\n\n    // 处理分类变更\n    handleCategoryChange(data) {\n      this.currentFilters.category = data.category\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('分类筛选变更:', data)\n    },\n\n    // 处理搜索变更\n    handleSearchChange(data) {\n      this.currentFilters.keyword = data.keyword\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('搜索变更:', data)\n    },\n\n    // 处理筛选变更\n    handleFilterChange(filters) {\n      this.currentFilters = { ...this.currentFilters, ...filters }\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('筛选条件变更:', filters)\n    },\n\n    // 处理分页变更\n    handlePageChange(page, pageSize) {\n      this.currentPage = page\n      if (pageSize) {\n        this.pageSize = pageSize\n      }\n      this.updateCurrentPagePlugins()\n\n      // 滚动到顶部\n      window.scrollTo({ top: 0, behavior: 'smooth' })\n    },\n\n    // 处理页面大小变更\n    handlePageSizeChange(page, pageSize) {\n      this.currentPage = page\n      this.pageSize = pageSize\n      this.updateCurrentPagePlugins()\n    },\n\n    // 处理插件使用 - 跳转到详情页\n    handlePluginUse(plugin) {\n      console.log('使用插件:', plugin)\n\n      // 检查插件数据有效性\n      if (!validatePluginData(plugin)) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 跳转到插件详情页\n      this.$router.push(`/market/plugin/${plugin.id}`)\n    },\n\n    // 处理插件详情\n    handlePluginDetail(plugin) {\n      console.log('查看插件详情:', plugin)\n\n      // 检查插件数据有效性\n      if (!plugin || !plugin.id) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 跳转到插件详情页\n      this.$router.push(`/market/plugin/${plugin.id}`)\n    },\n\n    // 处理重试\n    handleRetry() {\n      this.error = null\n      this.loadPlugins()\n    },\n\n    // 🔥 获取子插件价格显示文本\n    getSubPluginPriceText(subPlugin) {\n      const price = subPlugin.neednum\n      const isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1'\n\n      if (!price || price <= 0) {\n        return '免费'\n      }\n\n      if (isSvipFree) {\n        return `SVIP免费，低至¥${price}/次`\n      } else {\n        return `低至¥${price}/次`\n      }\n    },\n\n    // 清空所有筛选\n    clearAllFilters() {\n      this.currentFilters = {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      }\n      this.searchKeyword = ''\n      // 清空自定义价格输入框\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.currentPage = 1\n\n      // 清空localStorage中的筛选状态\n      this.clearMarketState()\n\n      // 重置子组件\n      if (this.$refs.categoryFilter) {\n        this.$refs.categoryFilter.resetCategory()\n      }\n      if (this.$refs.searchFilter) {\n        this.$refs.searchFilter.resetFilters()\n      }\n\n      this.applyFilters()\n      this.$notification.info({\n        message: '筛选已清空',\n        description: '已清空所有筛选条件',\n        placement: 'topRight'\n      })\n    },\n\n    // 实时搜索输入处理\n    handleSearchInput() {\n      // 实时更新搜索关键词\n      this.currentFilters.keyword = this.searchKeyword\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    // 🔥 搜索按钮点击或回车搜索\n    handleSearch() {\n      this.currentFilters.keyword = this.searchKeyword\n      this.currentPage = 1\n      this.applyFilters()\n\n      if (this.searchKeyword) {\n        this.$notification.success({\n          message: '搜索执行中',\n          description: `正在搜索\"${this.searchKeyword}\"相关插件`,\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 🔥 清空搜索框\n    clearSearchInput() {\n      this.searchKeyword = ''\n      this.currentFilters.keyword = ''\n      this.currentPage = 1\n      this.applyFilters()\n\n      this.$notification.info({\n        message: '搜索已清空',\n        description: '已清空搜索关键词',\n        placement: 'topRight'\n      })\n    },\n\n    selectCategory(category) {\n      this.currentFilters.category = category\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    selectPriceRange(range) {\n      this.currentFilters.priceRange = range\n      this.currentPage = 1\n      // 选择预设价格范围时清空自定义输入\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.applyFilters()\n    },\n\n    selectSort(sortType) {\n      this.currentFilters.sortType = sortType\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    getCategoryIcon(category) {\n      const icons = {\n        // 按分类值匹配\n        'ai-chat': '💬',\n        'ai-image': '🎨',\n        'ai-video': '🎬',\n        'ai-audio': '🎵',\n        'social-share': '📱',\n        'tools': '⚙️',\n        'entertainment': '🎮',\n        'combine': '🔗', // 🔥 组合插件图标\n        'other': '🔧',\n\n        // 按分类文本匹配（兼容旧数据）\n        '内容生成': '✍️',\n        '图片生成': '🎨',\n        '视频处理': '🎬',\n        '数据分析': '📊',\n        '开发工具': '⚙️',\n        '设计创意': '🎭',\n        '营销工具': '📈',\n        'AI对话': '💬',\n        'AI绘画': '🎨',\n        'AI视频': '🎬',\n        'AI音频': '🎵',\n        '社交分享': '📱',\n        '工具类': '⚙️',\n        '娱乐': '🎮',\n        '组合插件': '🔗', // 🔥 组合插件图标\n        '其他': '🔧'\n      }\n      return icons[category] || '🔧'\n    },\n\n    getCategoryText(categoryValue) {\n      const category = this.categories.find(cat => cat.value === categoryValue)\n      return category ? category.text : categoryValue\n    },\n\n    // 获取价格范围显示文字\n    getPriceRangeText(priceRange) {\n      const priceTexts = {\n        '0-1': '¥0 - ¥1',\n        '1-5': '¥1 - ¥5',\n        '5+': '¥5以上'\n      }\n\n      // 如果是预设范围，返回对应文字\n      if (priceTexts[priceRange]) {\n        return priceTexts[priceRange]\n      }\n\n      // 如果是自定义范围，格式化显示\n      if (priceRange && priceRange.includes('-')) {\n        const [min, max] = priceRange.split('-')\n        if (max === '999') {\n          return `¥${min}以上`\n        } else {\n          return `¥${min} - ¥${max}`\n        }\n      }\n\n      return priceRange\n    },\n\n    // 获取排序方式显示文字\n    getSortTypeText(sortType) {\n      const sortTexts = {\n        'newest': '最新发布',\n        'price-asc': '价格从低到高',\n        'price-desc': '价格从高到低',\n        'name-asc': '名称A-Z'\n      }\n      return sortTexts[sortType] || sortType\n    },\n\n    clearCategoryFilter() {\n      this.currentFilters.category = ''\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearKeywordFilter() {\n      this.currentFilters.keyword = ''\n      this.searchKeyword = ''\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearPriceFilter() {\n      this.currentFilters.priceRange = ''\n      this.showCustomPrice = false\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearSortFilter() {\n      this.currentFilters.sortType = 'default'\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    // 切换筛选面板折叠状态\n    toggleSection(section) {\n      this.collapsedSections[section] = !this.collapsedSections[section]\n    },\n\n    // 应用自定义价格\n    applyCustomPrice() {\n      if (this.customPriceMin !== null || this.customPriceMax !== null) {\n        const min = this.customPriceMin || 0\n        const max = this.customPriceMax || 999\n        this.currentFilters.priceRange = `${min}-${max}`\n        this.currentPage = 1\n        this.applyFilters()\n        this.$notification.success({\n          message: '价格筛选已应用',\n          description: `已应用价格范围：¥${min} - ${max === 999 ? '以上' : '¥' + max}`,\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 切换自定义价格\n    toggleCustomPrice() {\n      this.showCustomPrice = !this.showCustomPrice\n      if (this.showCustomPrice) {\n        // 打开自定义价格时，设置一个特殊标识，不清除筛选\n        // 这样\"全部\"就不会亮起，但也不会有实际的价格筛选\n      } else {\n        // 如果关闭自定义价格，清除自定义筛选\n        if (this.currentFilters.priceRange && this.currentFilters.priceRange.includes('-') && !['0-1', '1-5', '5+'].includes(this.currentFilters.priceRange)) {\n          this.currentFilters.priceRange = ''\n          this.applyFilters()\n        }\n      }\n    },\n\n    // 保存商城筛选状态到localStorage\n    saveMarketState(filters) {\n      try {\n        const state = {\n          category: filters.category || '',\n          search: filters.keyword || this.searchKeyword || '',\n          priceRange: filters.priceRange || '',\n          sortBy: filters.sortType || 'default',\n          timestamp: Date.now()\n        };\n\n        localStorage.setItem('market_filter_state', JSON.stringify(state));\n      } catch (error) {\n        console.warn('保存商城筛选状态失败:', error);\n      }\n    },\n\n    // 恢复商城筛选状态\n    restoreMarketState() {\n      try {\n        const stateStr = localStorage.getItem('market_filter_state');\n        if (!stateStr) return;\n\n        const state = JSON.parse(stateStr);\n\n        // 检查状态是否过期（24小时）\n        const now = Date.now();\n        const stateAge = now - (state.timestamp || 0);\n        const maxAge = 24 * 60 * 60 * 1000; // 24小时\n\n        if (stateAge > maxAge) {\n          localStorage.removeItem('market_filter_state');\n          return;\n        }\n\n        // 恢复筛选状态\n        if (state.category) {\n          this.currentFilters.category = state.category;\n        }\n        if (state.search) {\n          this.currentFilters.keyword = state.search;\n          this.searchKeyword = state.search;\n        }\n        if (state.priceRange) {\n          this.currentFilters.priceRange = state.priceRange;\n        }\n        if (state.sortBy && state.sortBy !== 'default') {\n          this.currentFilters.sortType = state.sortBy;\n        }\n\n        console.log('已恢复商城筛选状态:', state);\n\n      } catch (error) {\n        console.warn('恢复商城筛选状态失败:', error);\n        localStorage.removeItem('market_filter_state');\n      }\n    },\n\n    // 清空筛选状态\n    clearMarketState() {\n      try {\n        localStorage.removeItem('market_filter_state');\n      } catch (error) {\n        console.warn('清空商城筛选状态失败:', error);\n      }\n    },\n\n    // 🔥 查看组合插件详情（显示子插件选择弹窗）\n    async viewCombinedPluginDetails(plugin) {\n      console.log('🔗 查看组合插件详情:', plugin.combinedName)\n\n      this.selectedCombinedPlugin = plugin\n      this.showCombinedModal = true\n      this.combinedModalLoading = true\n      this.combinedSubPlugins = []\n\n      // 禁止背景滚动\n      document.body.style.overflow = 'hidden'\n\n      // 加载子插件\n      try {\n        const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n        this.combinedSubPlugins = subPlugins\n        console.log('🔗 加载子插件成功:', subPlugins.length)\n      } catch (error) {\n        console.error('🔗 加载子插件失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取子插件列表失败',\n          placement: 'topRight'\n        })\n      } finally {\n        this.combinedModalLoading = false\n      }\n    },\n\n    // 🔥 选择子插件（跳转到具体插件详情页）\n    handleSelectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname)\n      this.showCombinedModal = false\n\n      // 跳转到插件详情页\n      this.$router.push({\n        name: 'PluginDetail',\n        params: { id: subPlugin.id }\n      })\n    },\n\n    // 🔥 关闭组合插件弹窗\n    closeCombinedModal() {\n      this.showCombinedModal = false\n      this.selectedCombinedPlugin = null\n      this.combinedSubPlugins = []\n\n      // 恢复背景滚动\n      document.body.style.overflow = ''\n    },\n\n    // 🔥 选择子插件\n    selectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname)\n      this.closeCombinedModal()\n\n      // 跳转到插件详情页\n      this.$router.push({\n        name: 'PluginDetail',\n        params: { id: subPlugin.id }\n      })\n    },\n\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage(plugin) {\n      return getPluginImageUrl(plugin, this.defaultPluginImage)\n    },\n\n    // 🔥 跳转到剪映小助手页面\n    goToJianYingDraft() {\n      this.$router.push('/JianYingDraft')\n      console.log('跳转到剪映小助手页面')\n    },\n\n    // 🔥 悬浮组件交互方法\n    // 隐藏悬浮组件（仅在当前会话中隐藏，刷新页面后重新显示）\n    hideJianYingFloat() {\n      this.showJianYingFloat = false\n      console.log('🔥 隐藏悬浮组件（仅当前会话）')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.market-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 🔥 剪映小助手推广横幅样式 */\n.jianying-banner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n  position: relative;\n  overflow: hidden;\n}\n\n.jianying-banner::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"20\" cy=\"80\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.banner-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  z-index: 1;\n}\n\n.banner-left {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.banner-icon {\n  width: 60px;\n  height: 60px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  color: white;\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n\n.banner-text {\n  color: white;\n}\n\n.banner-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.25rem 0;\n  color: white;\n}\n\n.banner-subtitle {\n  font-size: 1rem;\n  margin: 0;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n.banner-right {\n  flex-shrink: 0;\n}\n\n.jianying-btn.ant-btn {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 48px !important;\n  padding: 0 2rem !important;\n  border-radius: 24px !important;\n  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4) !important;\n  transition: all 0.3s ease !important;\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.jianying-btn.ant-btn:hover:not(:disabled),\n.jianying-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #ee5a24, #d63031) !important;\n  box-shadow: 0 6px 25px rgba(255, 107, 107, 0.5) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.jianying-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #d63031, #c0392b) !important;\n}\n\n.jianying-btn.ant-btn .anticon {\n  font-size: 1.1rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .banner-content {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .banner-left {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .banner-title {\n    font-size: 1.3rem;\n  }\n\n  .banner-subtitle {\n    font-size: 0.9rem;\n  }\n\n  .jianying-btn.ant-btn {\n    width: 100%;\n    max-width: 200px;\n  }\n}\n\n/* 主内容区域 */\n.main-content {\n  background: #f8fafc;\n  min-height: calc(100vh - 200px);\n  padding: 2rem 0;\n}\n\n.content-layout {\n  display: grid;\n  grid-template-columns: 320px 1fr;\n  gap: 2.5rem;\n  align-items: start;\n}\n\n/* 左侧筛选栏 */\n.sidebar {\n  position: sticky;\n  top: 2rem;\n}\n\n.filter-panel {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  border: 1px solid #e2e8f0;\n}\n\n.filter-section {\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.filter-section:last-child {\n  border-bottom: none;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem 1.5rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background: white;\n}\n\n.filter-header:hover {\n  background: #f8fafc;\n}\n\n.filter-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-icon {\n  color: #3b82f6;\n  font-size: 1.1rem;\n}\n\n.filter-badge {\n  background: #3b82f6;\n  color: white;\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  margin-left: 0.5rem;\n  font-weight: 700;\n}\n\n.collapse-icon {\n  color: #64748b;\n  transition: transform 0.2s ease;\n}\n\n.filter-content {\n  padding: 0 1.5rem 1.5rem;\n}\n\n/* 🔥 搜索区域样式 */\n.search-section {\n  background: transparent;\n  padding: 0 0 2rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.search-layout {\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n}\n\n.search-input-group {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n}\n\n/* 🔥 自定义搜索输入框样式 */\n.custom-search-input {\n  flex: 1;\n  position: relative;\n  height: 56px;\n  border-radius: 28px;\n  background: #ffffff;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n}\n\n.custom-search-input::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\n  transform: translateX(-100%);\n  transition: transform 0.6s ease;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.custom-search-input:hover::before {\n  transform: translateX(100%);\n}\n\n.custom-search-input:hover,\n.custom-search-input:focus-within {\n  background: #ffffff;\n  border-color: #3b82f6;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 1.5rem;\n  color: #3b82f6;\n  font-size: 1.2rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n}\n\n.search-input-native {\n  width: 100%;\n  height: 100%;\n  border: none;\n  outline: none;\n  background: transparent;\n  font-size: 1.1rem;\n  font-weight: 500;\n  padding: 0 3rem 0 3.5rem; /* 🔥 右侧增加padding为清空图标留空间 */\n  color: #1e293b;\n  position: relative;\n  z-index: 2;\n}\n\n.search-input-native::placeholder {\n  color: #64748b;\n  font-weight: 400;\n}\n\n/* 🔥 清空搜索图标样式 */\n.clear-search-icon {\n  position: absolute;\n  right: 1.5rem;\n  color: #94a3b8;\n  font-size: 1rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  padding: 0 0.25rem;\n  border-radius: 50%;\n}\n\n.clear-search-icon:hover {\n  color: #ef4444;\n  background: rgba(239, 68, 68, 0.1);\n  transform: scale(1.1);\n}\n\n.clear-search-icon:active {\n  transform: scale(0.95);\n}\n\n/* 🔥 清空筛选按钮新样式 */\n.clear-filters-btn.ant-btn {\n  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 56px !important;\n  padding: 0 2rem !important;\n  border-radius: 28px !important;\n  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3) !important;\n  transition: all 0.3s ease !important;\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n  min-width: 140px;\n  line-height: 56px !important;\n}\n\n.clear-filters-btn.ant-btn:hover:not(:disabled),\n.clear-filters-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #4f46e5, #4338ca) !important;\n  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.clear-filters-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #4338ca, #3730a3) !important;\n}\n\n.clear-filters-btn.ant-btn:disabled,\n.clear-filters-btn.ant-btn[disabled] {\n  background: #e2e8f0 !important;\n  color: #94a3b8 !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n  border: 1px solid #e2e8f0 !important;\n}\n\n.clear-filters-btn.ant-btn .anticon {\n  margin-right: 0.5rem;\n}\n\n\n\n/* 清空筛选按钮 */\n/* 删除重复的红色样式 */\n\n/* 搜索框 */\n.search-input {\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* 分类网格 */\n.category-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.category-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f8fafc;\n  text-align: center;\n}\n\n.category-tag:hover {\n  background: #e2e8f0;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n}\n\n.category-tag.active {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.tag-icon {\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.tag-text {\n  font-weight: 500;\n  font-size: 0.85rem;\n}\n\n.tag-count {\n  font-size: 0.7rem;\n  background: rgba(0, 0, 0, 0.1);\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-weight: 600;\n  min-width: 20px;\n}\n\n.category-tag.active .tag-count {\n  background: rgba(255, 255, 255, 0.25);\n}\n\n/* 价格网格 */\n.price-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.price-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f0fdf4;\n  text-align: center;\n}\n\n.price-tag:hover {\n  background: #dcfce7;\n  border-color: #10b981;\n  transform: translateY(-2px);\n}\n\n.price-tag.active {\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  border-color: #10b981;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n}\n\n/* 排序网格 */\n.sort-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.sort-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #faf5ff;\n  text-align: center;\n}\n\n.sort-tag:hover {\n  background: #f3e8ff;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n}\n\n.sort-tag.active {\n  background: linear-gradient(135deg, #a855f7, #7c3aed);\n  color: white;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);\n}\n\n/* 自定义价格范围 */\n.custom-price-range {\n  margin-top: 1rem;\n  padding: 1.25rem;\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.custom-price-header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.custom-price-icon {\n  font-size: 1.1rem;\n}\n\n.custom-price-label {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-inputs {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.price-input-row {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.input-label {\n  font-size: 0.85rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-input {\n  width: 100%;\n  border-radius: 10px;\n  border: 2px solid #d1d5db;\n  height: 40px;\n  transition: all 0.3s ease;\n}\n\n.price-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n  transform: translateY(-1px);\n}\n\n.apply-custom-btn {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  height: 44px;\n  font-size: 0.9rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  margin-top: 0.5rem;\n}\n\n.apply-custom-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #1d4ed8, #1e40af);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n.apply-custom-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  box-shadow: none;\n  transform: none;\n}\n\n/* 右侧主内容 */\n.main-area {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n/* 结果头部 */\n.results-header {\n  padding: 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.results-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);\n}\n\n.results-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n/* 删除重复的样式定义 */\n\n/* 插件网格区域 */\n.plugins-grid-wrapper {\n  padding: 2rem;\n  min-height: 400px;\n}\n\n/* 分页区域 */\n.pagination-wrapper {\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f5f9;\n  background: #fafbfc;\n  text-align: center;\n}\n\n/* 插件展示区域 */\n.plugins-showcase {\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n  padding: 2rem 0;\n  min-height: 600px;\n}\n\n/* 结果头部 */\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 1.5rem 2rem;\n  background: white;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n  margin-bottom: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.results-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.75rem 0;\n}\n\n.active-filters {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.filter-tag {\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.header-right {\n  flex-shrink: 0;\n  text-align: right;\n}\n\n.results-count {\n  font-size: 0.9rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.count-number {\n  font-size: 1.1rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugins-grid-wrapper {\n  margin-bottom: 2rem;\n}\n\n/* 现代化分页 */\n.modern-pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 2rem;\n  background: white;\n  border-radius: 16px;\n  border: 1px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.modern-pagination::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #667eea, #764ba2);\n}\n\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.custom-pagination .ant-pagination-item {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  margin: 0 3px;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-item:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n.custom-pagination .ant-pagination-item-active {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  border-color: transparent;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.custom-pagination .ant-pagination-item-active a {\n  color: white;\n  font-weight: 600;\n}\n\n.custom-pagination .ant-pagination-prev,\n.custom-pagination .ant-pagination-next {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-prev:hover,\n.custom-pagination .ant-pagination-next:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 1400px) {\n  .content-layout {\n    grid-template-columns: 300px 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .content-layout {\n    grid-template-columns: 280px 1fr;\n    gap: 1.5rem;\n  }\n\n  .filter-section {\n    padding: 1rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .main-content {\n    padding: 1rem 0;\n  }\n\n  .content-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .sidebar {\n    position: static;\n    order: 2;\n  }\n\n  .main-area {\n    order: 1;\n  }\n\n  .results-header {\n    flex-direction: column;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .header-right {\n    text-align: left;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1rem;\n  }\n\n  .pagination-wrapper {\n    padding: 1rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .filter-section {\n    padding: 0.75rem;\n  }\n\n  .results-header {\n    padding: 0.75rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 0.75rem;\n  }\n}\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.05);\n}\n\n.plugin-badge {\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plugin-info {\n  padding: 1.5rem;\n}\n\n.plugin-name {\n  font-size: 1.3rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.plugin-description {\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  line-height: 1.6;\n}\n\n.plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.plugin-price {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugin-rating {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  color: #fbbf24;\n  font-weight: 600;\n}\n\n.btn-plugin-buy {\n  width: 100%;\n  padding: 0.875rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  color: white;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-plugin-buy:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .header-stats {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .plugins-section {\n    padding: 1rem 0;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  /* 🔥 搜索区域响应式 */\n  .search-section {\n    padding: 2rem 0;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n    gap: 1rem;\n    max-width: 100%;\n    padding: 0 1rem;\n  }\n\n  .custom-search-input {\n    height: 50px;\n  }\n\n  .search-input-native {\n    font-size: 1rem;\n    padding: 0 2.5rem 0 3rem; /* 🔥 移动端调整padding */\n  }\n\n  .clear-search-icon {\n    right: 1rem; /* 🔥 移动端调整位置 */\n    font-size: 0.9rem;\n  }\n\n  .clear-filters-btn.ant-btn {\n    height: 50px !important;\n    width: 100%;\n    min-width: auto;\n    line-height: 50px !important;\n  }\n}\n\n/* 🔥 自定义组合插件弹窗样式 */\n.combined-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.combined-modal-content {\n  background: white;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 1200px;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.combined-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32px;\n  border-bottom: none;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.combined-modal-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);\n  backdrop-filter: blur(10px);\n}\n\n.header-content1 {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 2;\n}\n\n.header-icon {\n  font-size: 2.5rem;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 12px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.header-text h2 {\n  margin: 0 0 4px 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: white !important;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n}\n\n.header-subtitle {\n  margin: 0;\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.9) !important;\n  font-weight: 500;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-close {\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white !important;\n  font-size: 1.8rem;\n  cursor: pointer;\n  padding: 0;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 2;\n  backdrop-filter: blur(10px);\n  font-weight: 300;\n}\n\n.combined-modal-close:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: rotate(90deg) scale(1.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-body {\n  padding: 24px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.loading-state,\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #64748b;\n}\n\n/* 🔥 子插件网格布局 */\n.sub-plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 24px;\n  padding: 8px;\n}\n\n/* 🔥 子插件卡片样式 - 与一级插件保持一致 */\n.sub-plugin-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.sub-plugin-card:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  border-color: #3b82f6;\n}\n\n/* 🔥 子插件图片区域 */\n.sub-plugin-image {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n}\n\n.sub-plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.4s ease;\n}\n\n.sub-plugin-card:hover .sub-plugin-image img {\n  transform: scale(1.1);\n}\n\n/* 🔥 子插件分类标签 */\n.sub-plugin-category {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(8px);\n  color: #1e293b;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  z-index: 2;\n}\n\n/* 🔥 子插件状态标签 */\n.sub-plugin-status {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n  z-index: 2;\n}\n\n/* 🔥 子插件内容区域 */\n.sub-plugin-content {\n  padding: 20px;\n}\n\n.sub-plugin-header {\n  margin-bottom: 12px;\n}\n\n.sub-plugin-title {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 8px 0;\n  line-height: 1.3;\n}\n\n.sub-plugin-author {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.author-icon {\n  font-size: 1rem;\n}\n\n.sub-plugin-description {\n  color: #64748b;\n  font-size: 0.9rem;\n  line-height: 1.6;\n  margin: 0 0 20px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* 🔥 子插件底部区域 */\n.sub-plugin-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n\n.sub-plugin-price {\n  display: flex;\n  align-items: baseline;\n  gap: 0.25rem;\n}\n\n.price-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.price-unit {\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n/* 🔥 子插件按钮 */\n.sub-plugin-btn {\n  background: linear-gradient(135deg, #3b82f6, #2563eb);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.sub-plugin-btn:hover {\n  background: linear-gradient(135deg, #2563eb, #1d4ed8);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n}\n\n.btn-icon {\n  font-size: 1rem;\n}\n\n/* 🔥 响应式设计 */\n@media (max-width: 768px) {\n  .combined-modal-content {\n    width: 95%;\n    margin: 20px;\n  }\n\n  .sub-plugins-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .combined-modal-header {\n    padding: 16px;\n  }\n\n  .combined-modal-header h2 {\n    font-size: 1.2rem;\n  }\n\n  .combined-modal-body {\n    padding: 16px;\n  }\n\n  .sub-plugin-card {\n    border-radius: 16px;\n  }\n\n  .sub-plugin-image {\n    height: 160px;\n  }\n\n  .sub-plugin-content {\n    padding: 16px;\n  }\n\n  .sub-plugin-title {\n    font-size: 1.1rem;\n  }\n\n  .sub-plugin-footer {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .sub-plugin-btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/market"}]}