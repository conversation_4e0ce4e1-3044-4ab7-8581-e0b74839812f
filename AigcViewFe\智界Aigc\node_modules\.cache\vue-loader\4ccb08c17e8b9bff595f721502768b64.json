{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue?vue&type=template&id=17697b29&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue", "mtime": 1753951994089}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-card\",\n    { attrs: { bordered: false } },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"table-page-search-wrapper\" },\n        [\n          _c(\n            \"a-form\",\n            {\n              attrs: { layout: \"inline\" },\n              nativeOn: {\n                keyup: function($event) {\n                  if (\n                    !$event.type.indexOf(\"key\") &&\n                    _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                  ) {\n                    return null\n                  }\n                  return _vm.searchQuery($event)\n                }\n              }\n            },\n            [\n              _c(\n                \"a-row\",\n                { attrs: { gutter: 24 } },\n                [\n                  _c(\n                    \"a-col\",\n                    { attrs: { xl: 6, lg: 7, md: 8, sm: 24 } },\n                    [\n                      _c(\n                        \"a-form-item\",\n                        { attrs: { label: \"作者类型\" } },\n                        [\n                          _c(\"j-dict-select-tag\", {\n                            attrs: {\n                              placeholder: \"请选择作者类型\",\n                              dictCode: \"author_type\"\n                            },\n                            model: {\n                              value: _vm.queryParam.authorType,\n                              callback: function($$v) {\n                                _vm.$set(_vm.queryParam, \"authorType\", $$v)\n                              },\n                              expression: \"queryParam.authorType\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { xl: 6, lg: 7, md: 8, sm: 24 } },\n                    [\n                      _c(\n                        \"a-form-item\",\n                        { attrs: { label: \"智能体名称\" } },\n                        [\n                          _c(\"a-input\", {\n                            attrs: { placeholder: \"请输入智能体名称\" },\n                            model: {\n                              value: _vm.queryParam.agentName,\n                              callback: function($$v) {\n                                _vm.$set(_vm.queryParam, \"agentName\", $$v)\n                              },\n                              expression: \"queryParam.agentName\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _vm.toggleSearchStatus\n                    ? [\n                        _c(\n                          \"a-col\",\n                          { attrs: { xl: 10, lg: 11, md: 12, sm: 24 } },\n                          [\n                            _c(\n                              \"a-form-item\",\n                              { attrs: { label: \"价格（元）\" } },\n                              [\n                                _c(\"a-input\", {\n                                  staticClass: \"query-group-cust\",\n                                  attrs: { placeholder: \"请输入最小值\" },\n                                  model: {\n                                    value: _vm.queryParam.price_begin,\n                                    callback: function($$v) {\n                                      _vm.$set(\n                                        _vm.queryParam,\n                                        \"price_begin\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"queryParam.price_begin\"\n                                  }\n                                }),\n                                _c(\"span\", {\n                                  staticClass: \"query-group-split-cust\"\n                                }),\n                                _c(\"a-input\", {\n                                  staticClass: \"query-group-cust\",\n                                  attrs: { placeholder: \"请输入最大值\" },\n                                  model: {\n                                    value: _vm.queryParam.price_end,\n                                    callback: function($$v) {\n                                      _vm.$set(_vm.queryParam, \"price_end\", $$v)\n                                    },\n                                    expression: \"queryParam.price_end\"\n                                  }\n                                })\n                              ],\n                              1\n                            )\n                          ],\n                          1\n                        ),\n                        _c(\n                          \"a-col\",\n                          { attrs: { xl: 6, lg: 7, md: 8, sm: 24 } },\n                          [\n                            _c(\n                              \"a-form-item\",\n                              { attrs: { label: \"审核状态\" } },\n                              [\n                                _c(\"j-dict-select-tag\", {\n                                  attrs: {\n                                    placeholder: \"请选择审核状态\",\n                                    dictCode: \"audit_status\"\n                                  },\n                                  model: {\n                                    value: _vm.queryParam.auditStatus,\n                                    callback: function($$v) {\n                                      _vm.$set(\n                                        _vm.queryParam,\n                                        \"auditStatus\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"queryParam.auditStatus\"\n                                  }\n                                })\n                              ],\n                              1\n                            )\n                          ],\n                          1\n                        )\n                      ]\n                    : _vm._e(),\n                  _c(\"a-col\", { attrs: { xl: 6, lg: 7, md: 8, sm: 24 } }, [\n                    _c(\n                      \"span\",\n                      {\n                        staticClass: \"table-page-search-submitButtons\",\n                        staticStyle: { float: \"left\", overflow: \"hidden\" }\n                      },\n                      [\n                        _c(\n                          \"a-button\",\n                          {\n                            attrs: { type: \"primary\", icon: \"search\" },\n                            on: { click: _vm.searchQuery }\n                          },\n                          [_vm._v(\"查询\")]\n                        ),\n                        _c(\n                          \"a-button\",\n                          {\n                            staticStyle: { \"margin-left\": \"8px\" },\n                            attrs: { type: \"primary\", icon: \"reload\" },\n                            on: { click: _vm.searchReset }\n                          },\n                          [_vm._v(\"重置\")]\n                        ),\n                        _c(\n                          \"a\",\n                          {\n                            staticStyle: { \"margin-left\": \"8px\" },\n                            on: { click: _vm.handleToggleSearch }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(\n                                  _vm.toggleSearchStatus ? \"收起\" : \"展开\"\n                                ) +\n                                \"\\n              \"\n                            ),\n                            _c(\"a-icon\", {\n                              attrs: {\n                                type: _vm.toggleSearchStatus ? \"up\" : \"down\"\n                              }\n                            })\n                          ],\n                          1\n                        )\n                      ],\n                      1\n                    )\n                  ])\n                ],\n                2\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"table-operator\" },\n        [\n          _c(\n            \"a-button\",\n            {\n              attrs: { type: \"primary\", icon: \"plus\" },\n              on: { click: _vm.handleAdd }\n            },\n            [_vm._v(\"新增\")]\n          ),\n          _c(\n            \"a-button\",\n            {\n              attrs: { type: \"primary\", icon: \"download\" },\n              on: {\n                click: function($event) {\n                  return _vm.handleExportXls(\"智能体表\")\n                }\n              }\n            },\n            [_vm._v(\"导出\")]\n          ),\n          _c(\n            \"a-upload\",\n            {\n              attrs: {\n                name: \"file\",\n                showUploadList: false,\n                multiple: false,\n                headers: _vm.tokenHeader,\n                action: _vm.importExcelUrl\n              },\n              on: { change: _vm.handleImportExcel }\n            },\n            [\n              _c(\"a-button\", { attrs: { type: \"primary\", icon: \"import\" } }, [\n                _vm._v(\"导入\")\n              ])\n            ],\n            1\n          ),\n          _c(\"j-super-query\", {\n            ref: \"superQueryModal\",\n            attrs: { fieldList: _vm.superFieldList },\n            on: { handleSuperQuery: _vm.handleSuperQuery }\n          }),\n          _vm.selectedRowKeys.length > 0\n            ? _c(\n                \"a-dropdown\",\n                [\n                  _c(\n                    \"a-menu\",\n                    { attrs: { slot: \"overlay\" }, slot: \"overlay\" },\n                    [\n                      _c(\n                        \"a-menu-item\",\n                        { key: \"1\", on: { click: _vm.batchDel } },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"delete\" } }),\n                          _vm._v(\"删除\")\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-button\",\n                    { staticStyle: { \"margin-left\": \"8px\" } },\n                    [\n                      _vm._v(\" 批量操作 \"),\n                      _c(\"a-icon\", { attrs: { type: \"down\" } })\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            : _vm._e()\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"ant-alert ant-alert-info\",\n              staticStyle: { \"margin-bottom\": \"16px\" }\n            },\n            [\n              _c(\"i\", {\n                staticClass: \"anticon anticon-info-circle ant-alert-icon\"\n              }),\n              _vm._v(\" 已选择 \"),\n              _c(\"a\", { staticStyle: { \"font-weight\": \"600\" } }, [\n                _vm._v(_vm._s(_vm.selectedRowKeys.length))\n              ]),\n              _vm._v(\"项\\n      \"),\n              _c(\n                \"a\",\n                {\n                  staticStyle: { \"margin-left\": \"24px\" },\n                  on: { click: _vm.onClearSelected }\n                },\n                [_vm._v(\"清空\")]\n              )\n            ]\n          ),\n          _c(\"a-table\", {\n            ref: \"table\",\n            staticClass: \"j-table-force-nowrap\",\n            attrs: {\n              size: \"middle\",\n              bordered: \"\",\n              rowKey: \"id\",\n              scroll: { x: true },\n              columns: _vm.columns,\n              dataSource: _vm.dataSource,\n              pagination: _vm.ipagination,\n              loading: _vm.loading,\n              rowSelection: {\n                selectedRowKeys: _vm.selectedRowKeys,\n                onChange: _vm.onSelectChange\n              }\n            },\n            on: { change: _vm.handleTableChange },\n            scopedSlots: _vm._u([\n              {\n                key: \"htmlSlot\",\n                fn: function(text) {\n                  return [_c(\"div\", { domProps: { innerHTML: _vm._s(text) } })]\n                }\n              },\n              {\n                key: \"imgSlot\",\n                fn: function(text) {\n                  return [\n                    !text\n                      ? _c(\n                          \"span\",\n                          {\n                            staticStyle: {\n                              \"font-size\": \"12px\",\n                              \"font-style\": \"italic\"\n                            }\n                          },\n                          [_vm._v(\"无图片\")]\n                        )\n                      : _c(\"img\", {\n                          staticStyle: {\n                            \"max-width\": \"80px\",\n                            \"font-size\": \"12px\",\n                            \"font-style\": \"italic\"\n                          },\n                          attrs: {\n                            src: _vm.getImgView(text),\n                            height: \"25px\",\n                            alt: \"\"\n                          }\n                        })\n                  ]\n                }\n              },\n              {\n                key: \"fileSlot\",\n                fn: function(text) {\n                  return [\n                    !text\n                      ? _c(\n                          \"span\",\n                          {\n                            staticStyle: {\n                              \"font-size\": \"12px\",\n                              \"font-style\": \"italic\"\n                            }\n                          },\n                          [_vm._v(\"无文件\")]\n                        )\n                      : _c(\n                          \"a-button\",\n                          {\n                            attrs: {\n                              ghost: true,\n                              type: \"primary\",\n                              icon: \"download\",\n                              size: \"small\"\n                            },\n                            on: {\n                              click: function($event) {\n                                return _vm.downloadFile(text)\n                              }\n                            }\n                          },\n                          [_vm._v(\"\\n          下载\\n        \")]\n                        )\n                  ]\n                }\n              },\n              {\n                key: \"action\",\n                fn: function(text, record) {\n                  return _c(\n                    \"span\",\n                    {},\n                    [\n                      _c(\n                        \"a\",\n                        {\n                          on: {\n                            click: function($event) {\n                              return _vm.handleEdit(record)\n                            }\n                          }\n                        },\n                        [_vm._v(\"编辑\")]\n                      ),\n                      _c(\"a-divider\", { attrs: { type: \"vertical\" } }),\n                      _c(\n                        \"a-dropdown\",\n                        [\n                          _c(\n                            \"a\",\n                            { staticClass: \"ant-dropdown-link\" },\n                            [\n                              _vm._v(\"更多 \"),\n                              _c(\"a-icon\", { attrs: { type: \"down\" } })\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"a-menu\",\n                            { attrs: { slot: \"overlay\" }, slot: \"overlay\" },\n                            [\n                              _c(\"a-menu-item\", [\n                                _c(\n                                  \"a\",\n                                  {\n                                    on: {\n                                      click: function($event) {\n                                        return _vm.handleDetail(record)\n                                      }\n                                    }\n                                  },\n                                  [_vm._v(\"详情\")]\n                                )\n                              ]),\n                              _c(\n                                \"a-menu-item\",\n                                [\n                                  _c(\n                                    \"a-popconfirm\",\n                                    {\n                                      attrs: { title: \"确定删除吗?\" },\n                                      on: {\n                                        confirm: function() {\n                                          return _vm.handleDelete(record.id)\n                                        }\n                                      }\n                                    },\n                                    [_c(\"a\", [_vm._v(\"删除\")])]\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                }\n              }\n            ])\n          })\n        ],\n        1\n      ),\n      _c(\"aigc-agent-modal\", { ref: \"modalForm\", on: { ok: _vm.modalFormOk } })\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}