{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue?vue&type=template&id=17697b29&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue", "mtime": 1753951994089}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"作者类型\">\n            <j-dict-select-tag placeholder=\"请选择作者类型\" v-model=\"queryParam.authorType\" dictCode=\"author_type\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"智能体名称\">\n            <a-input placeholder=\"请输入智能体名称\" v-model=\"queryParam.agentName\"></a-input>\n          </a-form-item>\n        </a-col>\n        <template v-if=\"toggleSearchStatus\">\n          <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n            <a-form-item label=\"价格（元）\">\n              <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.price_begin\"></a-input>\n              <span class=\"query-group-split-cust\"></span>\n              <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.price_end\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"审核状态\">\n              <j-dict-select-tag placeholder=\"请选择审核状态\" v-model=\"queryParam.auditStatus\" dictCode=\"audit_status\"/>\n            </a-form-item>\n          </a-col>\n        </template>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n            <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n            <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n              {{ toggleSearchStatus ? '收起' : '展开' }}\n              <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n            </a>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n  <!-- 查询区域-END -->\n  \n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\">\n    <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n    <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('智能体表')\">导出</a-button>\n    <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\" @change=\"handleImportExcel\">\n      <a-button type=\"primary\" icon=\"import\">导入</a-button>\n    </a-upload>\n    <!-- 高级查询区域 -->\n    <j-super-query :fieldList=\"superFieldList\" ref=\"superQueryModal\" @handleSuperQuery=\"handleSuperQuery\"></j-super-query>\n    <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n      <a-menu slot=\"overlay\">\n        <a-menu-item key=\"1\" @click=\"batchDel\"><a-icon type=\"delete\"/>删除</a-menu-item>\n      </a-menu>\n      <a-button style=\"margin-left: 8px\"> 批量操作 <a-icon type=\"down\" /></a-button>\n    </a-dropdown>\n  </div>\n\n  <!-- table区域-begin -->\n  <div>\n    <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n      <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n      <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n    </div>\n\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      bordered\n      rowKey=\"id\"\n      class=\"j-table-force-nowrap\"\n      :scroll=\"{x:true}\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      @change=\"handleTableChange\">\n\n      <template slot=\"htmlSlot\" slot-scope=\"text\">\n        <div v-html=\"text\"></div>\n      </template>\n      <template slot=\"imgSlot\" slot-scope=\"text\">\n        <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无图片</span>\n        <img v-else :src=\"getImgView(text)\" height=\"25px\" alt=\"\" style=\"max-width:80px;font-size: 12px;font-style: italic;\"/>\n      </template>\n      <template slot=\"fileSlot\" slot-scope=\"text\">\n        <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无文件</span>\n        <a-button\n          v-else\n          :ghost=\"true\"\n          type=\"primary\"\n          icon=\"download\"\n          size=\"small\"\n          @click=\"downloadFile(text)\">\n          下载\n        </a-button>\n      </template>\n\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n\n        <a-divider type=\"vertical\" />\n        <a-dropdown>\n          <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n          <a-menu slot=\"overlay\">\n            <a-menu-item>\n              <a @click=\"handleDetail(record)\">详情</a>\n            </a-menu-item>\n            <a-menu-item>\n              <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                <a>删除</a>\n              </a-popconfirm>\n            </a-menu-item>\n          </a-menu>\n        </a-dropdown>\n      </span>\n\n    </a-table>\n  </div>\n\n  <aigc-agent-modal ref=\"modalForm\" @ok=\"modalFormOk\"/>\n</a-card>\n", null]}