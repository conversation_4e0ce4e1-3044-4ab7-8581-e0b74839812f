package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;
import org.jeecg.modules.api.vo.AgentMarketVO;
import org.jeecg.modules.jianying.service.TosService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.entity.SysUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 智能体市场API
 * @Author: AigcView
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@Api(tags = "智能体市场API")
@RestController
@RequestMapping("/api/agent/market")
@Slf4j
public class AgentMarketController {

    @Autowired
    private IAigcAgentService aigcAgentService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private TosService tosService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IAigcWorkflowService aigcWorkflowService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取智能体市场列表
     * 注意：此接口为公开接口，无需登录即可访问，但不返回敏感信息
     */
    @AutoLog(value = "智能体市场-分页列表查询")
    @ApiOperation(value = "智能体市场-分页列表查询", notes = "获取审核通过的智能体列表，支持搜索和筛选，公开接口无需认证")
    @GetMapping(value = "/list")
    public Result<?> getAgentMarketList(
            @ApiParam("页码") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam("页大小") @RequestParam(name = "pageSize", defaultValue = "12") Integer pageSize,
            @ApiParam("智能体名称") @RequestParam(name = "agentName", required = false) String agentName,
            @ApiParam("作者类型") @RequestParam(name = "authorType", required = false) String authorType,
            HttpServletRequest request) {

        try {
            // 构建查询条件
            QueryWrapper<AigcAgent> queryWrapper = new QueryWrapper<>();
            
            // 只查询审核通过的智能体
            queryWrapper.eq("audit_status", "2");
            
            // 按名称搜索
            if (StringUtils.hasText(agentName)) {
                queryWrapper.like("agent_name", agentName);
            }
            
            // 按作者类型筛选
            if (StringUtils.hasText(authorType)) {
                queryWrapper.eq("author_type", authorType);
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 分页查询
            Page<AigcAgent> page = new Page<>(pageNo, pageSize);
            IPage<AigcAgent> pageList = aigcAgentService.page(page, queryWrapper);

            // 获取用户角色用于价格计算
            String userRole = getUserRole(request);
            
            // 转换为VO对象并处理返回数据
            List<AigcAgent> records = pageList.getRecords();
            List<AgentMarketVO> voList = new ArrayList<>();

            for (AigcAgent agent : records) {
                AgentMarketVO vo = new AgentMarketVO();
                BeanUtils.copyProperties(agent, vo);

                // 处理智能体头像和视频的CDN URL
                if (StringUtils.hasText(agent.getAgentAvatar())) {
                    vo.setAgentAvatar(tosService.generateFileUrl(agent.getAgentAvatar()));
                }
                if (StringUtils.hasText(agent.getDemoVideo())) {
                    vo.setDemoVideo(tosService.generateFileUrl(agent.getDemoVideo()));
                }

                // 设置原价格
                vo.setOriginalPrice(agent.getPrice());

                // 获取创作者信息
                enrichAgentWithCreatorInfo(vo, agent.getCreateBy());

                // 获取工作流数量
                enrichAgentWithWorkflowCount(vo, agent.getId());

                // 计算价格折扣
                enrichAgentWithPriceInfo(vo, userRole, agent.getAuthorType());

                // 设置统计信息（暂时使用默认值）
                vo.setStatistics(0L, 0L, 0L, 0L);

                voList.add(vo);
            }

            // 构建返回的分页对象
            Page<AgentMarketVO> voPage = new Page<>(pageNo, pageSize);
            voPage.setRecords(voList);
            voPage.setTotal(pageList.getTotal());
            voPage.setSize(pageList.getSize());
            voPage.setCurrent(pageList.getCurrent());
            voPage.setPages(pageList.getPages());

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取智能体市场列表失败", e);
            return Result.error("获取智能体市场列表失败");
        }
    }

    /**
     * 获取智能体详情
     */
    @AutoLog(value = "智能体详情-查询")
    @ApiOperation(value = "智能体详情-查询", notes = "获取智能体详细信息，包括基本信息和价格计算")
    @GetMapping(value = "/detail/{agentId}")
    public Result<?> getAgentDetail(@PathVariable String agentId, HttpServletRequest request) {
        try {
            log.info("🔍 获取智能体详情 - ID: {}", agentId);

            // 查询智能体基本信息
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 检查审核状态
            if (!"2".equals(agent.getAuditStatus())) {
                return Result.error("智能体未通过审核");
            }

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("id", agent.getId());
            result.put("agentName", agent.getAgentName());
            result.put("agentDescription", agent.getAgentDescription());
            result.put("agentAvatar", agent.getAgentAvatar());
            result.put("price", agent.getPrice());
            result.put("authorType", agent.getAuthorType());
            result.put("demoVideo", agent.getDemoVideo());
            result.put("experienceLink", agent.getExperienceLink());

            // 获取真实的创作者信息
            Map<String, Object> creatorInfo = getCreatorInfo(agent.getCreateBy(), agent.getAuthorType());
            result.put("creatorInfo", creatorInfo);

            // 获取用户角色并计算价格信息
            String userRole = getUserRole(request);
            Map<String, Object> priceInfo = calculatePriceInfo(agent, userRole);
            result.putAll(priceInfo);

            log.info("✅ 智能体详情查询成功 - 名称: {}", agent.getAgentName());

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 获取智能体详情失败 - ID: {}", agentId, e);
            return Result.error("获取智能体详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取智能体的工作流列表
     */
    @AutoLog(value = "智能体工作流-列表查询")
    @ApiOperation(value = "智能体工作流-列表查询", notes = "获取指定智能体下的所有工作流")
    @GetMapping(value = "/{agentId}/workflows")
    public Result<?> getAgentWorkflows(@PathVariable String agentId) {
        try {
            log.info("🔍 获取智能体工作流列表 - 智能体ID: {}", agentId);

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 检查审核状态
            if (!"2".equals(agent.getAuditStatus())) {
                return Result.error("智能体未通过审核");
            }

            // 查询真实的工作流数据
            List<Map<String, Object>> workflows = new ArrayList<>();

            try {
                // 从数据库查询该智能体的工作流
                List<AigcWorkflow> workflowList = aigcWorkflowService.selectByMainId(agentId);

                if (workflowList != null && !workflowList.isEmpty()) {
                    // 有真实工作流数据时，转换为前端需要的格式
                    for (AigcWorkflow workflow : workflowList) {
                        Map<String, Object> workflowData = new HashMap<>();
                        workflowData.put("id", workflow.getWorkflowId());
                        workflowData.put("name", workflow.getWorkflowName());
                        workflowData.put("description", workflow.getWorkflowDescription());
                        workflowData.put("avatar", agent.getAgentAvatar()); // 使用智能体头像
                        workflowData.put("type", 1); // 默认类型
                        workflowData.put("packagePath", workflow.getWorkflowPackage());
                        workflowData.put("createTime", workflow.getCreateTime());
                        workflows.add(workflowData);
                    }
                    log.info("✅ 查询到真实工作流数据 - 数量: {}", workflowList.size());
                } else {
                    // 没有真实工作流数据时，根据智能体类型生成示例工作流
                    log.info("⚠️ 未找到真实工作流数据，生成示例数据");

                    if ("1".equals(agent.getAuthorType())) {
                        // 官方智能体 - 更多工作流
                        workflows.add(createWorkflowData("demo-1", agent.getAgentName() + " - 基础对话流程",
                            "适用于日常对话和基础问答的工作流程", agent.getAgentAvatar(), 1));
                        workflows.add(createWorkflowData("demo-2", agent.getAgentName() + " - 高级分析流程",
                            "用于复杂数据分析和深度思考的高级工作流", agent.getAgentAvatar(), 2));
                        workflows.add(createWorkflowData("demo-3", agent.getAgentName() + " - 创意生成流程",
                            "专门用于创意内容生成和头脑风暴的工作流", agent.getAgentAvatar(), 3));
                    } else {
                        // 创作者智能体 - 较少工作流
                        workflows.add(createWorkflowData("demo-1", agent.getAgentName() + " - 核心功能流程",
                            "智能体的核心功能和主要应用场景", agent.getAgentAvatar(), 1));
                        workflows.add(createWorkflowData("demo-2", agent.getAgentName() + " - 扩展应用流程",
                            "扩展功能和特殊应用场景的工作流程", agent.getAgentAvatar(), 2));
                    }
                }
            } catch (Exception e) {
                log.error("❌ 查询工作流数据失败，使用示例数据: {}", e.getMessage());

                // 查询失败时生成示例数据
                if ("1".equals(agent.getAuthorType())) {
                    workflows.add(createWorkflowData("fallback-1", agent.getAgentName() + " - 基础功能",
                        "智能体的基础功能演示", agent.getAgentAvatar(), 1));
                } else {
                    workflows.add(createWorkflowData("fallback-1", agent.getAgentName() + " - 核心功能",
                        "智能体的核心功能演示", agent.getAgentAvatar(), 1));
                }
            }

            log.info("✅ 智能体工作流列表查询成功 - 数量: {}", workflows.size());

            return Result.OK(workflows);

        } catch (Exception e) {
            log.error("❌ 获取智能体工作流列表失败 - 智能体ID: {}", agentId, e);
            return Result.error("获取工作流列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户已购买的智能体列表
     */
    @AutoLog(value = "用户购买记录-查询")
    @ApiOperation(value = "用户购买记录-查询", notes = "获取当前用户已购买的智能体ID列表")
    @GetMapping(value = "/purchase/list")
    public Result<?> getUserPurchasedAgents(HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 获取用户已购买智能体列表 - 用户: {}", username);

            // TODO: 查询用户购买记录表
            // 目前返回空列表
            List<String> purchasedAgentIds = new ArrayList<>();

            // 模拟数据 - 可以根据需要添加一些测试数据
            // purchasedAgentIds.add("test-agent-id-1");

            log.info("✅ 用户已购买智能体列表查询成功 - 数量: {}", purchasedAgentIds.size());

            return Result.OK(purchasedAgentIds);

        } catch (Exception e) {
            log.error("❌ 获取用户已购买智能体列表失败", e);
            return Result.error("获取购买记录失败: " + e.getMessage());
        }
    }

    /**
     * 检查智能体购买状态
     */
    @AutoLog(value = "智能体购买状态-检查")
    @ApiOperation(value = "智能体购买状态-检查", notes = "检查用户是否已购买指定智能体")
    @GetMapping(value = "/purchase/check/{agentId}")
    public Result<?> checkAgentPurchaseStatus(@PathVariable String agentId, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                // 未登录用户返回未购买状态
                Map<String, Object> result = new HashMap<>();
                result.put("isPurchased", false);
                result.put("purchaseTime", null);
                result.put("purchasePrice", null);
                return Result.OK(result);
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 检查智能体购买状态 - 用户: {}, 智能体ID: {}", username, agentId);

            // TODO: 查询用户购买记录
            // 目前返回未购买状态
            Map<String, Object> result = new HashMap<>();
            result.put("isPurchased", false);
            result.put("purchaseTime", null);
            result.put("purchasePrice", null);

            log.info("✅ 智能体购买状态检查完成 - 结果: 未购买");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 检查智能体购买状态失败 - 智能体ID: {}", agentId, e);
            return Result.error("检查购买状态失败: " + e.getMessage());
        }
    }

    /**
     * 购买智能体
     */
    @AutoLog(value = "智能体购买-创建订单")
    @ApiOperation(value = "智能体购买-创建订单", notes = "用户购买智能体，创建购买记录和交易订单")
    @PostMapping(value = "/purchase")
    public Result<?> purchaseAgent(@RequestBody Map<String, Object> purchaseData, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            String agentId = (String) purchaseData.get("agentId");
            String agentName = (String) purchaseData.get("agentName");
            BigDecimal purchasePrice = new BigDecimal(purchaseData.get("purchasePrice").toString());
            BigDecimal originalPrice = new BigDecimal(purchaseData.get("originalPrice").toString());
            Integer discountRate = (Integer) purchaseData.get("discountRate");

            log.info("🛒 用户购买智能体 - 用户: {}, 智能体: {}, 价格: {}", username, agentName, purchasePrice);

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 检查是否已购买
            // TODO: 查询购买记录表
            // 目前跳过重复购买检查

            // TODO: 检查用户余额
            // 目前跳过余额检查

            // TODO: 创建购买记录
            // 插入到 aicg_user_agent_purchase 表

            // TODO: 创建交易记录
            // 插入到 aicg_user_transaction 表

            // 模拟购买过程
            Thread.sleep(1000);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "购买成功");
            result.put("agentId", agentId);
            result.put("purchaseTime", System.currentTimeMillis());

            log.info("✅ 智能体购买成功 - 用户: {}, 智能体: {}", username, agentName);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 智能体购买失败", e);
            return Result.error("购买失败: " + e.getMessage());
        }
    }

    /**
     * 工作流下载权限验证
     */
    @AutoLog(value = "工作流下载-权限验证")
    @ApiOperation(value = "工作流下载-权限验证", notes = "验证用户是否有权限下载指定工作流")
    @GetMapping(value = "/workflow/download/check")
    public Result<?> checkWorkflowDownloadPermission(@RequestParam String workflowId,
                                                     @RequestParam String agentId,
                                                     HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 检查工作流下载权限 - 用户: {}, 工作流: {}, 智能体: {}", username, workflowId, agentId);

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // TODO: 检查用户是否已购买该智能体
            // 目前返回有权限
            boolean hasPermission = true;

            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            result.put("workflowId", workflowId);
            result.put("agentId", agentId);

            if (hasPermission) {
                // TODO: 生成下载链接或返回下载信息
                result.put("downloadUrl", "/api/workflow/download/" + workflowId);
                result.put("message", "有权限下载");
            } else {
                result.put("message", "请先购买该智能体");
            }

            log.info("✅ 工作流下载权限检查完成 - 结果: {}", hasPermission ? "有权限" : "无权限");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 检查工作流下载权限失败", e);
            return Result.error("权限检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取创作者信息
     * @param createBy 创建者用户ID
     * @param authorType 作者类型：1-官方，2-创作者
     * @return 创作者信息Map
     */
    private Map<String, Object> getCreatorInfo(String createBy, String authorType) {
        Map<String, Object> creatorInfo = new HashMap<>();

        try {
            if ("1".equals(authorType)) {
                // 官方智能体
                creatorInfo.put("name", "官方团队");
                creatorInfo.put("nickname", "智界Aigc");
                creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
                log.debug("官方智能体创作者信息设置完成");
            } else {
                // 创作者智能体 - 查询真实用户信息
                if (StringUtils.hasText(createBy)) {
                    // 查询用户基本信息
                    SysUser user = sysUserService.getById(createBy);
                    if (user != null) {
                        String creatorName = "创作者";
                        String creatorAvatar = tosService.getDefaultAvatarUrl();

                        // 查询用户扩展信息
                        AicgUserProfile userProfile = userProfileService.getOne(
                            new LambdaQueryWrapper<AicgUserProfile>()
                                .eq(AicgUserProfile::getUserId, createBy)
                        );

                        if (userProfile != null && StringUtils.hasText(userProfile.getNickname())) {
                            // 优先使用扩展表中的昵称
                            creatorName = userProfile.getNickname();

                            // 处理头像URL
                            if (StringUtils.hasText(userProfile.getAvatar())) {
                                // 有头像时使用TOS服务生成正确的头像URL
                                creatorAvatar = tosService.generateFileUrl(userProfile.getAvatar());
                                log.debug("获取创作者头像成功: userId={}, avatarPath={}, avatarUrl={}",
                                    user.getId(), userProfile.getAvatar(), creatorAvatar);
                            } else {
                                // 没有头像时使用TOS默认头像
                                creatorAvatar = tosService.getDefaultAvatarUrl();
                                log.debug("使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                            }
                        } else {
                            // 扩展表没有昵称时，优先使用真实姓名，否则使用用户名
                            creatorName = StringUtils.hasText(user.getRealname()) ? user.getRealname() : user.getUsername();
                            // 没有扩展信息时使用TOS默认头像
                            creatorAvatar = tosService.getDefaultAvatarUrl();
                            log.debug("没有扩展信息，使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                        }

                        creatorInfo.put("name", creatorName);
                        creatorInfo.put("nickname", creatorName);
                        creatorInfo.put("avatar", creatorAvatar);

                        log.debug("创作者信息设置完成: createBy={}, creatorName={}, creatorAvatar={}",
                            createBy, creatorName, creatorAvatar);
                    } else {
                        // 用户不存在时使用默认信息
                        creatorInfo.put("name", "未知创作者");
                        creatorInfo.put("nickname", "未知创作者");
                        creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
                        log.warn("创作者用户不存在: createBy={}", createBy);
                    }
                } else {
                    // createBy为空时使用默认信息
                    creatorInfo.put("name", "匿名创作者");
                    creatorInfo.put("nickname", "匿名创作者");
                    creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
                    log.warn("创作者ID为空");
                }
            }
        } catch (Exception e) {
            log.error("获取创作者信息失败: createBy={}, authorType={}", createBy, authorType, e);
            // 异常时返回默认信息
            creatorInfo.put("name", "未知创作者");
            creatorInfo.put("nickname", "未知创作者");
            creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
        }

        return creatorInfo;
    }

    /**
     * 获取智能体市场统计信息
     */
    @AutoLog(value = "智能体市场-统计信息")
    @ApiOperation(value = "智能体市场-统计信息", notes = "获取智能体总数、创作者数量等统计信息")
    @GetMapping(value = "/stats")
    public Result<?> getMarketStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 统计审核通过的智能体总数
            QueryWrapper<AigcAgent> totalWrapper = new QueryWrapper<>();
            totalWrapper.eq("audit_status", "2");
            long totalAgents = aigcAgentService.count(totalWrapper);
            
            // 统计创作者数量（去重）
            String creatorCountSql = "SELECT COUNT(DISTINCT create_by) FROM aigc_agent WHERE audit_status = '2' AND author_type = '2'";
            List<Map<String, Object>> creatorResult = jdbcTemplate.queryForList(creatorCountSql);
            long totalCreators = creatorResult.isEmpty() ? 0 : ((Number) creatorResult.get(0).get("COUNT(DISTINCT create_by)")).longValue();
            
            stats.put("totalAgents", totalAgents);
            stats.put("totalCreators", totalCreators);
            
            return Result.OK(stats);
            
        } catch (Exception e) {
            log.error("获取智能体市场统计信息失败", e);
            return Result.error("获取统计信息失败");
        }
    }

    /**
     * 获取用户角色
     */
    private String getUserRole(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return "user"; // 默认普通用户
            }

            String username = JwtUtil.getUsername(token);
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return "user";
            }

            String userId = userProfile.getUserId();

            // 查询用户角色
            String sql = "SELECT r.role_code FROM sys_user u " +
                        "JOIN sys_user_role ur ON u.id = ur.user_id " +
                        "JOIN sys_role r ON ur.role_id = r.id " +
                        "WHERE u.id = ? AND r.role_code IN ('user', 'VIP', 'SVIP')";

            List<Map<String, Object>> roleResult = jdbcTemplate.queryForList(sql, userId);

            if (!roleResult.isEmpty()) {
                String userRole = (String) roleResult.get(0).get("role_code");
                if ("VIP".equals(userRole) || "SVIP".equals(userRole)) {
                    return userRole;
                }
            }

            return "user";

        } catch (Exception e) {
            log.error("获取用户角色失败", e);
            return "user";
        }
    }

    /**
     * 丰富智能体的创作者信息（从用户扩展表获取nickname和avatar）
     */
    private void enrichAgentWithCreatorInfo(AgentMarketVO vo, String createBy) {
        try {
            if (StringUtils.hasText(createBy)) {
                // 1. 先获取用户基本信息
                SysUser user = sysUserService.getUserByName(createBy);
                if (user != null) {
                    // 2. 根据用户ID获取扩展表中的昵称和头像
                    AicgUserProfile userProfile = userProfileService.getByUserId(user.getId());

                    String creatorName;
                    String creatorAvatar = null;

                    if (userProfile != null && StringUtils.hasText(userProfile.getNickname())) {
                        // 优先使用扩展表中的昵称
                        creatorName = userProfile.getNickname();

                        // 处理头像URL（参考订阅会员快速充值的头像逻辑）
                        if (StringUtils.hasText(userProfile.getAvatar())) {
                            // 有头像时使用TOS服务生成正确的头像URL（支持CDN优先）
                            creatorAvatar = tosService.generateFileUrl(userProfile.getAvatar());
                            log.debug("获取创作者头像成功: userId={}, avatarPath={}, avatarUrl={}",
                                user.getId(), userProfile.getAvatar(), creatorAvatar);
                        } else {
                            // 没有头像时使用TOS默认头像
                            creatorAvatar = tosService.getDefaultAvatarUrl();
                            log.debug("使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                        }
                    } else {
                        // 扩展表没有昵称时，优先使用真实姓名，否则使用用户名
                        creatorName = StringUtils.hasText(user.getRealname()) ? user.getRealname() : user.getUsername();
                        // 没有扩展信息时使用TOS默认头像
                        creatorAvatar = tosService.getDefaultAvatarUrl();
                        log.debug("没有扩展信息，使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                    }

                    // 设置创作者信息到VO
                    vo.setCreatorInfo(user.getUsername(), user.getRealname());
                    vo.setCreatorName(creatorName);
                    vo.setCreatorAvatar(creatorAvatar);

                    log.debug("创作者信息设置完成: createBy={}, creatorName={}, creatorAvatar={}",
                        createBy, creatorName, creatorAvatar);
                } else {
                    // 用户不存在时使用TOS默认头像
                    String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                    vo.setCreatorAvatar(defaultAvatarUrl);
                    log.debug("用户不存在，使用默认头像: createBy={}, defaultUrl={}", createBy, defaultAvatarUrl);
                }
            } else {
                // 没有创建人信息时使用TOS默认头像
                String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                vo.setCreatorAvatar(defaultAvatarUrl);
                log.debug("没有创建人信息，使用默认头像: defaultUrl={}", defaultAvatarUrl);
            }
        } catch (Exception e) {
            log.warn("获取创作者信息失败，创建人: {}", createBy, e);
            // 异常时使用TOS默认头像
            try {
                String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                vo.setCreatorAvatar(defaultAvatarUrl);
            } catch (Exception ex) {
                log.error("获取默认头像也失败", ex);
            }
        }
    }

    /**
     * 丰富智能体的工作流数量信息
     */
    private void enrichAgentWithWorkflowCount(AgentMarketVO vo, String agentId) {
        try {
            String sql = "SELECT COUNT(*) FROM aigc_workflow WHERE agent_id = ?";
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(sql, agentId);

            if (!countResult.isEmpty()) {
                int workflowCount = ((Number) countResult.get(0).get("COUNT(*)")).intValue();
                vo.setWorkflowCount(workflowCount);
            } else {
                vo.setWorkflowCount(0);
            }
        } catch (Exception e) {
            log.warn("获取工作流数量失败: {}", e.getMessage());
            vo.setWorkflowCount(0);
        }
    }

    /**
     * 计算智能体价格信息（用于详情接口）
     */
    private Map<String, Object> calculatePriceInfo(AigcAgent agent, String userRole) {
        Map<String, Object> priceInfo = new HashMap<>();

        BigDecimal originalPrice = agent.getPrice() != null ? agent.getPrice() : BigDecimal.ZERO;
        BigDecimal discountPrice = originalPrice;
        int discountRate = 0;
        boolean isFree = false;
        boolean showSvipPromo = false;
        boolean showDiscountPrice = false;

        // 根据作者类型和用户角色计算折扣
        if ("1".equals(agent.getAuthorType())) {
            // 官方智能体
            if ("SVIP".equals(userRole)) {
                isFree = true;
                discountPrice = BigDecimal.ZERO;
                discountRate = 100;
                showDiscountPrice = false;
            } else if ("VIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.HALF_UP);
                discountRate = 30;
                showDiscountPrice = true;
            } else {
                // 普通用户或未登录用户显示SVIP推广
                showSvipPromo = true;
                showDiscountPrice = false;
            }
        } else if ("2".equals(agent.getAuthorType())) {
            // 创作者智能体
            if ("SVIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.5")).setScale(2, RoundingMode.HALF_UP);
                discountRate = 50;
                showDiscountPrice = true;
            } else if ("VIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.HALF_UP);
                discountRate = 30;
                showDiscountPrice = true;
            } else {
                // 普通用户或未登录用户显示SVIP推广
                showSvipPromo = true;
                showDiscountPrice = false;
            }
        }

        priceInfo.put("originalPrice", originalPrice);
        priceInfo.put("discountPrice", discountPrice);
        priceInfo.put("discountRate", discountRate);
        priceInfo.put("isFree", isFree);
        priceInfo.put("showSvipPromo", showSvipPromo);
        priceInfo.put("showDiscountPrice", showDiscountPrice);

        return priceInfo;
    }

    /**
     * 丰富智能体的价格信息（包含折扣计算）
     */
    private void enrichAgentWithPriceInfo(AgentMarketVO vo, String userRole, String authorType) {
        try {
            BigDecimal originalPrice = vo.getOriginalPrice() != null ? vo.getOriginalPrice() : BigDecimal.ZERO;
            BigDecimal discountPrice = originalPrice;
            int discountRate = 0;
            boolean isFree = false;

            // 根据作者类型和用户角色计算折扣
            if ("1".equals(authorType)) {
                // 官方智能体
                if ("SVIP".equals(userRole)) {
                    isFree = true;
                    discountPrice = BigDecimal.ZERO;
                    discountRate = 100;
                } else if ("VIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.HALF_UP);
                    discountRate = 30;
                }
            } else if ("2".equals(authorType)) {
                // 创作者智能体
                if ("SVIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.5")).setScale(2, RoundingMode.HALF_UP);
                    discountRate = 50;
                } else if ("VIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.HALF_UP);
                    discountRate = 30;
                }
            }

            // 设置价格信息到VO
            vo.setPriceInfo(originalPrice, discountPrice, discountRate, isFree);

        } catch (Exception e) {
            log.warn("计算价格折扣失败: {}", e.getMessage());
            // 设置默认价格信息
            vo.setPriceInfo(vo.getOriginalPrice(), vo.getOriginalPrice(), 0, false);
        }
    }

    /**
     * 创建工作流数据
     */
    private Map<String, Object> createWorkflowData(String id, String name, String description, String avatar, int sequence) {
        Map<String, Object> workflow = new HashMap<>();
        workflow.put("id", id);
        workflow.put("workflowName", name);
        workflow.put("workflowDescription", description);
        workflow.put("agentAvatar", avatar);
        workflow.put("sequence", sequence);
        return workflow;
    }
}
