{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue?vue&type=style&index=0&id=a83192ae&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue", "mtime": 1753944973183}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.related-plugins {\n  margin-bottom: 24px;\n}\n\n.related-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin-bottom: 20px;\n  padding-bottom: 8px;\n  border-bottom: 2px solid #f0f0f0;\n}\n\n.section-title .anticon {\n  color: #1890ff;\n  font-size: 20px;\n}\n\n.category-hint {\n  font-size: 14px;\n  color: #666;\n  font-weight: normal;\n}\n\n.plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.plugin-card {\n  position: relative;\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.plugin-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n  border-color: #1890ff;\n}\n\n.plugin-card:hover .image-overlay {\n  opacity: 1;\n}\n\n.plugin-card:hover .plugin-actions {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.plugin-image {\n  position: relative;\n  height: 160px;\n  overflow: hidden;\n}\n\n.plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.4);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.view-icon {\n  color: white;\n  font-size: 24px;\n}\n\n.plugin-info {\n  padding: 16px;\n}\n\n.plugin-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 8px 0;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.plugin-description {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 0 0 12px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: #666;\n}\n\n.meta-item .anticon {\n  color: #1890ff;\n}\n\n.plugin-tags {\n  display: flex;\n  gap: 6px;\n  flex-wrap: wrap;\n}\n\n.plugin-actions {\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  opacity: 0;\n  transform: translateY(10px);\n  transition: all 0.3s ease;\n}\n\n.no-recommendations {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.more-actions {\n  text-align: center;\n  padding-top: 2rem;\n  margin-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.more-plugins-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 25px;\n  color: white;\n  font-weight: 600;\n  font-size: 1rem;\n  height: 50px;\n  padding: 0 2rem;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.more-plugins-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.more-plugins-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  color: white;\n  border: none;\n}\n\n.more-plugins-btn:hover::before {\n  left: 100%;\n}\n\n.more-plugins-btn:active {\n  transform: translateY(0);\n}\n\n.more-plugins-btn .anticon {\n  font-size: 1.1rem;\n}\n\n.more-plugins-btn .anticon:first-child {\n  margin-right: 0.25rem;\n}\n\n.more-plugins-btn .anticon:last-child {\n  margin-left: 0.25rem;\n  transition: transform 0.3s ease;\n}\n\n.more-plugins-btn:hover .anticon:last-child {\n  transform: translateX(3px);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .plugins-grid {\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n    gap: 16px;\n  }\n  \n  .plugin-card {\n    margin-bottom: 0;\n  }\n  \n  .plugin-actions {\n    position: static;\n    opacity: 1;\n    transform: none;\n    margin-top: 12px;\n  }\n  \n  .image-overlay {\n    display: none;\n  }\n}\n", {"version": 3, "sources": ["RelatedPlugins.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0OA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "RelatedPlugins.vue", "sourceRoot": "src/views/website/market/components", "sourcesContent": ["<template>\n  <div class=\"related-plugins\">\n    <div class=\"related-card\">\n      <h3 class=\"section-title\">\n        <a-icon type=\"appstore\" />\n        相关推荐\n        <span class=\"category-hint\" v-if=\"categoryText\">- {{ categoryText }}</span>\n      </h3>\n      \n      <div v-if=\"recommendations && recommendations.length > 0\" class=\"plugins-grid\">\n        <div \n          v-for=\"plugin in recommendations\" \n          :key=\"plugin.id\"\n          class=\"plugin-card\"\n          @click=\"goToPlugin(plugin.id)\">\n          \n          <!-- 插件图片 -->\n          <div class=\"plugin-image\">\n            <img\n              :src=\"getPluginImage(plugin)\"\n              :alt=\"plugin.plubname\"\n              @error=\"handleImageError\"\n            />\n            <div class=\"image-overlay\">\n              <a-icon type=\"eye\" class=\"view-icon\" />\n            </div>\n          </div>\n          \n          <!-- 插件信息 -->\n          <div class=\"plugin-info\">\n            <h4 class=\"plugin-name\">{{ plugin.plubname }}</h4>\n            <p class=\"plugin-description\">{{ plugin.plubinfo || '暂无描述' }}</p>\n            \n            <div class=\"plugin-meta\">\n              <div class=\"meta-item\">\n                <a-icon type=\"dollar\" />\n                <span>{{ getPluginPriceText(plugin) }}</span>\n              </div>\n            </div>\n            \n            <div class=\"plugin-tags\">\n              <a-tag :color=\"getCategoryColor(plugin.plubCategory)\" size=\"small\">\n                {{ getCategoryText(plugin.plubCategory) }}\n              </a-tag>\n              <a-tag :color=\"getStatusColor(plugin.status)\" size=\"small\">\n                {{ getStatusText(plugin.status) }}\n              </a-tag>\n            </div>\n          </div>\n          \n          <!-- 悬浮操作 -->\n          <div class=\"plugin-actions\">\n            <a-button type=\"primary\" size=\"small\" @click.stop=\"goToPlugin(plugin.id)\">\n              查看详情\n            </a-button>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 无推荐内容 -->\n      <div v-else class=\"no-recommendations\">\n        <a-empty description=\"暂无相关推荐\">\n          <a-button type=\"primary\" @click=\"goToMarket\">\n            <a-icon type=\"shop\" />\n            浏览更多插件\n          </a-button>\n        </a-empty>\n      </div>\n      \n      <!-- 查看更多 -->\n      <div v-if=\"recommendations && recommendations.length > 0\" class=\"more-actions\">\n        <a-button class=\"more-plugins-btn\" @click=\"viewMoreByCategory\">\n          <a-icon type=\"appstore\" />\n          <span>查看更多插件</span>\n          <a-icon type=\"right\" />\n        </a-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getPluginImageUrl } from '../utils/marketUtils'\n\nexport default {\n  name: 'RelatedPlugins',\n  \n  props: {\n    recommendations: {\n      type: Array,\n      default: () => []\n    },\n    currentCategory: {\n      type: String,\n      default: ''\n    },\n    currentPluginId: {\n      type: String,\n      default: ''\n    }\n  },\n\n  data() {\n    return {\n      // 🔥 使用TOS统一管理的默认插件图片\n      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    }\n  },\n\n  async mounted() {\n    console.log('RelatedPlugins组件挂载，推荐数据:', this.recommendations);\n    console.log('推荐数据长度:', this.recommendations ? this.recommendations.length : 0);\n\n    // 🔥 初始化分类数据\n    await this.$categoryService.getCategories();\n  },\n\n  watch: {\n    recommendations: {\n      handler(newVal) {\n        console.log('推荐数据变化:', newVal);\n        console.log('新推荐数据长度:', newVal ? newVal.length : 0);\n      },\n      immediate: true\n    }\n  },\n\n  computed: {\n    categoryText() {\n      // 🔥 使用全局分类字典服务获取分类文本\n      if (!this.currentCategory) {\n        return '相关';\n      }\n      return this.$categoryService.getCategoryText(this.currentCategory);\n    },\n\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';\n    }\n  },\n\n  methods: {\n    // 🔥 获取插件图片（支持组合插件优先级处理，与market页面保持一致）\n    getPluginImage(pluginOrPath) {\n      // 如果传入的是字符串路径，保持向后兼容\n      if (typeof pluginOrPath === 'string') {\n        return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);\n      }\n\n      // 如果传入的是插件对象，使用新的优先级逻辑\n      return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);\n    },\n\n    handleImageError(event) {\n      event.target.src = this.defaultPluginImage;\n    },\n\n    formatNumber(num) {\n      if (!num) return '0';\n      if (num >= 10000) {\n        return (num / 10000).toFixed(1) + '万';\n      }\n      return num.toLocaleString();\n    },\n\n    getCategoryColor(category) {\n      // 🔥 使用全局分类字典服务获取分类颜色\n      return this.$categoryService.getCategoryColor(category);\n    },\n\n    getCategoryText(category) {\n      // 🔥 使用全局分类字典服务获取分类文本\n      return this.$categoryService.getCategoryText(category);\n    },\n\n    getStatusColor(status) {\n      const colors = {\n        0: 'red',     // 下架\n        1: 'green',   // 上架\n        2: 'orange',  // 审核中\n        3: 'red'      // 已拒绝\n      };\n      return colors[status] || 'default';\n    },\n\n    getStatusText(status) {\n      const texts = {\n        0: '已下架',\n        1: '已上架',\n        2: '审核中',\n        3: '已拒绝'\n      };\n      return texts[status] || '未知状态';\n    },\n\n    goToPlugin(pluginId) {\n      if (pluginId === this.currentPluginId) {\n        this.$message.info('这就是当前插件');\n        return;\n      }\n      \n      // 跳转到插件详情页\n      this.$router.push(`/market/plugin/${pluginId}`);\n    },\n\n    goToMarket() {\n      this.$router.push('/market');\n    },\n\n    viewMoreByCategory() {\n      this.$router.push(`/market?category=${this.currentCategory}`);\n    },\n\n    // 🔥 获取插件价格显示文本\n    getPluginPriceText(plugin) {\n      const price = plugin.neednum\n      const isSvipFree = plugin.isSvipFree === 1 || plugin.isSvipFree === '1'\n\n      if (!price || price <= 0) {\n        return '免费'\n      }\n\n      if (isSvipFree) {\n        return `SVIP免费，低至¥${price}/次`\n      } else {\n        return `低至¥${price}/次`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.related-plugins {\n  margin-bottom: 24px;\n}\n\n.related-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin-bottom: 20px;\n  padding-bottom: 8px;\n  border-bottom: 2px solid #f0f0f0;\n}\n\n.section-title .anticon {\n  color: #1890ff;\n  font-size: 20px;\n}\n\n.category-hint {\n  font-size: 14px;\n  color: #666;\n  font-weight: normal;\n}\n\n.plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.plugin-card {\n  position: relative;\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.plugin-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n  border-color: #1890ff;\n}\n\n.plugin-card:hover .image-overlay {\n  opacity: 1;\n}\n\n.plugin-card:hover .plugin-actions {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.plugin-image {\n  position: relative;\n  height: 160px;\n  overflow: hidden;\n}\n\n.plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.4);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.view-icon {\n  color: white;\n  font-size: 24px;\n}\n\n.plugin-info {\n  padding: 16px;\n}\n\n.plugin-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1a1a1a;\n  margin: 0 0 8px 0;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.plugin-description {\n  color: #666;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 0 0 12px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  color: #666;\n}\n\n.meta-item .anticon {\n  color: #1890ff;\n}\n\n.plugin-tags {\n  display: flex;\n  gap: 6px;\n  flex-wrap: wrap;\n}\n\n.plugin-actions {\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  opacity: 0;\n  transform: translateY(10px);\n  transition: all 0.3s ease;\n}\n\n.no-recommendations {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n}\n\n.more-actions {\n  text-align: center;\n  padding-top: 2rem;\n  margin-top: 2rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.more-plugins-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 25px;\n  color: white;\n  font-weight: 600;\n  font-size: 1rem;\n  height: 50px;\n  padding: 0 2rem;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.more-plugins-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.more-plugins-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n  color: white;\n  border: none;\n}\n\n.more-plugins-btn:hover::before {\n  left: 100%;\n}\n\n.more-plugins-btn:active {\n  transform: translateY(0);\n}\n\n.more-plugins-btn .anticon {\n  font-size: 1.1rem;\n}\n\n.more-plugins-btn .anticon:first-child {\n  margin-right: 0.25rem;\n}\n\n.more-plugins-btn .anticon:last-child {\n  margin-left: 0.25rem;\n  transition: transform 0.3s ease;\n}\n\n.more-plugins-btn:hover .anticon:last-child {\n  transform: translateX(3px);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .plugins-grid {\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n    gap: 16px;\n  }\n  \n  .plugin-card {\n    margin-bottom: 0;\n  }\n  \n  .plugin-actions {\n    position: static;\n    opacity: 1;\n    transform: none;\n    margin-top: 12px;\n  }\n  \n  .image-overlay {\n    display: none;\n  }\n}\n</style>\n"]}]}