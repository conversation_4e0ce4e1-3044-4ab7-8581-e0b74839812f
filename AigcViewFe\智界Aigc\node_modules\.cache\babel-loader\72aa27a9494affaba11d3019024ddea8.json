{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue", "mtime": 1754042137915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport AgentCard from './AgentCard.vue';\nimport AgentDetailModal from './AgentDetailModal.vue';\nimport { getUserRole } from '@/utils/roleUtils';\nexport default {\n  name: 'AgentMarket',\n  components: {\n    AgentCard: AgentCard,\n    AgentDetailModal: AgentDetailModal\n  },\n  data: function data() {\n    return {\n      loading: false,\n      loadingMore: false,\n      searchQuery: '',\n      authorTypeFilter: '',\n      agentList: [],\n      userRole: 'user',\n      // 用户角色\n      currentPage: 1,\n      pageSize: 16,\n      totalCount: 0,\n      hasMore: true,\n      // 详情弹窗相关\n      detailModalVisible: false,\n      // 详情弹窗显示状态\n      selectedAgentId: '',\n      // 选中的智能体ID\n      selectedAgent: null,\n      // 选中的智能体数据\n      purchasedAgents: [] // 已购买的智能体ID列表\n\n    };\n  },\n  computed: {\n    // 检查选中的智能体是否已购买\n    isSelectedAgentPurchased: function isSelectedAgentPurchased() {\n      return this.selectedAgentId && this.purchasedAgents.includes(this.selectedAgentId);\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadUserRole();\n\n            case 2:\n              _context.next = 4;\n              return this.loadPurchasedAgents();\n\n            case 4:\n              _context.next = 6;\n              return this.loadAgentList();\n\n            case 6:\n              this.setupIntersectionObserver();\n\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  beforeDestroy: function beforeDestroy() {\n    if (this.observer) {\n      this.observer.disconnect();\n    }\n  },\n  methods: {\n    // 加载用户角色\n    loadUserRole: function () {\n      var _loadUserRole = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var role;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                _context2.next = 3;\n                return getUserRole();\n\n              case 3:\n                role = _context2.sent;\n                this.userRole = role;\n                console.log('🔍 AgentMarket: 用户角色:', this.userRole);\n                _context2.next = 12;\n                break;\n\n              case 8:\n                _context2.prev = 8;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('获取用户角色失败:', _context2.t0);\n                this.userRole = null;\n\n              case 12:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 8]]);\n      }));\n\n      function loadUserRole() {\n        return _loadUserRole.apply(this, arguments);\n      }\n\n      return loadUserRole;\n    }(),\n    // 加载智能体列表（首次加载或搜索时重置）\n    loadAgentList: function () {\n      var _loadAgentList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var _this = this;\n\n        var reset,\n            params,\n            response,\n            data,\n            newRecords,\n            processedRecords,\n            _this$agentList,\n            _args3 = arguments;\n\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                reset = _args3.length > 0 && _args3[0] !== undefined ? _args3[0] : true;\n\n                if (reset) {\n                  this.loading = true;\n                  this.currentPage = 1;\n                  this.agentList = [];\n                  this.hasMore = true;\n                } else {\n                  this.loadingMore = true;\n                }\n\n                _context3.prev = 2;\n                params = {\n                  pageNo: this.currentPage,\n                  pageSize: this.pageSize,\n                  agentName: this.searchQuery || undefined,\n                  authorType: this.authorTypeFilter || undefined,\n                  auditStatus: '2' // 只显示审核通过的\n\n                }; // 调用后端API\n\n                _context3.next = 6;\n                return this.$http.get('/api/agent/market/list', {\n                  params: params\n                });\n\n              case 6:\n                response = _context3.sent;\n                console.log('API响应:', response); // 兼容不同的响应格式\n\n                data = response.data || response;\n\n                if (data && data.success) {\n                  newRecords = data.result.records || []; // 对每个智能体添加价格信息和购买状态\n\n                  processedRecords = newRecords.map(function (agent) {\n                    var priceInfo = _this.calculatePrice(agent);\n\n                    var isPurchased = _this.isAgentPurchased(agent.id);\n\n                    return _objectSpread(_objectSpread(_objectSpread({}, agent), priceInfo), {}, {\n                      isPurchased: isPurchased\n                    });\n                  });\n\n                  if (reset) {\n                    this.agentList = processedRecords;\n                  } else {\n                    (_this$agentList = this.agentList).push.apply(_this$agentList, _toConsumableArray(processedRecords));\n                  }\n\n                  this.totalCount = data.result.total || 0; // 判断是否还有更多数据\n\n                  this.hasMore = this.agentList.length < this.totalCount; // 如果有数据，准备下一页\n\n                  if (newRecords.length > 0) {\n                    this.currentPage++;\n                  }\n                } else {\n                  this.$message.error(data && data.message || '获取智能体列表失败');\n                }\n\n                _context3.next = 16;\n                break;\n\n              case 12:\n                _context3.prev = 12;\n                _context3.t0 = _context3[\"catch\"](2);\n                console.error('加载智能体列表失败:', _context3.t0);\n                this.$message.error('加载智能体列表失败，请稍后重试');\n\n              case 16:\n                _context3.prev = 16;\n                this.loading = false;\n                this.loadingMore = false; // 重新设置IntersectionObserver，确保监听新的DOM元素\n\n                if (reset) {\n                  this.$nextTick(function () {\n                    _this.setupIntersectionObserver();\n                  });\n                }\n\n                return _context3.finish(16);\n\n              case 21:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[2, 12, 16, 21]]);\n      }));\n\n      function loadAgentList() {\n        return _loadAgentList.apply(this, arguments);\n      }\n\n      return loadAgentList;\n    }(),\n    // 临时模拟数据\n    loadMockData: function () {\n      var _loadMockData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.next = 2;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 500);\n                });\n\n              case 2:\n                this.agentList = [];\n                this.totalCount = 0;\n                this.hasMore = false;\n\n              case 5:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this);\n      }));\n\n      function loadMockData() {\n        return _loadMockData.apply(this, arguments);\n      }\n\n      return loadMockData;\n    }(),\n    // 加载更多数据\n    loadMore: function () {\n      var _loadMore = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                if (!(!this.hasMore || this.loadingMore)) {\n                  _context5.next = 2;\n                  break;\n                }\n\n                return _context5.abrupt(\"return\");\n\n              case 2:\n                _context5.next = 4;\n                return this.loadAgentList(false);\n\n              case 4:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this);\n      }));\n\n      function loadMore() {\n        return _loadMore.apply(this, arguments);\n      }\n\n      return loadMore;\n    }(),\n    // 计算价格和推广标签显示\n    calculatePrice: function calculatePrice(agent) {\n      var showSvipPromo = false;\n      var showDiscountPrice = false;\n      var discountRate = 1; // 默认无折扣\n\n      var isFree = false; // 是否免费\n      // 根据用户角色计算价格和推广显示\n\n      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {\n        // 未登录、普通用户或管理员：显示SVIP推广标签\n        showSvipPromo = true;\n        showDiscountPrice = false;\n      } else if (this.userRole === 'VIP') {\n        // VIP用户：显示7折价格，不显示推广标签\n        showSvipPromo = false;\n        showDiscountPrice = true;\n        discountRate = 0.7; // VIP 7折\n      } else if (this.userRole === 'SVIP') {\n        // SVIP用户：根据作者类型计算价格\n        showSvipPromo = false;\n\n        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {\n          // 官方智能体：免费\n          isFree = true;\n          discountRate = 0;\n          showDiscountPrice = false; // 免费时不显示折扣价格\n        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {\n          // 创作者智能体：5折\n          isFree = false;\n          showDiscountPrice = true;\n          discountRate = 0.5;\n        }\n      }\n\n      console.log('🔍 calculatePrice [NEW VERSION]: 智能体名称:', agent && agent.agentName, '用户角色:', this.userRole, '作者类型:', agent && agent.authorType, '显示SVIP推广:', showSvipPromo, '显示折扣价:', showDiscountPrice, '折扣率:', discountRate, '是否免费:', isFree);\n      return {\n        showSvipPromo: showSvipPromo,\n        showDiscountPrice: showDiscountPrice,\n        discountRate: discountRate,\n        isFree: isFree\n      };\n    },\n    // 搜索处理\n    handleSearch: function handleSearch() {\n      this.scrollToTop();\n      this.loadAgentList(true);\n    },\n    // 筛选变化处理\n    handleFilterChange: function handleFilterChange() {\n      this.scrollToTop();\n      this.loadAgentList(true);\n    },\n    // 滚动到顶部\n    scrollToTop: function scrollToTop() {\n      // 滚动到页面顶部\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    },\n    // 设置滚动监听\n    setupIntersectionObserver: function setupIntersectionObserver() {\n      var _this2 = this;\n\n      // 先断开旧的observer\n      if (this.observer) {\n        this.observer.disconnect();\n        this.observer = null;\n      }\n\n      this.$nextTick(function () {\n        var target = _this2.$refs.loadMoreTrigger;\n        if (!target) return;\n        _this2.observer = new IntersectionObserver(function (entries) {\n          entries.forEach(function (entry) {\n            if (entry.isIntersecting && _this2.hasMore && !_this2.loadingMore) {\n              console.log('触发懒加载，hasMore:', _this2.hasMore, 'loadingMore:', _this2.loadingMore);\n\n              _this2.loadMore();\n            }\n          });\n        }, {\n          rootMargin: '100px' // 提前100px开始加载\n\n        });\n\n        _this2.observer.observe(target);\n\n        console.log('IntersectionObserver已重新设置');\n      });\n    },\n    // 查看详情\n    handleViewDetail: function handleViewDetail(agent) {\n      console.log('查看智能体详情:', agent);\n      this.selectedAgent = agent;\n      this.selectedAgentId = agent.id;\n      this.detailModalVisible = true;\n    },\n    // 关闭详情弹窗\n    handleCloseDetailModal: function handleCloseDetailModal() {\n      this.detailModalVisible = false;\n      this.selectedAgent = null;\n      this.selectedAgentId = '';\n    },\n    // 从弹窗发起购买\n    handlePurchaseFromModal: function handlePurchaseFromModal(agent) {\n      console.log('从弹窗购买智能体:', agent); // 购买逻辑已在AgentDetailModal中实现\n    },\n    // 购买成功回调\n    handlePurchaseSuccess: function handlePurchaseSuccess(agentId) {\n      console.log('购买成功回调:', agentId);\n      this.onPurchaseSuccess(agentId);\n      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了');\n    },\n    // 加载已购买的智能体列表\n    loadPurchasedAgents: function () {\n      var _loadPurchasedAgents = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6() {\n        var token, cacheKey, cacheTimeKey, cacheTime, now, cacheExpiry, cached;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                // 检查是否已登录\n                token = this.$store.getters.token;\n\n                if (token) {\n                  _context6.next = 5;\n                  break;\n                }\n\n                console.log('用户未登录，跳过购买状态检查');\n                return _context6.abrupt(\"return\");\n\n              case 5:\n                // 检查缓存时效性（5分钟）\n                cacheKey = 'purchasedAgents';\n                cacheTimeKey = 'purchasedAgentsTime';\n                cacheTime = localStorage.getItem(cacheTimeKey);\n                now = Date.now();\n                cacheExpiry = 5 * 60 * 1000; // 5分钟\n                // 从缓存中获取\n\n                cached = localStorage.getItem(cacheKey);\n\n                if (!(cached && cacheTime && now - parseInt(cacheTime) < cacheExpiry)) {\n                  _context6.next = 23;\n                  break;\n                }\n\n                _context6.prev = 12;\n                this.purchasedAgents = JSON.parse(cached);\n                console.log('🔍 从缓存加载已购买智能体:', this.purchasedAgents.length, '个');\n                return _context6.abrupt(\"return\");\n\n              case 18:\n                _context6.prev = 18;\n                _context6.t0 = _context6[\"catch\"](12);\n                console.warn('购买状态缓存解析失败:', _context6.t0);\n                localStorage.removeItem(cacheKey);\n                localStorage.removeItem(cacheTimeKey);\n\n              case 23:\n                _context6.next = 28;\n                break;\n\n              case 25:\n                _context6.prev = 25;\n                _context6.t1 = _context6[\"catch\"](0);\n                console.error('❌ 加载购买状态失败:', _context6.t1);\n\n              case 28:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 25], [12, 18]]);\n      }));\n\n      function loadPurchasedAgents() {\n        return _loadPurchasedAgents.apply(this, arguments);\n      }\n\n      return loadPurchasedAgents;\n    }(),\n    // 检查智能体是否已购买\n    isAgentPurchased: function isAgentPurchased(agentId) {\n      return this.purchasedAgents.includes(agentId);\n    },\n    // 购买成功后更新状态\n    onPurchaseSuccess: function onPurchaseSuccess(agentId) {\n      if (!this.purchasedAgents.includes(agentId)) {\n        this.purchasedAgents.push(agentId);\n        localStorage.setItem('purchasedAgents', JSON.stringify(this.purchasedAgents));\n        console.log('✅ 购买状态已更新:', agentId); // 更新智能体列表中的购买状态\n\n        this.agentList.forEach(function (agent) {\n          if (agent.id === agentId) {\n            agent.isPurchased = true;\n          }\n        });\n      }\n    },\n    // 刷新数据\n    handleRefresh: function handleRefresh() {\n      this.loadAgentList();\n    },\n    // 获取创作者数量\n    getCreatorCount: function getCreatorCount() {\n      return this.agentList.filter(function (agent) {\n        return agent.authorType === '2';\n      }).length;\n    },\n    // 清空搜索\n    clearSearch: function clearSearch() {\n      this.searchQuery = '';\n      this.handleSearch();\n    }\n  }\n};", {"version": 3, "sources": ["AgentMarket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA,OAAA,SAAA,MAAA,iBAAA;AACA,OAAA,gBAAA,MAAA,wBAAA;AACA,SAAA,WAAA,QAAA,mBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,aADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA,SADA;AAEA,IAAA,gBAAA,EAAA;AAFA,GAFA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,WAAA,EAAA,KAFA;AAGA,MAAA,WAAA,EAAA,EAHA;AAIA,MAAA,gBAAA,EAAA,EAJA;AAKA,MAAA,SAAA,EAAA,EALA;AAMA,MAAA,QAAA,EAAA,MANA;AAMA;AACA,MAAA,WAAA,EAAA,CAPA;AAQA,MAAA,QAAA,EAAA,EARA;AASA,MAAA,UAAA,EAAA,CATA;AAUA,MAAA,OAAA,EAAA,IAVA;AAWA;AACA,MAAA,kBAAA,EAAA,KAZA;AAYA;AACA,MAAA,eAAA,EAAA,EAbA;AAaA;AACA,MAAA,aAAA,EAAA,IAdA;AAcA;AACA,MAAA,eAAA,EAAA,EAfA,CAeA;;AAfA,KAAA;AAiBA,GAxBA;AAyBA,EAAA,QAAA,EAAA;AACA;AACA,IAAA,wBAFA,sCAEA;AACA,aAAA,KAAA,eAAA,IAAA,KAAA,eAAA,CAAA,QAAA,CAAA,KAAA,eAAA,CAAA;AACA;AAJA,GAzBA;AA+BA,EAAA,OA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAgCA,KAAA,YAAA,EAhCA;;AAAA;AAAA;AAAA,qBAiCA,KAAA,mBAAA,EAjCA;;AAAA;AAAA;AAAA,qBAkCA,KAAA,aAAA,EAlCA;;AAAA;AAmCA,mBAAA,yBAAA;;AAnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsCA,EAAA,aAtCA,2BAsCA;AACA,QAAA,KAAA,QAAA,EAAA;AACA,WAAA,QAAA,CAAA,UAAA;AACA;AACA,GA1CA;AA2CA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,YAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAIA,WAAA,EAJA;;AAAA;AAIA,gBAAA,IAJA;AAKA,qBAAA,QAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA,KAAA,QAAA;AANA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,GAAA,IAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAaA;AACA,IAAA,aAdA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAcA,gBAAA,KAdA,8DAcA,IAdA;;AAeA,oBAAA,KAAA,EAAA;AACA,uBAAA,OAAA,GAAA,IAAA;AACA,uBAAA,WAAA,GAAA,CAAA;AACA,uBAAA,SAAA,GAAA,EAAA;AACA,uBAAA,OAAA,GAAA,IAAA;AACA,iBALA,MAKA;AACA,uBAAA,WAAA,GAAA,IAAA;AACA;;AAtBA;AAyBA,gBAAA,MAzBA,GAyBA;AACA,kBAAA,MAAA,EAAA,KAAA,WADA;AAEA,kBAAA,QAAA,EAAA,KAAA,QAFA;AAGA,kBAAA,SAAA,EAAA,KAAA,WAAA,IAAA,SAHA;AAIA,kBAAA,UAAA,EAAA,KAAA,gBAAA,IAAA,SAJA;AAKA,kBAAA,WAAA,EAAA,GALA,CAKA;;AALA,iBAzBA,EAiCA;;AAjCA;AAAA,uBAkCA,KAAA,KAAA,CAAA,GAAA,CAAA,wBAAA,EAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,CAlCA;;AAAA;AAkCA,gBAAA,QAlCA;AAmCA,gBAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,QAAA,EAnCA,CAqCA;;AACA,gBAAA,IAtCA,GAsCA,QAAA,CAAA,IAAA,IAAA,QAtCA;;AAuCA,oBAAA,IAAA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,UADA,GACA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EADA,EAGA;;AACA,kBAAA,gBAJA,GAIA,UAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,wBAAA,SAAA,GAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA;;AACA,wBAAA,WAAA,GAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,EAAA,CAAA;;AACA,yEACA,KADA,GAEA,SAFA;AAGA,sBAAA,WAAA,EAAA;AAHA;AAKA,mBARA,CAJA;;AAcA,sBAAA,KAAA,EAAA;AACA,yBAAA,SAAA,GAAA,gBAAA;AACA,mBAFA,MAEA;AACA,4CAAA,SAAA,EAAA,IAAA,2CAAA,gBAAA;AACA;;AAEA,uBAAA,UAAA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,CAAA,CApBA,CAsBA;;AACA,uBAAA,OAAA,GAAA,KAAA,SAAA,CAAA,MAAA,GAAA,KAAA,UAAA,CAvBA,CAyBA;;AACA,sBAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,yBAAA,WAAA;AACA;AACA,iBA7BA,MA6BA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,IAAA,IAAA,IAAA,CAAA,OAAA,IAAA,WAAA;AACA;;AAtEA;AAAA;;AAAA;AAAA;AAAA;AAwEA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,iBAAA;;AAzEA;AAAA;AA2EA,qBAAA,OAAA,GAAA,KAAA;AACA,qBAAA,WAAA,GAAA,KAAA,CA5EA,CA8EA;;AACA,oBAAA,KAAA,EAAA;AACA,uBAAA,SAAA,CAAA,YAAA;AACA,oBAAA,KAAA,CAAA,yBAAA;AACA,mBAFA;AAGA;;AAnFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAuFA;AACA,IAAA,YAxFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBA0FA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,yBAAA,UAAA,CAAA,OAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CA1FA;;AAAA;AA4FA,qBAAA,SAAA,GAAA,EAAA;AACA,qBAAA,UAAA,GAAA,CAAA;AACA,qBAAA,OAAA,GAAA,KAAA;;AA9FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiGA;AACA,IAAA,QAlGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAmGA,CAAA,KAAA,OAAA,IAAA,KAAA,WAnGA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAAA;AAAA,uBAsGA,KAAA,aAAA,CAAA,KAAA,CAtGA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyGA;AACA,IAAA,cA1GA,0BA0GA,KA1GA,EA0GA;AACA,UAAA,aAAA,GAAA,KAAA;AACA,UAAA,iBAAA,GAAA,KAAA;AACA,UAAA,YAAA,GAAA,CAAA,CAHA,CAGA;;AACA,UAAA,MAAA,GAAA,KAAA,CAJA,CAIA;AAEA;;AACA,UAAA,KAAA,QAAA,KAAA,IAAA,IAAA,KAAA,QAAA,KAAA,MAAA,IAAA,KAAA,QAAA,KAAA,OAAA,EAAA;AACA;AACA,QAAA,aAAA,GAAA,IAAA;AACA,QAAA,iBAAA,GAAA,KAAA;AACA,OAJA,MAIA,IAAA,KAAA,QAAA,KAAA,KAAA,EAAA;AACA;AACA,QAAA,aAAA,GAAA,KAAA;AACA,QAAA,iBAAA,GAAA,IAAA;AACA,QAAA,YAAA,GAAA,GAAA,CAJA,CAIA;AACA,OALA,MAKA,IAAA,KAAA,QAAA,KAAA,MAAA,EAAA;AACA;AACA,QAAA,aAAA,GAAA,KAAA;;AAEA,YAAA,KAAA,KAAA,KAAA,CAAA,UAAA,KAAA,CAAA,IAAA,KAAA,CAAA,UAAA,KAAA,GAAA,CAAA,EAAA;AACA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,UAAA,iBAAA,GAAA,KAAA,CAJA,CAIA;AACA,SALA,MAKA,IAAA,KAAA,KAAA,KAAA,CAAA,UAAA,KAAA,CAAA,IAAA,KAAA,CAAA,UAAA,KAAA,GAAA,CAAA,EAAA;AACA;AACA,UAAA,MAAA,GAAA,KAAA;AACA,UAAA,iBAAA,GAAA,IAAA;AACA,UAAA,YAAA,GAAA,GAAA;AACA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,yCAAA,EAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA,OAAA,EAAA,KAAA,QAAA,EAAA,OAAA,EAAA,KAAA,IAAA,KAAA,CAAA,UAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,YAAA,EAAA,OAAA,EAAA,MAAA;AAEA,aAAA;AACA,QAAA,aAAA,EAAA,aADA;AAEA,QAAA,iBAAA,EAAA,iBAFA;AAGA,QAAA,YAAA,EAAA,YAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAAA;AAMA,KAnJA;AAqJA;AACA,IAAA,YAtJA,0BAsJA;AACA,WAAA,WAAA;AACA,WAAA,aAAA,CAAA,IAAA;AACA,KAzJA;AA2JA;AACA,IAAA,kBA5JA,gCA4JA;AACA,WAAA,WAAA;AACA,WAAA,aAAA,CAAA,IAAA;AACA,KA/JA;AAiKA;AACA,IAAA,WAlKA,yBAkKA;AACA;AACA,MAAA,MAAA,CAAA,QAAA,CAAA;AACA,QAAA,GAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAAA;AAIA,KAxKA;AA0KA;AACA,IAAA,yBA3KA,uCA2KA;AAAA;;AACA;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,UAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;;AAEA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,GAAA,MAAA,CAAA,KAAA,CAAA,eAAA;AACA,YAAA,CAAA,MAAA,EAAA;AAEA,QAAA,MAAA,CAAA,QAAA,GAAA,IAAA,oBAAA,CAAA,UAAA,OAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,gBAAA,KAAA,CAAA,cAAA,IAAA,MAAA,CAAA,OAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,MAAA,CAAA,OAAA,EAAA,cAAA,EAAA,MAAA,CAAA,WAAA;;AACA,cAAA,MAAA,CAAA,QAAA;AACA;AACA,WALA;AAMA,SAPA,EAOA;AACA,UAAA,UAAA,EAAA,OADA,CACA;;AADA,SAPA,CAAA;;AAWA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,2BAAA;AACA,OAjBA;AAkBA,KApMA;AAsMA;AACA,IAAA,gBAvMA,4BAuMA,KAvMA,EAuMA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,eAAA,GAAA,KAAA,CAAA,EAAA;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,KA5MA;AA8MA;AACA,IAAA,sBA/MA,oCA+MA;AACA,WAAA,kBAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,KAnNA;AAqNA;AACA,IAAA,uBAtNA,mCAsNA,KAtNA,EAsNA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,EADA,CAEA;AACA,KAzNA;AA2NA;AACA,IAAA,qBA5NA,iCA4NA,OA5NA,EA4NA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA;AACA,WAAA,iBAAA,CAAA,OAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,yBAAA;AACA,KAhOA;AAkOA;AACA,IAAA,mBAnOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqOA;AACA,gBAAA,KAtOA,GAsOA,KAAA,MAAA,CAAA,OAAA,CAAA,KAtOA;;AAAA,oBAuOA,KAvOA;AAAA;AAAA;AAAA;;AAwOA,gBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA;AAxOA;;AAAA;AA4OA;AACA,gBAAA,QA7OA,GA6OA,iBA7OA;AA8OA,gBAAA,YA9OA,GA8OA,qBA9OA;AA+OA,gBAAA,SA/OA,GA+OA,YAAA,CAAA,OAAA,CAAA,YAAA,CA/OA;AAgPA,gBAAA,GAhPA,GAgPA,IAAA,CAAA,GAAA,EAhPA;AAiPA,gBAAA,WAjPA,GAiPA,IAAA,EAAA,GAAA,IAjPA,EAiPA;AAEA;;AACA,gBAAA,MApPA,GAoPA,YAAA,CAAA,OAAA,CAAA,QAAA,CApPA;;AAAA,sBAqPA,MAAA,IAAA,SAAA,IAAA,GAAA,GAAA,QAAA,CAAA,SAAA,CAAA,GAAA,WArPA;AAAA;AAAA;AAAA;;AAAA;AAuPA,qBAAA,eAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,EAAA,KAAA,eAAA,CAAA,MAAA,EAAA,GAAA;AAxPA;;AAAA;AAAA;AAAA;AA2PA,gBAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA,gBAAA,YAAA,CAAA,UAAA,CAAA,QAAA;AACA,gBAAA,YAAA,CAAA,UAAA,CAAA,YAAA;;AA7PA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA2QA,gBAAA,OAAA,CAAA,KAAA,CAAA,aAAA;;AA3QA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+QA;AACA,IAAA,gBAhRA,4BAgRA,OAhRA,EAgRA;AACA,aAAA,KAAA,eAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AACA,KAlRA;AAoRA;AACA,IAAA,iBArRA,6BAqRA,OArRA,EAqRA;AACA,UAAA,CAAA,KAAA,eAAA,CAAA,QAAA,CAAA,OAAA,CAAA,EAAA;AACA,aAAA,eAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,KAAA,eAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,OAAA,EAHA,CAKA;;AACA,aAAA,SAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,CAAA,EAAA,KAAA,OAAA,EAAA;AACA,YAAA,KAAA,CAAA,WAAA,GAAA,IAAA;AACA;AACA,SAJA;AAKA;AACA,KAlSA;AAoSA;AACA,IAAA,aArSA,2BAqSA;AACA,WAAA,aAAA;AACA,KAvSA;AAySA;AACA,IAAA,eA1SA,6BA0SA;AACA,aAAA,KAAA,SAAA,CAAA,MAAA,CAAA,UAAA,KAAA;AAAA,eAAA,KAAA,CAAA,UAAA,KAAA,GAAA;AAAA,OAAA,EAAA,MAAA;AACA,KA5SA;AA8SA;AACA,IAAA,WA/SA,yBA+SA;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,YAAA;AACA;AAlTA;AA3CA,CAAA", "sourcesContent": ["<template>\n  <div class=\"agent-market\">\n    <!-- 固定的搜索和筛选区域 -->\n    <div class=\"sticky-filters\">\n      <div class=\"market-filters\">\n        <div class=\"filter-row\">\n          <!-- 搜索框 -->\n          <div class=\"search-box\">\n            <div class=\"search-wrapper\">\n              <a-icon type=\"search\" class=\"search-icon\" />\n              <a-input\n                v-model=\"searchQuery\"\n                placeholder=\"搜索智能体名称、描述或标签...\"\n                size=\"large\"\n                @pressEnter=\"handleSearch\"\n                @input=\"handleSearch\"\n                class=\"search-input\"\n              />\n              <a-icon\n                v-if=\"searchQuery\"\n                type=\"close-circle\"\n                class=\"clear-icon\"\n                @click=\"clearSearch\"\n              />\n            </div>\n          </div>\n\n          <!-- 筛选器 -->\n          <div class=\"filter-controls\">\n            <div class=\"filter-item-inline\">\n              <span class=\"filter-label\">作者类型：</span>\n              <a-select\n                v-model=\"authorTypeFilter\"\n                placeholder=\"全部类型\"\n                size=\"large\"\n                class=\"filter-select\"\n                @change=\"handleFilterChange\"\n              >\n                <a-select-option value=\"\">全部类型</a-select-option>\n                <a-select-option value=\"1\">\n                  <a-icon type=\"crown\" style=\"color: #f59e0b; margin-right: 4px;\" />\n                  官方\n                </a-select-option>\n                <a-select-option value=\"2\">\n                  <a-icon type=\"user\" style=\"color: #3b82f6; margin-right: 4px;\" />\n                  创作者\n                </a-select-option>\n              </a-select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能体列表 -->\n    <div class=\"agent-list\" v-if=\"!loading\">\n      <div class=\"list-header\">\n        <h3 class=\"list-title\">\n          智能体列表\n          <span class=\"list-count\">({{ totalCount }}个)</span>\n        </h3>\n      </div>\n\n      <!-- 智能体卡片网格 -->\n      <div class=\"agent-grid\" v-if=\"agentList.length > 0\">\n        <AgentCard\n          v-for=\"agent in agentList\"\n          :key=\"agent.id\"\n          :agent=\"agent\"\n          @view-detail=\"handleViewDetail\"\n        />\n      </div>\n\n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <a-empty\n          description=\"暂无智能体数据\"\n        >\n          <a-button type=\"primary\" @click=\"handleRefresh\">\n            <a-icon type=\"reload\" />\n            刷新数据\n          </a-button>\n        </a-empty>\n      </div>\n\n      <!-- 加载更多提示 -->\n      <div class=\"load-more-wrapper\" v-if=\"agentList.length > 0\">\n        <div v-if=\"loadingMore\" class=\"loading-more\">\n          <a-spin size=\"small\" />\n          <span>正在加载更多...</span>\n        </div>\n        <div v-else-if=\"hasMore\" class=\"load-more-trigger\" ref=\"loadMoreTrigger\">\n          <!-- 滚动到这里触发加载更多 -->\n        </div>\n        <div v-else class=\"no-more-data\">\n          <a-icon type=\"check-circle\" />\n          <span>已加载全部数据 (共{{ totalCount }}个)</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-else class=\"loading-state\">\n      <a-spin size=\"large\" tip=\"正在加载智能体数据...\">\n        <div class=\"loading-placeholder\"></div>\n      </a-spin>\n    </div>\n\n    <!-- 智能体详情弹窗 -->\n    <AgentDetailModal\n      :visible=\"detailModalVisible\"\n      :agentId=\"selectedAgentId\"\n      :isPurchased=\"isSelectedAgentPurchased\"\n      @close=\"handleCloseDetailModal\"\n      @purchase=\"handlePurchaseFromModal\"\n      @purchase-success=\"handlePurchaseSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport AgentCard from './AgentCard.vue'\nimport AgentDetailModal from './AgentDetailModal.vue'\nimport { getUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'AgentMarket',\n  components: {\n    AgentCard,\n    AgentDetailModal\n  },\n  data() {\n    return {\n      loading: false,\n      loadingMore: false,\n      searchQuery: '',\n      authorTypeFilter: '',\n      agentList: [],\n      userRole: 'user', // 用户角色\n      currentPage: 1,\n      pageSize: 16,\n      totalCount: 0,\n      hasMore: true,\n      // 详情弹窗相关\n      detailModalVisible: false, // 详情弹窗显示状态\n      selectedAgentId: '', // 选中的智能体ID\n      selectedAgent: null, // 选中的智能体数据\n      purchasedAgents: [] // 已购买的智能体ID列表\n    }\n  },\n  computed: {\n    // 检查选中的智能体是否已购买\n    isSelectedAgentPurchased() {\n      return this.selectedAgentId && this.purchasedAgents.includes(this.selectedAgentId)\n    }\n  },\n  async mounted() {\n    await this.loadUserRole()\n    await this.loadPurchasedAgents()\n    await this.loadAgentList()\n    this.setupIntersectionObserver()\n  },\n\n  beforeDestroy() {\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n  },\n  methods: {\n    // 加载用户角色\n    async loadUserRole() {\n      try {\n        const role = await getUserRole()\n        this.userRole = role\n        console.log('🔍 AgentMarket: 用户角色:', this.userRole)\n      } catch (error) {\n        console.error('获取用户角色失败:', error)\n        this.userRole = null\n      }\n    },\n\n    // 加载智能体列表（首次加载或搜索时重置）\n    async loadAgentList(reset = true) {\n      if (reset) {\n        this.loading = true\n        this.currentPage = 1\n        this.agentList = []\n        this.hasMore = true\n      } else {\n        this.loadingMore = true\n      }\n\n      try {\n        const params = {\n          pageNo: this.currentPage,\n          pageSize: this.pageSize,\n          agentName: this.searchQuery || undefined,\n          authorType: this.authorTypeFilter || undefined,\n          auditStatus: '2' // 只显示审核通过的\n        }\n\n        // 调用后端API\n        const response = await this.$http.get('/api/agent/market/list', { params })\n        console.log('API响应:', response)\n\n        // 兼容不同的响应格式\n        const data = response.data || response\n        if (data && data.success) {\n          const newRecords = data.result.records || []\n\n          // 对每个智能体添加价格信息和购买状态\n          const processedRecords = newRecords.map(agent => {\n            const priceInfo = this.calculatePrice(agent)\n            const isPurchased = this.isAgentPurchased(agent.id)\n            return {\n              ...agent,\n              ...priceInfo,\n              isPurchased\n            }\n          })\n\n          if (reset) {\n            this.agentList = processedRecords\n          } else {\n            this.agentList.push(...processedRecords)\n          }\n\n          this.totalCount = data.result.total || 0\n\n          // 判断是否还有更多数据\n          this.hasMore = this.agentList.length < this.totalCount\n\n          // 如果有数据，准备下一页\n          if (newRecords.length > 0) {\n            this.currentPage++\n          }\n        } else {\n          this.$message.error((data && data.message) || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('加载智能体列表失败，请稍后重试')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n\n        // 重新设置IntersectionObserver，确保监听新的DOM元素\n        if (reset) {\n          this.$nextTick(() => {\n            this.setupIntersectionObserver()\n          })\n        }\n      }\n    },\n\n    // 临时模拟数据\n    async loadMockData() {\n      // 模拟API延迟\n      await new Promise(resolve => setTimeout(resolve, 500))\n\n      this.agentList = []\n      this.totalCount = 0\n      this.hasMore = false\n    },\n\n    // 加载更多数据\n    async loadMore() {\n      if (!this.hasMore || this.loadingMore) {\n        return\n      }\n      await this.loadAgentList(false)\n    },\n\n    // 计算价格和推广标签显示\n    calculatePrice(agent) {\n      let showSvipPromo = false\n      let showDiscountPrice = false\n      let discountRate = 1 // 默认无折扣\n      let isFree = false // 是否免费\n\n      // 根据用户角色计算价格和推广显示\n      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {\n        // 未登录、普通用户或管理员：显示SVIP推广标签\n        showSvipPromo = true\n        showDiscountPrice = false\n      } else if (this.userRole === 'VIP') {\n        // VIP用户：显示7折价格，不显示推广标签\n        showSvipPromo = false\n        showDiscountPrice = true\n        discountRate = 0.7 // VIP 7折\n      } else if (this.userRole === 'SVIP') {\n        // SVIP用户：根据作者类型计算价格\n        showSvipPromo = false\n\n        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {\n          // 官方智能体：免费\n          isFree = true\n          discountRate = 0\n          showDiscountPrice = false // 免费时不显示折扣价格\n        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {\n          // 创作者智能体：5折\n          isFree = false\n          showDiscountPrice = true\n          discountRate = 0.5\n        }\n      }\n\n      console.log('🔍 calculatePrice [NEW VERSION]: 智能体名称:', agent && agent.agentName, '用户角色:', this.userRole, '作者类型:', agent && agent.authorType, '显示SVIP推广:', showSvipPromo, '显示折扣价:', showDiscountPrice, '折扣率:', discountRate, '是否免费:', isFree)\n\n      return {\n        showSvipPromo,\n        showDiscountPrice,\n        discountRate,\n        isFree\n      }\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 筛选变化处理\n    handleFilterChange() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 滚动到顶部\n    scrollToTop() {\n      // 滚动到页面顶部\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      })\n    },\n\n    // 设置滚动监听\n    setupIntersectionObserver() {\n      // 先断开旧的observer\n      if (this.observer) {\n        this.observer.disconnect()\n        this.observer = null\n      }\n\n      this.$nextTick(() => {\n        const target = this.$refs.loadMoreTrigger\n        if (!target) return\n\n        this.observer = new IntersectionObserver((entries) => {\n          entries.forEach(entry => {\n            if (entry.isIntersecting && this.hasMore && !this.loadingMore) {\n              console.log('触发懒加载，hasMore:', this.hasMore, 'loadingMore:', this.loadingMore)\n              this.loadMore()\n            }\n          })\n        }, {\n          rootMargin: '100px' // 提前100px开始加载\n        })\n\n        this.observer.observe(target)\n        console.log('IntersectionObserver已重新设置')\n      })\n    },\n\n    // 查看详情\n    handleViewDetail(agent) {\n      console.log('查看智能体详情:', agent)\n      this.selectedAgent = agent\n      this.selectedAgentId = agent.id\n      this.detailModalVisible = true\n    },\n\n    // 关闭详情弹窗\n    handleCloseDetailModal() {\n      this.detailModalVisible = false\n      this.selectedAgent = null\n      this.selectedAgentId = ''\n    },\n\n    // 从弹窗发起购买\n    handlePurchaseFromModal(agent) {\n      console.log('从弹窗购买智能体:', agent)\n      // 购买逻辑已在AgentDetailModal中实现\n    },\n\n    // 购买成功回调\n    handlePurchaseSuccess(agentId) {\n      console.log('购买成功回调:', agentId)\n      this.onPurchaseSuccess(agentId)\n      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了')\n    },\n\n    // 加载已购买的智能体列表\n    async loadPurchasedAgents() {\n      try {\n        // 检查是否已登录\n        const token = this.$store.getters.token\n        if (!token) {\n          console.log('用户未登录，跳过购买状态检查')\n          return\n        }\n\n        // 检查缓存时效性（5分钟）\n        const cacheKey = 'purchasedAgents'\n        const cacheTimeKey = 'purchasedAgentsTime'\n        const cacheTime = localStorage.getItem(cacheTimeKey)\n        const now = Date.now()\n        const cacheExpiry = 5 * 60 * 1000 // 5分钟\n\n        // 从缓存中获取\n        const cached = localStorage.getItem(cacheKey)\n        if (cached && cacheTime && (now - parseInt(cacheTime)) < cacheExpiry) {\n          try {\n            this.purchasedAgents = JSON.parse(cached)\n            console.log('🔍 从缓存加载已购买智能体:', this.purchasedAgents.length, '个')\n            return // 缓存有效，直接返回\n          } catch (e) {\n            console.warn('购买状态缓存解析失败:', e)\n            localStorage.removeItem(cacheKey)\n            localStorage.removeItem(cacheTimeKey)\n          }\n        }\n\n        // 缓存过期或不存在，从后端获取\n        // TODO: 调用后端API获取最新的购买状态\n        // const response = await this.$http.get('/api/agent/purchase/list')\n        // if (response.data && response.data.success) {\n        //   this.purchasedAgents = response.data.result || []\n        //   localStorage.setItem(cacheKey, JSON.stringify(this.purchasedAgents))\n        //   localStorage.setItem(cacheTimeKey, now.toString())\n        //   console.log('✅ 购买状态更新成功:', this.purchasedAgents.length, '个')\n        // }\n      } catch (error) {\n        console.error('❌ 加载购买状态失败:', error)\n      }\n    },\n\n    // 检查智能体是否已购买\n    isAgentPurchased(agentId) {\n      return this.purchasedAgents.includes(agentId)\n    },\n\n    // 购买成功后更新状态\n    onPurchaseSuccess(agentId) {\n      if (!this.purchasedAgents.includes(agentId)) {\n        this.purchasedAgents.push(agentId)\n        localStorage.setItem('purchasedAgents', JSON.stringify(this.purchasedAgents))\n        console.log('✅ 购买状态已更新:', agentId)\n\n        // 更新智能体列表中的购买状态\n        this.agentList.forEach(agent => {\n          if (agent.id === agentId) {\n            agent.isPurchased = true\n          }\n        })\n      }\n    },\n\n    // 刷新数据\n    handleRefresh() {\n      this.loadAgentList()\n    },\n\n    // 获取创作者数量\n    getCreatorCount() {\n      return this.agentList.filter(agent => agent.authorType === '2').length\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.searchQuery = ''\n      this.handleSearch()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.agent-market {\n  padding: 0;\n}\n\n/* 固定的搜索和筛选区域 */\n.sticky-filters {\n  position: sticky;\n  top: 156px; /* 顶部导航栏100px + tab栏60px */\n  z-index: 100;\n  background: white;\n  padding: 1rem 0;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.market-filters {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  padding: 2rem;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n  max-width: 1600px;\n  margin: 0 auto;\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 600px;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  border: 2px solid #cbd5e1;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.search-wrapper:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.search-wrapper:focus-within {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n  transform: translateY(-1px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 16px;\n  z-index: 2;\n  color: #64748b;\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.search-wrapper:focus-within .search-icon {\n  color: #3b82f6;\n}\n\n.clear-icon {\n  position: absolute;\n  right: 16px;\n  z-index: 2;\n  color: #94a3b8;\n  font-size: 16px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.clear-icon:hover {\n  color: #64748b;\n}\n\n.search-input {\n  flex: 1;\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n  font-size: 16px;\n  height: 48px;\n  background: transparent;\n}\n\n.search-input:focus {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.filter-controls {\n  display: flex;\n  gap: 1.5rem;\n  align-items: center;\n}\n\n.filter-item-inline {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  white-space: nowrap;\n}\n\n.filter-select {\n  min-width: 160px;\n}\n\n.filter-select .ant-select-selector {\n  border-radius: 12px !important;\n  border: 2px solid #cbd5e1 !important;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n  background: white !important;\n  height: 48px !important;\n}\n\n.filter-select .ant-select-selection-item {\n  line-height: 44px !important;\n  font-weight: 500 !important;\n  color: #374151 !important;\n}\n\n.filter-select:hover .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select.ant-select-focused .ant-select-selector,\n.filter-select.ant-select-open .ant-select-selector {\n  border-color: #3b82f6 !important;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3) !important;\n  transform: translateY(-1px) !important;\n}\n\n.filter-select .ant-select-arrow {\n  color: #64748b !important;\n  font-size: 16px !important;\n  transition: all 0.3s ease !important;\n}\n\n.filter-select:hover .ant-select-arrow,\n.filter-select.ant-select-focused .ant-select-arrow,\n.filter-select.ant-select-open .ant-select-arrow {\n  color: #3b82f6 !important;\n  transform: scale(1.1) !important;\n}\n\n/* 下拉菜单样式 */\n.filter-select .ant-select-dropdown {\n  border-radius: 12px !important;\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12) !important;\n  border: 1px solid #e2e8f0 !important;\n  overflow: hidden !important;\n}\n\n.filter-select .ant-select-item {\n  padding: 12px 16px !important;\n  font-weight: 500 !important;\n  transition: all 0.2s ease !important;\n}\n\n.filter-select .ant-select-item:hover {\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;\n  color: #3b82f6 !important;\n}\n\n.filter-select .ant-select-item-option-selected {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;\n  color: white !important;\n  font-weight: 600 !important;\n}\n\n.filter-select .ant-select-item-option-selected:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;\n}\n\n/* 智能体列表 */\n.agent-list {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 2rem 2rem 1rem 2rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.list-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.list-count {\n  color: #64748b;\n  font-weight: 400;\n  font-size: 1rem;\n}\n\n/* 智能体网格 */\n.agent-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 2rem;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* 懒加载相关样式 */\n.load-more-wrapper {\n  padding: 2rem;\n  border-top: 1px solid #f1f5f9;\n  display: flex;\n  justify-content: center;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.load-more-trigger {\n  height: 20px;\n  width: 100%;\n}\n\n.no-more-data {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #94a3b8;\n  font-size: 0.875rem;\n}\n\n.no-more-data .anticon {\n  color: #10b981;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 400px;\n  background: transparent;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n\n  .filter-controls {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .agent-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .market-filters,\n  .list-header,\n  .load-more-wrapper {\n    padding: 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/workflow/components"}]}