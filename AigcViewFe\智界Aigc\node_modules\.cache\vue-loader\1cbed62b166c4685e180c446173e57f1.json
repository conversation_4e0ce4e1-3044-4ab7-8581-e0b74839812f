{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=template&id=f8301f64&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754040785821}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-modal\n  :visible=\"visible\"\n  :width=\"800\"\n  :footer=\"null\"\n  :closable=\"false\"\n  :maskClosable=\"true\"\n  @cancel=\"handleClose\"\n  class=\"agent-detail-modal\"\n  :bodyStyle=\"{ padding: 0 }\"\n>\n  <!-- 加载状态 -->\n  <div v-if=\"loading\" class=\"loading-container\">\n    <a-spin size=\"large\" tip=\"加载中...\">\n      <div class=\"loading-placeholder\"></div>\n    </a-spin>\n  </div>\n\n  <!-- 主要内容 -->\n  <div v-else class=\"modal-content\">\n    <!-- 1. 智能体基本信息区域 -->\n    <div class=\"agent-info-section\">\n      <div class=\"agent-header\">\n        <!-- 智能体头像 -->\n        <div class=\"agent-avatar\">\n          <img\n            v-if=\"agentDetail.agentAvatar\"\n            :src=\"agentDetail.agentAvatar\"\n            :alt=\"agentDetail.agentName\"\n            @error=\"handleImageError\"\n          />\n          <div v-else class=\"avatar-placeholder\">\n            <a-icon type=\"robot\" />\n          </div>\n        </div>\n\n        <!-- 智能体基本信息 -->\n        <div class=\"agent-basic-info\">\n          <h2 class=\"agent-name\">{{ agentDetail.agentName }}</h2>\n          <p class=\"agent-description\">{{ agentDetail.agentDescription }}</p>\n          \n          <!-- 创作者信息 -->\n          <div class=\"creator-info\">\n            <div class=\"creator-avatar\">\n              <img\n                v-if=\"agentDetail.creatorInfo && agentDetail.creatorInfo.avatar\"\n                :src=\"agentDetail.creatorInfo.avatar\"\n                :alt=\"agentDetail.creatorInfo.name\"\n                @error=\"handleCreatorAvatarError\"\n              />\n              <a-icon v-else type=\"user\" />\n            </div>\n            <div class=\"creator-details\">\n              <span class=\"creator-name\">{{ creatorName }}</span>\n              <span class=\"creator-type\">{{ authorTypeText }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 价格信息 -->\n        <div class=\"price-section\">\n          <div v-if=\"agentDetail.isFree\" class=\"price-container\">\n            <span class=\"free-price\">免费</span>\n          </div>\n          <div v-else-if=\"agentDetail.showDiscountPrice\" class=\"price-container\">\n            <span class=\"discount-price\">¥{{ finalPrice }}</span>\n            <span class=\"original-price\">¥{{ agentDetail.originalPrice || 0 }}</span>\n          </div>\n          <div v-else class=\"price-container\">\n            <span class=\"current-price\">¥{{ agentDetail.originalPrice || 0 }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 2. 演示视频区域 -->\n    <div v-if=\"agentDetail.demoVideo\" class=\"demo-video-section\">\n      <h3 class=\"section-title\">演示视频</h3>\n      <div class=\"video-container\">\n        <video\n          :src=\"agentDetail.demoVideo\"\n          controls\n          preload=\"metadata\"\n          class=\"demo-video\"\n        >\n          您的浏览器不支持视频播放\n        </video>\n      </div>\n    </div>\n\n    <!-- 3. 工作流列表区域 -->\n    <div class=\"workflow-section\">\n      <h3 class=\"section-title\">\n        工作流列表\n        <span class=\"workflow-count\">({{ workflowList.length }}个)</span>\n      </h3>\n      \n      <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n        <a-spin tip=\"加载工作流中...\" />\n      </div>\n      \n      <div v-else-if=\"workflowList.length > 0\" class=\"workflow-list\">\n        <div\n          v-for=\"(workflow, index) in workflowList\"\n          :key=\"workflow.id\"\n          class=\"workflow-item\"\n        >\n          <div class=\"workflow-info\">\n            <div class=\"workflow-sequence\">{{ index + 1 }}</div>\n            <div class=\"workflow-avatar\">\n              <img\n                v-if=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                :src=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                :alt=\"workflow.workflowName\"\n                @error=\"handleWorkflowImageError\"\n              />\n              <a-icon v-else type=\"setting\" />\n            </div>\n            <div class=\"workflow-details\">\n              <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n              <p class=\"workflow-description\">{{ workflow.workflowDescription }}</p>\n            </div>\n          </div>\n          \n          <!-- 下载按钮 -->\n          <div class=\"workflow-actions\">\n            <a-button\n              v-if=\"!isPurchased\"\n              type=\"default\"\n              disabled\n              @click=\"handleDownloadTip\"\n            >\n              <a-icon type=\"download\" />\n              请先购买\n            </a-button>\n            <a-button\n              v-else\n              type=\"primary\"\n              @click=\"handleWorkflowDownload(workflow)\"\n              :loading=\"downloadLoading[workflow.id]\"\n            >\n              <a-icon type=\"download\" />\n              下载\n            </a-button>\n          </div>\n        </div>\n      </div>\n      \n      <div v-else class=\"workflow-empty\">\n        <a-empty description=\"暂无工作流\" />\n      </div>\n    </div>\n\n    <!-- 4. 底部操作按钮区域 -->\n    <div class=\"action-buttons\">\n      <a-button @click=\"handleClose\" class=\"close-btn\">\n        关闭\n      </a-button>\n      \n      <a-button\n        v-if=\"isPurchased\"\n        type=\"default\"\n        @click=\"handleViewDetail\"\n        class=\"detail-btn\"\n      >\n        查看详情\n      </a-button>\n      <a-button\n        v-else\n        type=\"default\"\n        disabled\n        class=\"detail-btn disabled\"\n      >\n        查看详情\n      </a-button>\n      \n      <a-button\n        v-if=\"!isPurchased\"\n        type=\"primary\"\n        @click=\"handlePurchase\"\n        :loading=\"purchaseLoading\"\n        class=\"purchase-btn\"\n      >\n        立即购买\n      </a-button>\n      \n      <a-button\n        v-if=\"agentDetail.experienceLink\"\n        type=\"default\"\n        @click=\"handleExperience\"\n        class=\"experience-btn\"\n      >\n        体验智能体\n      </a-button>\n      <a-button\n        v-else\n        type=\"default\"\n        disabled\n        class=\"experience-btn disabled\"\n      >\n        暂无体验\n      </a-button>\n    </div>\n  </div>\n</a-modal>\n", null]}