<template>
  <a-modal
    :visible="visible"
    :width="1200"
    :footer="null"
    :closable="false"
    :maskClosable="true"
    @cancel="handleClose"
    class="agent-detail-modal custom-modal"
    :bodyStyle="{ padding: 0, borderRadius: '16px', overflow: 'hidden' }"
    :centered="true"
    :destroyOnClose="true"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 主要内容 -->
    <div v-else class="modal-content">
      <!-- 自定义关闭按钮 -->
      <div class="custom-close-button" @click="handleClose">
        <a-icon type="close" />
      </div>

      <!-- 背景装饰 -->
      <div class="modal-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
      </div>
      <!-- 1. 智能体基本信息区域 -->
      <div class="agent-info-section">
        <div class="agent-header">
          <!-- 智能体头像 -->
          <div class="agent-avatar">
            <img
              v-if="agentDetail.agentAvatar"
              :src="agentDetail.agentAvatar"
              :alt="agentDetail.agentName"
              @error="handleImageError"
            />
            <div v-else class="avatar-placeholder">
              <a-icon type="robot" />
            </div>
          </div>

          <!-- 智能体基本信息 -->
          <div class="agent-basic-info">
            <h2 class="agent-name">{{ agentDetail.agentName }}</h2>
            <p class="agent-description">{{ agentDetail.agentDescription }}</p>
            
            <!-- 创作者信息 -->
            <div class="creator-info">
              <div class="creator-avatar">
                <img
                  v-if="agentDetail.creatorInfo && agentDetail.creatorInfo.avatar"
                  :src="agentDetail.creatorInfo.avatar"
                  :alt="agentDetail.creatorInfo.name"
                  @error="handleCreatorAvatarError"
                />
                <a-icon v-else type="user" />
              </div>
              <div class="creator-details">
                <span class="creator-name">{{ creatorName }}</span>
                <span class="creator-type">{{ authorTypeText }}</span>
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="price-section">
            <div v-if="agentDetail.isFree" class="price-container">
              <span class="free-price">免费</span>
            </div>
            <div v-else-if="agentDetail.showDiscountPrice" class="price-container">
              <span class="discount-price">¥{{ finalPrice }}</span>
              <span class="original-price">¥{{ agentDetail.price || agentDetail.originalPrice || 0 }}</span>
            </div>
            <div v-else class="price-container">
              <span class="current-price">¥{{ agentDetail.price || agentDetail.originalPrice || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. 演示视频区域 -->
      <div v-if="agentDetail.demoVideo" class="demo-video-section">
        <h3 class="section-title">
          <a-icon type="play-circle" />
          演示视频
        </h3>
        <div class="video-container">
          <div class="video-wrapper" v-if="!videoError">
            <video
              ref="demoVideo"
              :src="agentDetail.demoVideo"
              class="demo-video"
              :muted="videoMuted"
              :autoplay="videoAutoplay"
              preload="metadata"
              @loadstart="handleVideoLoadStart"
              @loadeddata="handleVideoLoaded"
              @error="handleVideoError"
              @play="handleVideoPlay"
              @pause="handleVideoPause"
              @timeupdate="handleVideoTimeUpdate"
              @volumechange="handleVolumeChange"
              @click="toggleVideoPlay"
            >
              您的浏览器不支持视频播放
            </video>

            <!-- 自定义视频控制栏 -->
            <div class="video-controls" v-show="showControls">
              <div class="controls-left">
                <!-- 播放/暂停按钮 -->
                <a-button
                  type="link"
                  :icon="videoPlaying ? 'pause' : 'caret-right'"
                  @click="toggleVideoPlay"
                  class="control-btn play-btn"
                />

                <!-- 时间显示 -->
                <span class="time-display">
                  {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
                </span>
              </div>

              <div class="controls-center">
                <!-- 进度条 -->
                <div class="progress-container" @click="handleProgressClick">
                  <div class="progress-bar">
                    <div
                      class="progress-filled"
                      :style="{ width: progressPercent + '%' }"
                    ></div>
                    <div
                      class="progress-thumb"
                      :style="{ left: progressPercent + '%' }"
                    ></div>
                  </div>
                </div>
              </div>

              <div class="controls-right">
                <!-- 音量控制 -->
                <div class="volume-control" @mouseenter="showVolumeSlider = true" @mouseleave="showVolumeSlider = false">
                  <a-button
                    type="link"
                    :icon="videoMuted ? 'sound' : 'sound-filled'"
                    @click="toggleMute"
                    class="control-btn volume-btn"
                  />
                  <div class="volume-slider" v-show="showVolumeSlider">
                    <a-slider
                      v-model="videoVolume"
                      :min="0"
                      :max="100"
                      :step="1"
                      vertical
                      :tip-formatter="null"
                      @change="handleVolumeSliderChange"
                      class="volume-range"
                    />
                  </div>
                </div>

                <!-- 全屏按钮 -->
                <a-button
                  type="link"
                  icon="fullscreen"
                  @click="toggleFullscreen"
                  class="control-btn fullscreen-btn"
                />
              </div>
            </div>

            <!-- 视频加载状态 -->
            <div class="video-loading" v-show="videoLoading">
              <a-spin size="large">
                <a-icon slot="indicator" type="loading" style="font-size: 24px;" spin />
              </a-spin>
              <p>视频加载中...</p>
            </div>
          </div>

          <!-- 视频加载失败占位符 -->
          <div class="video-error-placeholder" v-if="videoError">
            <div class="error-content">
              <a-icon type="exclamation-circle" class="error-icon" />
              <h4>视频加载失败</h4>
              <p>抱歉，演示视频暂时无法播放</p>
              <a-button @click="retryVideoLoad" type="primary" ghost>
                <a-icon type="reload" />
                重新加载
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 3. 工作流列表区域 -->
      <div class="workflow-section">
        <h3 class="section-title">
          工作流列表
          <span class="workflow-count">({{ workflowList.length }}个)</span>
        </h3>
        
        <div v-if="workflowLoading" class="workflow-loading">
          <a-spin tip="加载工作流中..." />
        </div>
        
        <div v-else-if="workflowList.length > 0" class="workflow-list">
          <div
            v-for="(workflow, index) in workflowList"
            :key="workflow.id"
            class="workflow-item"
          >
            <div class="workflow-info">
              <div class="workflow-sequence">{{ index + 1 }}</div>
              <div class="workflow-avatar">
                <img
                  v-if="workflow.agentAvatar || agentDetail.agentAvatar"
                  :src="workflow.agentAvatar || agentDetail.agentAvatar"
                  :alt="workflow.workflowName"
                  @error="handleWorkflowImageError"
                />
                <a-icon v-else type="setting" />
              </div>
              <div class="workflow-details">
                <h4 class="workflow-name">{{ workflow.workflowName }}</h4>
                <p class="workflow-description">{{ workflow.workflowDescription }}</p>
              </div>
            </div>
            
            <!-- 下载按钮 -->
            <div class="workflow-actions">
              <a-button
                v-if="!isPurchased"
                type="default"
                disabled
                @click="handleDownloadTip"
              >
                <a-icon type="download" />
                请先购买
              </a-button>
              <a-button
                v-else
                type="primary"
                @click="handleWorkflowDownload(workflow)"
                :loading="downloadLoading[workflow.id]"
              >
                <a-icon type="download" />
                下载
              </a-button>
            </div>
          </div>
        </div>
        
        <div v-else class="workflow-empty">
          <a-empty description="暂无工作流" />
        </div>
      </div>

      <!-- 4. 底部操作按钮区域 -->
      <div class="action-buttons modern-actions">
        <a-button @click="handleClose" class="close-btn modern-btn-secondary">
          <a-icon type="close" />
          关闭
        </a-button>

        <div class="primary-actions">
          <a-button
            v-if="isPurchased"
            type="default"
            @click="handleViewDetail"
            class="detail-btn modern-btn-outline"
          >
            <a-icon type="eye" />
            查看详情
          </a-button>
          <a-button
            v-else
            type="default"
            disabled
            class="detail-btn modern-btn-outline disabled"
          >
            <a-icon type="eye" />
            查看详情
          </a-button>

          <a-button
            v-if="!isPurchased"
            type="primary"
            @click="handlePurchase"
            :loading="purchaseLoading"
            class="purchase-btn modern-btn-primary"
          >
            <a-icon type="shopping-cart" />
            立即购买
          </a-button>

          <a-button
            v-if="agentDetail.experienceLink"
            type="default"
            @click="handleExperience"
            class="experience-btn modern-btn-outline"
          >
            <a-icon type="play-circle" />
            体验智能体
          </a-button>
          <a-button
            v-else
            type="default"
            disabled
            class="experience-btn modern-btn-outline disabled"
          >
            <a-icon type="play-circle" />
            暂无体验
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'AgentDetailModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    agentId: {
      type: String,
      default: ''
    },
    isPurchased: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      workflowLoading: false,
      purchaseLoading: false,
      downloadLoading: {}, // 工作流下载loading状态
      agentDetail: {}, // 智能体详情
      workflowList: [], // 工作流列表

      // 视频播放相关状态
      videoLoading: false,
      videoError: false,
      videoPlaying: false,
      videoMuted: true, // 默认静音以支持自动播放
      videoAutoplay: true,
      currentTime: 0,
      duration: 0,
      videoVolume: 50,
      showControls: true,
      showVolumeSlider: false,
      progressPercent: 0
    }
  },
  computed: {
    // 创作者名称
    creatorName() {
      if (this.agentDetail.creatorInfo) {
        // 优先显示昵称，其次显示姓名
        return this.agentDetail.creatorInfo.nickname || this.agentDetail.creatorInfo.name || '未知创作者'
      }
      return '未知创作者'
    },

    // 作者类型文本
    authorTypeText() {
      if (this.agentDetail.authorType === '1') {
        return '官方'
      } else if (this.agentDetail.authorType === '2') {
        return '创作者'
      }
      return '未知'
    },

    // 最终价格（考虑折扣）
    finalPrice() {
      if (!this.agentDetail) return 0
      if (this.agentDetail.isFree) return 0

      // 安全地获取价格，确保返回数字
      const originalPrice = parseFloat(this.agentDetail.price || this.agentDetail.originalPrice) || 0
      const discountPrice = parseFloat(this.agentDetail.discountPrice) || originalPrice

      return this.agentDetail.showDiscountPrice ? discountPrice : originalPrice
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.agentId) {
        this.loadAgentDetail()
        this.loadWorkflowList()
      } else {
        this.resetData()
      }
    }
  },
  methods: {
    // 加载智能体详情
    async loadAgentDetail() {
      this.loading = true
      try {
        // TODO: 调用后端API获取智能体详情
        // const response = await this.$http.get(`/api/agent/detail/${this.agentId}`)

        // 临时模拟数据 - 从父组件传入的agent数据中获取基本信息
        await new Promise(resolve => setTimeout(resolve, 500))

        // 如果有传入的agent数据，使用它作为基础
        const baseAgent = this.$parent.selectedAgent || {}

        this.agentDetail = {
          id: this.agentId,
          agentName: baseAgent.agentName || '示例智能体',
          agentDescription: baseAgent.agentDescription || baseAgent.description || '这是一个示例智能体的详细描述信息',
          agentAvatar: baseAgent.agentAvatar || '',
          demoVideo: baseAgent.demoVideo || '',
          experienceLink: baseAgent.experienceLink || 'https://example.com',
          price: baseAgent.price || 99,
          originalPrice: baseAgent.originalPrice || baseAgent.price || 99,
          authorType: baseAgent.authorType || '1',
          // 继承价格计算结果
          showSvipPromo: baseAgent.showSvipPromo || false,
          showDiscountPrice: baseAgent.showDiscountPrice || false,
          discountRate: baseAgent.discountRate || 1,
          isFree: baseAgent.isFree || false,
          creatorInfo: {
            name: '官方团队',
            nickname: '智界Aigc',
            avatar: ''
          }
        }

        console.log('🔍 AgentDetailModal: 加载智能体详情', this.agentDetail)
      } catch (error) {
        console.error('加载智能体详情失败:', error)
        this.$message.error('加载智能体详情失败')
        // 设置默认数据防止页面崩溃
        this.agentDetail = {
          id: this.agentId,
          agentName: '加载失败',
          agentDescription: '智能体详情加载失败',
          agentAvatar: '',
          demoVideo: '',
          experienceLink: '',
          price: 0,
          originalPrice: 0,
          authorType: '2',
          showSvipPromo: false,
          showDiscountPrice: false,
          discountRate: 0,
          isFree: false,
          creatorInfo: {
            name: '未知',
            nickname: '未知',
            avatar: ''
          }
        }
      } finally {
        this.loading = false
      }
    },

    // 加载工作流列表
    async loadWorkflowList() {
      this.workflowLoading = true
      try {
        // TODO: 调用后端API获取工作流列表
        // const response = await this.$http.get(`/api/agent/${this.agentId}/workflows`)

        // 临时模拟数据 - 根据智能体类型生成不同的工作流
        await new Promise(resolve => setTimeout(resolve, 300))

        const baseAgent = this.$parent.selectedAgent || {}
        const agentName = baseAgent.agentName || '智能体'

        // 根据智能体类型生成相应的工作流
        if (baseAgent.authorType === '1') {
          // 官方智能体 - 更多工作流
          this.workflowList = [
            {
              id: '1',
              workflowName: `${agentName} - 基础对话流程`,
              workflowDescription: '适用于日常对话和基础问答的工作流程',
              agentAvatar: baseAgent.agentAvatar || '',
              sequence: 1
            },
            {
              id: '2',
              workflowName: `${agentName} - 高级分析流程`,
              workflowDescription: '用于复杂数据分析和深度思考的高级工作流',
              agentAvatar: baseAgent.agentAvatar || '',
              sequence: 2
            },
            {
              id: '3',
              workflowName: `${agentName} - 创意生成流程`,
              workflowDescription: '专门用于创意内容生成和头脑风暴的工作流',
              agentAvatar: baseAgent.agentAvatar || '',
              sequence: 3
            }
          ]
        } else {
          // 创作者智能体 - 较少工作流
          this.workflowList = [
            {
              id: '1',
              workflowName: `${agentName} - 核心功能流程`,
              workflowDescription: '智能体的核心功能和主要应用场景',
              agentAvatar: baseAgent.agentAvatar || '',
              sequence: 1
            },
            {
              id: '2',
              workflowName: `${agentName} - 扩展应用流程`,
              workflowDescription: '扩展功能和特殊应用场景的工作流程',
              agentAvatar: baseAgent.agentAvatar || '',
              sequence: 2
            }
          ]
        }

        console.log('🔍 AgentDetailModal: 加载工作流列表', this.workflowList.length, '个')
      } catch (error) {
        console.error('加载工作流列表失败:', error)
        this.$message.error('加载工作流列表失败')
      } finally {
        this.workflowLoading = false
      }
    },

    // 重置数据
    resetData() {
      this.agentDetail = {}
      this.workflowList = []
      this.downloadLoading = {}

      // 重置视频状态
      this.videoLoading = false
      this.videoError = false
      this.videoPlaying = false
      this.currentTime = 0
      this.duration = 0
      this.progressPercent = 0
      this.showVolumeSlider = false

      // 停止视频播放
      if (this.$refs.demoVideo) {
        this.$refs.demoVideo.pause()
        this.$refs.demoVideo.currentTime = 0
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 购买操作
    handlePurchase() {
      this.showPurchaseConfirm()
    },

    // 显示购买确认弹窗
    showPurchaseConfirm() {
      const agent = this.agentDetail
      const finalPrice = this.finalPrice

      this.$confirm({
        title: '确认购买智能体',
        content: () => (
          <div style="padding: 16px 0;">
            <div style="display: flex; align-items: center; margin-bottom: 16px;">
              <img
                src={agent.agentAvatar || ''}
                alt={agent.agentName}
                style="width: 48px; height: 48px; border-radius: 8px; margin-right: 12px; object-fit: cover;"
                onError={(e) => { e.target.style.display = 'none' }}
              />
              <div>
                <div style="font-weight: 600; font-size: 16px; color: #1a202c;">{agent.agentName}</div>
                <div style="font-size: 12px; color: #718096; margin-top: 4px;">
                  {agent.authorType === '1' ? '官方智能体' : '创作者智能体'}
                </div>
              </div>
            </div>

            <div style="background: #f7fafc; padding: 12px; border-radius: 8px; margin-bottom: 16px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="color: #4a5568;">购买价格：</span>
                <div>
                  {agent.isFree ? (
                    <span style="font-size: 18px; font-weight: 600; color: #38a169;">免费</span>
                  ) : agent.showDiscountPrice ? (
                    <div>
                      <span style="font-size: 18px; font-weight: 600; color: #e53e3e;">¥{finalPrice}</span>
                      <span style="font-size: 14px; color: #a0aec0; text-decoration: line-through; margin-left: 8px;">
                        ¥{agent.price || agent.originalPrice || 0}
                      </span>
                    </div>
                  ) : (
                    <span style="font-size: 18px; font-weight: 600; color: #2d3748;">¥{agent.price || agent.originalPrice || 0}</span>
                  )}
                </div>
              </div>
            </div>

            <div style="font-size: 14px; color: #718096; line-height: 1.5;">
              购买后您将获得：
              <ul style="margin: 8px 0 0 16px; padding: 0;">
                <li>永久使用权限</li>
                <li>所有工作流下载权限</li>
                <li>无使用次数限制</li>
                <li>后续功能更新</li>
              </ul>
            </div>
          </div>
        ),
        okText: '确认购买',
        cancelText: '取消',
        okType: 'primary',
        width: 480,
        onOk: () => {
          this.processPurchase()
        }
      })
    },

    // 处理购买流程
    async processPurchase() {
      this.purchaseLoading = true
      try {
        // TODO: 检查用户余额
        // const balanceResponse = await this.$http.get('/api/user/balance')

        // TODO: 调用购买API
        // const purchaseData = {
        //   agentId: this.agentDetail.id,
        //   agentName: this.agentDetail.agentName,
        //   purchasePrice: this.finalPrice,
        //   originalPrice: this.agentDetail.price || this.agentDetail.originalPrice,
        //   discountRate: this.agentDetail.discountRate || 1
        // }
        // const response = await this.$http.post('/api/agent/purchase', purchaseData)

        // 模拟购买过程
        await new Promise(resolve => setTimeout(resolve, 1500))

        this.$message.success('购买成功！')

        // 通知父组件更新购买状态
        this.$emit('purchase-success', this.agentDetail.id)

        // 关闭弹窗
        this.handleClose()

      } catch (error) {
        console.error('购买失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('购买失败，请稍后重试')
        }
      } finally {
        this.purchaseLoading = false
      }
    },

    // 查看详情
    handleViewDetail() {
      if (!this.isPurchased) {
        this.$message.warning('请先购买该智能体后查看详情')
        return
      }

      // TODO: 跳转到智能体详情页面
      // 这里应该跳转到一个更详细的智能体介绍页面
      this.$router.push({
        path: '/agent/detail',
        query: {
          agentId: this.agentDetail.id,
          agentName: this.agentDetail.agentName
        }
      })

      console.log('🔗 跳转到智能体详情页面')
      this.$message.success('正在跳转到详情页面...')
    },

    // 体验智能体
    handleExperience() {
      if (!this.agentDetail.experienceLink) {
        this.$message.warning('该智能体暂未提供体验链接')
        return
      }

      try {
        // 在新窗口打开体验链接
        window.open(this.agentDetail.experienceLink, '_blank')
        console.log('🔗 打开体验链接:', this.agentDetail.experienceLink)
        this.$message.success('正在打开体验页面...')
      } catch (error) {
        console.error('打开体验链接失败:', error)
        this.$message.error('打开体验页面失败')
      }
    },

    // 工作流下载
    async handleWorkflowDownload(workflow) {
      this.$set(this.downloadLoading, workflow.id, true)
      try {
        // TODO: 跳转到下载页面
        this.$message.success('正在跳转到下载页面...')
      } catch (error) {
        this.$message.error('下载失败')
      } finally {
        this.$set(this.downloadLoading, workflow.id, false)
      }
    },

    // 下载提示
    handleDownloadTip() {
      this.$message.warning('请先购买智能体后再下载工作流')
    },

    // 处理工作流下载
    async handleWorkflowDownload(workflow) {
      console.log('下载工作流:', workflow)

      // 检查购买状态
      if (!this.isPurchased) {
        this.$message.warning('请先购买该智能体后再下载工作流')
        return
      }

      // 设置下载状态
      this.$set(this.downloadLoading, workflow.id, true)

      try {
        // 已购买用户 - 跳转到下载页面
        await this.navigateToDownloadPage(workflow)
      } catch (error) {
        console.error('下载工作流失败:', error)
        this.$message.error('下载失败，请稍后重试')
      } finally {
        this.$set(this.downloadLoading, workflow.id, false)
      }
    },

    // 跳转到工作流下载页面
    async navigateToDownloadPage(workflow) {
      try {
        // TODO: 实现跳转到工作流下载页面的逻辑
        // 这里应该跳转到一个专门的下载页面，而不是直接下载文件

        // 模拟跳转延迟
        await new Promise(resolve => setTimeout(resolve, 800))

        const downloadUrl = `/workflow/download/${workflow.id}?agentId=${this.agentDetail.id}`

        // 使用路由跳转
        this.$router.push({
          path: '/workflow/download',
          query: {
            workflowId: workflow.id,
            agentId: this.agentDetail.id,
            workflowName: workflow.workflowName
          }
        })

        console.log('🔗 跳转到工作流下载页面:', downloadUrl)
        this.$message.success('正在跳转到下载页面...')

      } catch (error) {
        console.error('跳转下载页面失败:', error)
        this.$message.error('跳转失败，请稍后重试')
      }
    },

    // ========== 视频播放控制方法 ==========

    // 视频开始加载
    handleVideoLoadStart() {
      console.log('🎬 视频开始加载')
      this.videoLoading = true
      this.videoError = false
    },

    // 视频加载完成
    handleVideoLoaded() {
      console.log('🎬 视频加载完成')
      this.videoLoading = false
      this.videoError = false

      const video = this.$refs.demoVideo
      if (video) {
        this.duration = video.duration || 0
        this.videoVolume = Math.round(video.volume * 100)

        // 尝试自动播放（静音状态下）
        if (this.videoAutoplay && this.videoMuted) {
          this.playVideo()
        }
      }
    },

    // 视频加载错误
    handleVideoError(event) {
      console.error('🎬 视频加载失败:', event)
      this.videoLoading = false
      this.videoError = true
      this.videoPlaying = false
      this.$message.error('视频加载失败')
    },

    // 视频播放事件
    handleVideoPlay() {
      console.log('🎬 视频开始播放')
      this.videoPlaying = true
    },

    // 视频暂停事件
    handleVideoPause() {
      console.log('🎬 视频暂停播放')
      this.videoPlaying = false
    },

    // 视频时间更新
    handleVideoTimeUpdate() {
      const video = this.$refs.demoVideo
      if (video && video.duration) {
        this.currentTime = video.currentTime
        this.progressPercent = (video.currentTime / video.duration) * 100
      }
    },

    // 音量变化事件
    handleVolumeChange() {
      const video = this.$refs.demoVideo
      if (video) {
        this.videoVolume = Math.round(video.volume * 100)
        this.videoMuted = video.muted
      }
    },

    // 切换播放/暂停
    toggleVideoPlay() {
      const video = this.$refs.demoVideo
      if (!video) return

      if (this.videoPlaying) {
        video.pause()
      } else {
        this.playVideo()
      }
    },

    // 播放视频
    async playVideo() {
      const video = this.$refs.demoVideo
      if (!video) return

      try {
        await video.play()
        console.log('🎬 视频播放成功')
      } catch (error) {
        console.error('🎬 视频播放失败:', error)
        // 如果自动播放失败，可能是因为浏览器策略，尝试静音播放
        if (!this.videoMuted) {
          this.videoMuted = true
          video.muted = true
          try {
            await video.play()
            this.$message.info('视频已静音播放，点击音量按钮开启声音')
          } catch (retryError) {
            console.error('🎬 静音播放也失败:', retryError)
            this.$message.warning('视频播放失败，请手动点击播放')
          }
        }
      }
    },

    // 切换静音
    toggleMute() {
      const video = this.$refs.demoVideo
      if (!video) return

      this.videoMuted = !this.videoMuted
      video.muted = this.videoMuted

      console.log('🎬 切换静音状态:', this.videoMuted ? '静音' : '有声')
    },

    // 处理进度条点击
    handleProgressClick(event) {
      const video = this.$refs.demoVideo
      if (!video || !video.duration) return

      const progressContainer = event.currentTarget
      const rect = progressContainer.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const progressWidth = rect.width
      const clickPercent = clickX / progressWidth

      const newTime = clickPercent * video.duration
      video.currentTime = newTime

      console.log('🎬 跳转到时间:', this.formatTime(newTime))
    },

    // 音量滑块变化
    handleVolumeSliderChange(value) {
      const video = this.$refs.demoVideo
      if (!video) return

      this.videoVolume = value
      video.volume = value / 100

      // 如果音量大于0，自动取消静音
      if (value > 0 && this.videoMuted) {
        this.videoMuted = false
        video.muted = false
      }

      console.log('🎬 调整音量:', value + '%')
    },

    // 切换全屏
    toggleFullscreen() {
      const video = this.$refs.demoVideo
      if (!video) return

      if (video.requestFullscreen) {
        video.requestFullscreen()
      } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen()
      } else if (video.mozRequestFullScreen) {
        video.mozRequestFullScreen()
      } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen()
      }

      console.log('🎬 切换全屏模式')
    },

    // 重新加载视频
    retryVideoLoad() {
      console.log('🎬 重新加载视频')
      this.videoError = false
      this.videoLoading = true

      const video = this.$refs.demoVideo
      if (video) {
        video.load()
      }
    },

    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 图片加载错误处理
    handleImageError(event) {
      event.target.style.display = 'none'
    },

    // 创作者头像加载错误处理
    handleCreatorAvatarError(event) {
      event.target.style.display = 'none'
    },

    // 工作流图片加载错误处理
    handleWorkflowImageError(event) {
      event.target.style.display = 'none'
    }
  }
}
</script>

<style scoped>
/* 弹窗样式 */
.agent-detail-modal {
  top: 20px;
}

.agent-detail-modal .ant-modal-body {
  max-height: 80vh;
  overflow-y: auto;
  padding: 0;
}

/* 自定义滚动条 */
.agent-detail-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 弹窗动画优化 */
.agent-detail-modal .ant-modal {
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 加载状态 */
.loading-container {
  padding: 60px 0;
  text-align: center;
}

.loading-placeholder {
  height: 200px;
}

/* 主要内容 */
.modal-content {
  padding: 0;
}

/* 1. 智能体基本信息区域 */
.agent-info-section {
  padding: 32px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.agent-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.agent-avatar {
  width: 96px;
  height: 96px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  border: 3px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  font-size: 32px;
}

.agent-basic-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.agent-description {
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creator-avatar .anticon {
  font-size: 12px;
  color: #999;
}

.creator-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.creator-name {
  font-size: 15px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.creator-type {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  display: inline-block;
  margin-top: 4px;
}

.price-section {
  flex-shrink: 0;
  text-align: right;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.free-price {
  font-size: 20px;
  font-weight: 600;
  color: #38a169;
}

.discount-price {
  font-size: 20px;
  font-weight: 600;
  color: #e53e3e;
}

.original-price {
  font-size: 14px;
  color: #a0aec0;
  text-decoration: line-through;
}

.current-price {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

/* 2. 演示视频区域 */
.demo-video-section {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .anticon {
  color: #4299e1;
}

.video-container {
  border-radius: 12px;
  overflow: hidden;
  background: #000;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
}

.demo-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.demo-video:hover {
  transform: scale(1.02);
}

/* 视频控制栏 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: opacity 0.3s ease;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.controls-center {
  flex: 1;
  margin: 0 16px;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  color: white !important;
  border: none !important;
  background: transparent !important;
  padding: 4px 8px !important;
  height: auto !important;
  font-size: 16px;
  transition: all 0.2s ease;
}

.control-btn:hover {
  color: #4299e1 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.time-display {
  color: white;
  font-size: 12px;
  font-family: monospace;
  min-width: 80px;
}

/* 进度条 */
.progress-container {
  cursor: pointer;
  padding: 8px 0;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progress-filled {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #3182ce);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: left 0.1s ease;
}

/* 音量控制 */
.volume-control {
  position: relative;
}

.volume-slider {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 12px 8px;
  border-radius: 6px;
  margin-bottom: 8px;
  height: 100px;
}

.volume-range {
  height: 80px;
}

.volume-range .ant-slider-rail {
  background: rgba(255, 255, 255, 0.3);
}

.volume-range .ant-slider-track {
  background: #4299e1;
}

.volume-range .ant-slider-handle {
  border-color: #4299e1;
}

/* 视频加载状态 */
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.video-loading p {
  margin-top: 16px;
  color: white;
  font-size: 14px;
}

/* 视频错误占位符 */
.video-error-placeholder {
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e0;
}

.error-content {
  text-align: center;
  color: #718096;
}

.error-icon {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 16px;
}

.error-content h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 16px 0;
  color: #718096;
  font-size: 14px;
}

/* 3. 工作流列表区域 */
.workflow-section {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.workflow-count {
  font-size: 14px;
  color: #718096;
  font-weight: normal;
}

.workflow-loading {
  padding: 40px 0;
  text-align: center;
}

.workflow-list {
  margin-top: 16px;
}

.workflow-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.2s ease;
}

.workflow-item:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.workflow-item:last-child {
  margin-bottom: 0;
}

.workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.workflow-sequence {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #4299e1;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.workflow-avatar {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.workflow-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.workflow-avatar .anticon {
  font-size: 16px;
  color: #a0aec0;
}

.workflow-details {
  flex: 1;
  min-width: 0;
}

.workflow-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.2;
}

.workflow-description {
  margin: 0;
  font-size: 12px;
  color: #718096;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.workflow-actions {
  flex-shrink: 0;
  margin-left: 16px;
}

.workflow-empty {
  padding: 40px 0;
  text-align: center;
}

/* 4. 底部操作按钮区域 */
.action-buttons {
  padding: 20px 24px;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e2e8f0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.action-buttons .ant-btn {
  min-width: 80px;
}

.close-btn {
  margin-right: auto;
}

.purchase-btn {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  border-color: #4299e1;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  transition: all 0.3s ease;
}

.purchase-btn:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  border-color: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-detail-modal {
    top: 10px;
    margin: 0 10px;
  }

  .agent-detail-modal .ant-modal {
    max-width: none;
    width: calc(100vw - 20px) !important;
  }

  .agent-header {
    flex-direction: column;
    gap: 16px;
  }

  .agent-avatar {
    align-self: center;
  }

  .price-section {
    text-align: center;
  }

  .workflow-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .workflow-actions {
    margin-left: 0;
    align-self: stretch;
  }

  .workflow-actions .ant-btn {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .close-btn {
    margin-right: 0;
    order: 1;
  }
}

/* ===== 现代化弹窗样式美化 ===== */

/* 自定义弹窗样式 */
.custom-modal :deep(.ant-modal) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.custom-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.custom-modal :deep(.ant-modal-body) {
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
}

/* 自定义关闭按钮 */
.custom-close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-close-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.custom-close-button .anticon {
  font-size: 18px;
  color: #64748b;
}

/* 背景装饰 */
.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.05;
}

.bg-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
}

/* 现代化按钮样式 */
.modern-actions {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(20px);
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.primary-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.modern-btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 8px 24px !important;
  height: 44px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 14px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4) !important;
  background: linear-gradient(135deg, #5b52f0 0%, #8b5cf6 100%) !important;
}

.modern-btn-outline {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 2px solid rgba(79, 70, 229, 0.2) !important;
  color: #4f46e5 !important;
  font-weight: 500 !important;
  padding: 8px 20px !important;
  height: 44px !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-outline:hover {
  background: rgba(79, 70, 229, 0.05) !important;
  border-color: rgba(79, 70, 229, 0.4) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15) !important;
}

.modern-btn-secondary {
  background: rgba(100, 116, 139, 0.1) !important;
  border: 2px solid rgba(100, 116, 139, 0.2) !important;
  color: #64748b !important;
  font-weight: 500 !important;
  padding: 8px 20px !important;
  height: 44px !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-secondary:hover {
  background: rgba(100, 116, 139, 0.15) !important;
  border-color: rgba(100, 116, 139, 0.3) !important;
  color: #475569 !important;
}

.modern-btn-outline.disabled,
.modern-btn-secondary.disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.modern-btn-outline.disabled:hover,
.modern-btn-secondary.disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}
</style>
