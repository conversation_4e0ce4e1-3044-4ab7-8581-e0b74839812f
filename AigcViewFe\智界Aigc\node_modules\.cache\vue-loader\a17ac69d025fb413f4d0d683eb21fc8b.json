{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue?vue&type=template&id=a83192ae&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue", "mtime": 1753944973183}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"related-plugins\">\n  <div class=\"related-card\">\n    <h3 class=\"section-title\">\n      <a-icon type=\"appstore\" />\n      相关推荐\n      <span class=\"category-hint\" v-if=\"categoryText\">- {{ categoryText }}</span>\n    </h3>\n    \n    <div v-if=\"recommendations && recommendations.length > 0\" class=\"plugins-grid\">\n      <div \n        v-for=\"plugin in recommendations\" \n        :key=\"plugin.id\"\n        class=\"plugin-card\"\n        @click=\"goToPlugin(plugin.id)\">\n        \n        <!-- 插件图片 -->\n        <div class=\"plugin-image\">\n          <img\n            :src=\"getPluginImage(plugin)\"\n            :alt=\"plugin.plubname\"\n            @error=\"handleImageError\"\n          />\n          <div class=\"image-overlay\">\n            <a-icon type=\"eye\" class=\"view-icon\" />\n          </div>\n        </div>\n        \n        <!-- 插件信息 -->\n        <div class=\"plugin-info\">\n          <h4 class=\"plugin-name\">{{ plugin.plubname }}</h4>\n          <p class=\"plugin-description\">{{ plugin.plubinfo || '暂无描述' }}</p>\n          \n          <div class=\"plugin-meta\">\n            <div class=\"meta-item\">\n              <a-icon type=\"dollar\" />\n              <span>{{ getPluginPriceText(plugin) }}</span>\n            </div>\n          </div>\n          \n          <div class=\"plugin-tags\">\n            <a-tag :color=\"getCategoryColor(plugin.plubCategory)\" size=\"small\">\n              {{ getCategoryText(plugin.plubCategory) }}\n            </a-tag>\n            <a-tag :color=\"getStatusColor(plugin.status)\" size=\"small\">\n              {{ getStatusText(plugin.status) }}\n            </a-tag>\n          </div>\n        </div>\n        \n        <!-- 悬浮操作 -->\n        <div class=\"plugin-actions\">\n          <a-button type=\"primary\" size=\"small\" @click.stop=\"goToPlugin(plugin.id)\">\n            查看详情\n          </a-button>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 无推荐内容 -->\n    <div v-else class=\"no-recommendations\">\n      <a-empty description=\"暂无相关推荐\">\n        <a-button type=\"primary\" @click=\"goToMarket\">\n          <a-icon type=\"shop\" />\n          浏览更多插件\n        </a-button>\n      </a-empty>\n    </div>\n    \n    <!-- 查看更多 -->\n    <div v-if=\"recommendations && recommendations.length > 0\" class=\"more-actions\">\n      <a-button class=\"more-plugins-btn\" @click=\"viewMoreByCategory\">\n        <a-icon type=\"appstore\" />\n        <span>查看更多插件</span>\n        <a-icon type=\"right\" />\n      </a-button>\n    </div>\n  </div>\n</div>\n", null]}