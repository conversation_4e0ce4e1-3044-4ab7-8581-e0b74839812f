{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue?vue&type=template&id=0abacd97&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue", "mtime": 1753944273572}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      staticClass: \"plugin-card\",\n      class: {\n        \"no-transition\": _vm.disableTransition,\n        \"combined-plugin\": _vm.isCombinedPlugin\n      },\n      on: {\n        click: function($event) {\n          return _vm.handlePluginClick(\"card\")\n        }\n      }\n    },\n    [\n      _c(\"div\", { staticClass: \"plugin-image\" }, [\n        _c(\"div\", { staticClass: \"image-overlay\" }),\n        _c(\"img\", {\n          attrs: {\n            src: _vm.getPluginImage(_vm.plugin),\n            alt: _vm.isCombinedPlugin\n              ? _vm.plugin.combinedName\n              : _vm.plugin.plubname\n          },\n          on: { error: _vm.handleImageError, load: _vm.handleImageLoad }\n        }),\n        _vm.isCombinedPlugin\n          ? _c(\"div\", { staticClass: \"combined-badge\" }, [\n              _c(\"span\", { staticClass: \"badge-icon\" }, [_vm._v(\"🔗\")]),\n              _vm._v(\"\\n      组合插件\\n    \")\n            ])\n          : _vm._e(),\n        _vm.plugin.plubCategory_dictText\n          ? _c(\"div\", { staticClass: \"plugin-category\" }, [\n              _c(\"span\", { staticClass: \"category-icon\" }, [\n                _vm._v(\n                  _vm._s(_vm.getCategoryIcon(_vm.plugin.plubCategory_dictText))\n                )\n              ]),\n              _vm._v(\n                \"\\n      \" + _vm._s(_vm.plugin.plubCategory_dictText) + \"\\n    \"\n              )\n            ])\n          : _vm._e(),\n        _vm.plugin.status_dictText && _vm.plugin.status_dictText !== \"正常\"\n          ? _c(\n              \"div\",\n              {\n                staticClass: \"plugin-badge\",\n                class: _vm.getBadgeClass(_vm.plugin.status_dictText)\n              },\n              [\n                _vm._v(\n                  \"\\n      \" + _vm._s(_vm.plugin.status_dictText) + \"\\n    \"\n                )\n              ]\n            )\n          : _vm._e(),\n        _vm.imageLoading\n          ? _c(\n              \"div\",\n              { staticClass: \"image-loading\" },\n              [_c(\"a-spin\", { attrs: { size: \"small\" } })],\n              1\n            )\n          : _vm._e()\n      ]),\n      _c(\"div\", { staticClass: \"plugin-info\" }, [\n        _vm.isCombinedPlugin\n          ? _c(\"div\", { staticClass: \"combined-plugin-info\" }, [\n              _c(\"div\", { staticClass: \"plugin-header-info\" }, [\n                _c(\n                  \"h3\",\n                  {\n                    staticClass: \"plugin-name\",\n                    attrs: { title: _vm.plugin.combinedName }\n                  },\n                  [\n                    _vm._v(\n                      \"\\n          \" +\n                        _vm._s(_vm.plugin.combinedName) +\n                        \"\\n        \"\n                    )\n                  ]\n                ),\n                _vm.plugin.plubwrite_dictText\n                  ? _c(\n                      \"span\",\n                      { staticClass: \"plugin-author\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"user\" } }),\n                        _vm._v(\n                          \"\\n          创作者 \" +\n                            _vm._s(_vm.plugin.plubwrite_dictText) +\n                            \"\\n        \"\n                        )\n                      ],\n                      1\n                    )\n                  : _vm._e()\n              ]),\n              _c(\n                \"p\",\n                {\n                  staticClass: \"plugin-description\",\n                  attrs: { title: _vm.plugin.combinedDescription }\n                },\n                [\n                  _vm._v(\n                    \"\\n        \" +\n                      _vm._s(\n                        _vm.truncateText(_vm.plugin.combinedDescription, 100)\n                      ) +\n                      \"\\n      \"\n                  )\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"plugin-footer\" },\n                [\n                  _vm._m(0),\n                  _c(\n                    \"a-button\",\n                    {\n                      staticClass: \"detail-button combined-detail-btn\",\n                      attrs: { type: \"primary\", size: \"default\" },\n                      on: {\n                        click: function($event) {\n                          $event.stopPropagation()\n                          return _vm.handlePluginClick(\"button\")\n                        }\n                      }\n                    },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                      _vm._v(\"\\n          查看详情\\n        \")\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ])\n          : _c(\"div\", { staticClass: \"normal-plugin-info\" }, [\n              _c(\"div\", { staticClass: \"plugin-header-info\" }, [\n                _c(\n                  \"h3\",\n                  {\n                    staticClass: \"plugin-name\",\n                    attrs: { title: _vm.plugin.plubname }\n                  },\n                  [\n                    _vm._v(\n                      \"\\n          \" +\n                        _vm._s(_vm.plugin.plubname) +\n                        \"\\n        \"\n                    )\n                  ]\n                ),\n                _vm.plugin.plubwrite_dictText\n                  ? _c(\n                      \"span\",\n                      { staticClass: \"plugin-author\" },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"user\" } }),\n                        _vm._v(\n                          \"\\n          创作者 \" +\n                            _vm._s(_vm.plugin.plubwrite_dictText) +\n                            \"\\n        \"\n                        )\n                      ],\n                      1\n                    )\n                  : _vm._e()\n              ]),\n              _c(\n                \"p\",\n                {\n                  staticClass: \"plugin-description\",\n                  attrs: { title: _vm.plugin.plubinfo }\n                },\n                [\n                  _vm._v(\n                    \"\\n        \" +\n                      _vm._s(_vm.truncateText(_vm.plugin.plubinfo, 100)) +\n                      \"\\n      \"\n                  )\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"plugin-footer\" },\n                [\n                  _c(\"div\", { staticClass: \"plugin-price\" }, [\n                    _c(\"span\", { staticClass: \"price-value\" }, [\n                      _vm._v(_vm._s(_vm.getPriceText()))\n                    ])\n                  ]),\n                  _c(\n                    \"a-button\",\n                    {\n                      staticClass: \"detail-button\",\n                      attrs: { type: \"primary\", size: \"default\" },\n                      on: {\n                        click: function($event) {\n                          $event.stopPropagation()\n                          return _vm.handlePluginClick(\"button\")\n                        }\n                      }\n                    },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                      _vm._v(\"\\n          查看详情\\n        \")\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ])\n      ])\n    ]\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"plugin-price combined-price\" }, [\n      _c(\"span\", { staticClass: \"price-hint\" }, [_vm._v(\"收费请进入详情查看\")])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}