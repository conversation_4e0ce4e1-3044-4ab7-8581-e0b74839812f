<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    width="1200px"
    :footer="null"
    @cancel="handleCancel"
    :z-index="2000"
    :mask-closable="true"
    :destroy-on-close="false"
    class="combined-plugin-modal"
    :get-container="() => document.body">
    
    <!-- 🔥 组合插件头部信息 -->
    <div class="combined-plugin-header">
      <div class="combined-info">
        <div class="combined-image">
          <img
            :src="getPluginImage(combinedPlugin)"
            :alt="combinedPlugin && combinedPlugin.combinedName"
            @error="handleImageError" />
        </div>
        <div class="combined-details">
          <h2 class="combined-title">{{ combinedPlugin && combinedPlugin.combinedName }}</h2>
          <p class="combined-description">{{ combinedPlugin && combinedPlugin.combinedDescription }}</p>
          <div class="combined-meta">
            <span class="meta-item">
              <a-icon type="user" />
              创作者：{{ (combinedPlugin && combinedPlugin.plubwrite_dictText) || '未知' }}
            </span>
            <span class="meta-item">
              <a-icon type="tag" />
              分类：{{ getCategoryText(combinedPlugin && combinedPlugin.plubCategory) }}
            </span>
            <span class="meta-item">
              <a-icon type="link" />
              组合插件
            </span>
          </div>
        </div>
      </div>
    </div>

    <a-divider>
      <span class="divider-text">
        <a-icon type="appstore" />
        包含的插件 ({{ subPlugins.length }})
      </span>
    </a-divider>

    <!-- 🔥 子插件网格展示 -->
    <div class="sub-plugins-container" v-loading="loading">
      <div v-if="loading" class="loading-container">
        <a-spin size="large">
          <span slot="tip">正在加载插件列表...</span>
        </a-spin>
      </div>
      
      <div v-else-if="subPlugins.length === 0" class="empty-container">
        <a-empty description="暂无子插件" />
      </div>
      
      <div v-else class="sub-plugins-grid">
        <div 
          v-for="subPlugin in subPlugins" 
          :key="subPlugin.id"
          class="sub-plugin-card"
          @click="selectSubPlugin(subPlugin)">
          
          <div class="sub-plugin-image">
            <img
              :src="getPluginImage(subPlugin)"
              :alt="subPlugin.plubname"
              @error="handleSubImageError" />
            <div class="plugin-overlay">
              <a-icon type="eye" class="view-icon" />
            </div>
          </div>
          
          <div class="sub-plugin-info">
            <h4 class="sub-plugin-title" :title="subPlugin.plubname">
              {{ subPlugin.plubname }}
            </h4>
            <p class="sub-plugin-description" :title="subPlugin.plubinfo">
              {{ truncateText(subPlugin.plubinfo, 60) }}
            </p>
            <div class="sub-plugin-meta">
              <span class="category-tag">
                {{ getCategoryText(subPlugin.plubCategory) }}
              </span>
              <span class="price-tag">
                {{ getSubPluginPriceText(subPlugin) }}
              </span>
            </div>
          </div>
          
          <div class="sub-plugin-actions">
            <a-button type="primary" size="small" @click.stop="selectSubPlugin(subPlugin)">
              <a-icon type="eye" />
              查看详情
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import { getPluginImageUrl } from '../utils/marketUtils'

export default {
  name: 'CombinedPluginModal',
  
  props: {
    value: {
      type: Boolean,
      default: false
    },
    combinedPlugin: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      visible: false,
      loading: false,
      subPlugins: []
    }
  },

  mounted() {
    console.log('🔗 CombinedPluginModal - 组件已挂载')
    console.log('🔗 CombinedPluginModal - 初始visible:', this.visible)
    console.log('🔗 CombinedPluginModal - 初始value:', this.value)
  },
  
  computed: {
    modalTitle() {
      return (this.combinedPlugin && this.combinedPlugin.combinedName) ?
        `选择插件 - ${this.combinedPlugin.combinedName}` :
        '选择插件'
    },

    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）
    defaultPluginImage() {
      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    }
  },



  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        console.log('🔗 CombinedPluginModal - value变化:', newVal)
        this.visible = newVal
        if (newVal && this.combinedPlugin && this.combinedPlugin.combinedName) {
          this.loadSubPlugins()
        }
      }
    },

    visible(newVal) {
      console.log('🔗 CombinedPluginModal - visible变化:', newVal)
      this.$emit('input', newVal)
    }

    // visible 不再需要watch，因为我们直接使用props
  },
  
  methods: {
    // 🔥 加载子插件列表
    async loadSubPlugins() {
      if (!this.combinedPlugin.combinedName) {
        this.$notification.warning({
          message: '参数错误',
          description: '组合插件名称不能为空',
          placement: 'topRight'
        })
        return
      }

      this.loading = true
      try {
        console.log('🔍 加载组合插件子插件:', this.combinedPlugin.combinedName)

        // 使用原有的公开API，通过前端筛选获取子插件
        const response = await getAction('/plubshop/aigcPlubShop/list', {
          pageNo: 1,
          pageSize: 100,
          combinedName: this.combinedPlugin.combinedName,
          isCombined: 1,
          status: 1
        })

        if (response.success) {
          const result = response.result || response.data
          this.subPlugins = result.records || result || []
          console.log('✅ 子插件加载成功:', this.subPlugins.length)
        } else {
          this.$notification.error({
            message: '加载失败',
            description: '获取子插件列表失败：' + response.message,
            placement: 'topRight'
          })
          this.subPlugins = []
        }
      } catch (error) {
        console.error('❌ 获取子插件列表异常:', error)
        this.$notification.error({
          message: '系统异常',
          description: '获取子插件列表异常',
          placement: 'topRight'
        })
        this.subPlugins = []
      } finally {
        this.loading = false
      }
    },
    
    // 🔥 选择子插件
    selectSubPlugin(subPlugin) {
      console.log('🎯 选择子插件:', subPlugin.plubname)
      this.$emit('select-sub-plugin', subPlugin)
    },
    
    // 🔥 关闭弹窗
    handleCancel() {
      this.visible = false
      this.subPlugins = []
    },
    
    // 🔥 获取插件图片（支持组合插件优先级处理）
    getPluginImage(pluginOrPath) {
      // 如果传入的是字符串路径，保持向后兼容
      if (typeof pluginOrPath === 'string') {
        return getPluginImageUrl(pluginOrPath, this.defaultPluginImage)
      }

      // 如果传入的是插件对象，使用新的优先级逻辑
      return getPluginImageUrl(pluginOrPath, this.defaultPluginImage)
    },

    // 🔥 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    },

    handleSubImageError(event) {
      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    },
    
    // 🔥 获取分类文本
    getCategoryText(categoryValue) {
      if (this.$categoryService) {
        return this.$categoryService.getCategoryText(categoryValue)
      }
      return categoryValue || '未知分类'
    },
    
    // 🔥 截断文本
    truncateText(text, maxLength) {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    },
    
    // 🔥 格式化价格
    formatPrice(price) {
      if (price === null || price === undefined) return '0'
      return parseFloat(price).toFixed(2)
    },

    // 🔥 获取子插件价格显示文本
    getSubPluginPriceText(subPlugin) {
      const price = subPlugin.neednum
      const isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1'

      if (!price || price <= 0) {
        return '免费'
      }

      if (isSvipFree) {
        return `SVIP免费，低至¥${this.formatPrice(price)}/次`
      } else {
        return `低至¥${this.formatPrice(price)}/次`
      }
    }
  }
}
</script>

<style scoped>
/* 🔥 强制显示Modal */
.combined-plugin-modal {
  z-index: 2000 !important;
}

.combined-plugin-modal .ant-modal {
  z-index: 2000 !important;
}

.combined-plugin-modal .ant-modal-mask {
  z-index: 1999 !important;
  background-color: rgba(0, 0, 0, 0.45) !important;
}

.combined-plugin-modal .ant-modal-wrap {
  z-index: 2000 !important;
}

.combined-plugin-modal .ant-modal-body {
  padding: 24px;
  max-height: 80vh;
  overflow-y: auto;
}

/* 🔥 组合插件头部 */
.combined-plugin-header {
  margin-bottom: 24px;
}

.combined-info {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.combined-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.combined-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.combined-details {
  flex: 1;
}

.combined-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.combined-description {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.combined-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  font-size: 0.9rem;
}

.meta-item .anticon {
  color: #3b82f6;
}

/* 🔥 分割线 */
.divider-text {
  color: #64748b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 🔥 子插件容器 */
.sub-plugins-container {
  min-height: 200px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 🔥 子插件网格 */
.sub-plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.sub-plugin-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.sub-plugin-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.sub-plugin-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.sub-plugin-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.sub-plugin-card:hover .sub-plugin-image img {
  transform: scale(1.05);
}

.plugin-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sub-plugin-card:hover .plugin-overlay {
  opacity: 1;
}

.view-icon {
  color: white;
  font-size: 2rem;
}

.sub-plugin-info {
  padding: 16px;
}

.sub-plugin-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sub-plugin-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 12px 0;
  height: 2.7rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.sub-plugin-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.price-tag {
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.9rem;
}

.sub-plugin-actions {
  padding: 0 16px 16px 16px;
}

.sub-plugin-actions .ant-btn {
  width: 100%;
  border-radius: 8px;
  font-weight: 500;
}

/* 🔥 响应式设计 */
@media (max-width: 768px) {
  .combined-info {
    flex-direction: column;
    text-align: center;
  }
  
  .combined-image {
    align-self: center;
  }
  
  .sub-plugins-grid {
    grid-template-columns: 1fr;
  }
  
  .combined-plugin-modal .ant-modal {
    width: 95% !important;
    margin: 10px;
  }
}
</style>
