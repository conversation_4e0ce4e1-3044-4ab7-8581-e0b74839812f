{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue", "mtime": 1753947183493}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\nimport '@/assets/less/TableExpand.less'\nimport { mixinDevice } from '@/utils/mixin'\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin'\nimport AigcPlubShopModal from './modules/AigcPlubShopModal'\nimport {filterMultiDictText} from '@/components/dict/JDictSelectUtil'\n\nexport default {\n  name: 'AigcPlubShopList',\n  mixins:[JeecgListMixin, mixinDevice],\n  components: {\n    AigcPlubShopModal\n  },\n  data () {\n    return {\n      description: '插件商城管理页面',\n      // 表头\n      columns: [\n        {\n          title: '#',\n          dataIndex: '',\n          key:'rowIndex',\n          width:60,\n          align:\"center\",\n          customRender:function (t,r,index) {\n            return parseInt(index)+1;\n          }\n        },\n        {\n          title:'插件名称',\n          align:\"center\",\n          dataIndex: 'plubname'\n        },\n        {\n          title:'图片',\n          align:\"center\",\n          dataIndex: 'plubimg',\n          scopedSlots: {customRender: 'imgSlot'}\n        },\n        {\n          title:'插件创作者',\n          align:\"center\",\n          dataIndex: 'plubwrite_dictText'\n        },\n        {\n          title:'插件介绍',\n          align:\"center\",\n          dataIndex: 'plubinfo'\n        },\n        {\n          title:'插件分类',\n          align:\"center\",\n          dataIndex: 'plubCategory_dictText'\n        },\n        {\n          title:'插件状态',\n          align:\"center\",\n          dataIndex: 'status_dictText'\n        },\n        {\n          title:'是否组合插件',\n          align:\"center\",\n          dataIndex: 'isCombined_dictText',\n          width: 120\n        },\n        {\n          title:'组合插件名',\n          align:\"center\",\n          dataIndex: 'combinedName',\n          width: 150,\n          customRender: (text) => {\n            return text || '-';\n          }\n        },\n        {\n          title:'组合插件图片',\n          align:\"center\",\n          dataIndex: 'combinedImage',\n          width: 120,\n          scopedSlots: {customRender: 'imgSlot'}\n        },\n        {\n          title:'插件唯一标识',\n          align:\"center\",\n          dataIndex: 'pluginKey'\n        },\n        {\n          title:'教程链接',\n          align:\"center\",\n          dataIndex: 'tutorialLink',\n          scopedSlots: {customRender: 'tutorialLinkSlot'}\n        },\n        {\n          title:'排序权重',\n          align:\"center\",\n          dataIndex: 'sortOrder',\n          sorter: true,\n          defaultSortOrder: 'ascend'\n        },\n        {\n          title:'插件教程视频',\n          align:\"center\",\n          dataIndex: 'plubvideo',\n          scopedSlots: {customRender: 'fileSlot'}\n        },\n        {\n          title:'收益金额',\n          align:\"center\",\n          dataIndex: 'income',\n          customRender: function (text) {\n            return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';\n          }\n        },\n        {\n          title:'调用次数',\n          align:\"center\",\n          dataIndex: 'usernum'\n        },\n        {\n          title:'需要金额',\n          align:\"center\",\n          dataIndex: 'neednum',\n          customRender: function (text) {\n            return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';\n          }\n        },\n        {\n          title:'SVIP是否免费',\n          align:\"center\",\n          dataIndex: 'isSvipFree_dictText'\n        },\n        {\n          title: '操作',\n          dataIndex: 'action',\n          align:\"center\",\n          fixed:\"right\",\n          width: this.isAdmin ? 200 : 80, // admin用户宽度147，非admin用户宽度80\n          scopedSlots: { customRender: 'action' }\n        }\n      ],\n      url: {\n        list: \"/plubshop/aigcPlubShop/list\",\n        delete: \"/plubshop/aigcPlubShop/delete\",\n        deleteBatch: \"/plubshop/aigcPlubShop/deleteBatch\",\n        exportXlsUrl: \"/plubshop/aigcPlubShop/exportXls\",\n        importExcelUrl: \"plubshop/aigcPlubShop/importExcel\",\n\n      },\n      // 🎯 覆盖默认排序：按排序权重升序排列（数字小的在前）\n      isorter: {\n        column: 'sortOrder',\n        order: 'asc',\n      },\n      dictOptions:{},\n      superFieldList:[],\n    }\n  },\n  created() {\n  this.getSuperFieldList();\n  },\n  computed: {\n    importExcelUrl: function(){\n      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n    },\n    isAdmin() {\n      // 判断当前用户是否为admin角色（基于role_code）\n      const userRole = localStorage.getItem('userRole');\n      return userRole && userRole.toLowerCase().includes('admin');\n    }\n  },\n  methods: {\n    initDictConfig(){\n    },\n    getSuperFieldList(){\n      let fieldList=[];\n      fieldList.push({type:'string',value:'plubname',text:'插件名称',dictCode:''})\n      fieldList.push({type:'string',value:'plubimg',text:'图片',dictCode:''})\n      fieldList.push({type:'string',value:'plubwrite',text:'插件创作者',dictCode:'aigc_plub_author,authorname,id'})\n      fieldList.push({type:'string',value:'plubinfo',text:'插件介绍',dictCode:''})\n      fieldList.push({type:'string',value:'plubContent',text:'插件详细内容',dictCode:''})\n      fieldList.push({type:'string',value:'plubCategory',text:'插件分类',dictCode:'plugin_category'})\n      fieldList.push({type:'int',value:'status',text:'插件状态',dictCode:'plugin_status'})\n      fieldList.push({type:'int',value:'isCombined',text:'是否组合插件',dictCode:'isTrue'})\n      fieldList.push({type:'int',value:'isSvipFree',text:'SVIP是否免费',dictCode:'isTrue'})\n      fieldList.push({type:'string',value:'combinedName',text:'组合插件名',dictCode:''})\n      fieldList.push({type:'string',value:'combinedDescription',text:'组合插件介绍',dictCode:''})\n      fieldList.push({type:'string',value:'combinedImage',text:'组合插件图片',dictCode:''})\n      fieldList.push({type:'string',value:'pluginKey',text:'插件唯一标识',dictCode:''})\n      fieldList.push({type:'int',value:'sortOrder',text:'排序权重',dictCode:''})\n      fieldList.push({type:'string',value:'plubvideo',text:'插件教程视频',dictCode:''})\n      fieldList.push({type:'BigDecimal',value:'income',text:'收益金额',dictCode:''})\n      fieldList.push({type:'int',value:'usernum',text:'调用次数',dictCode:''})\n      fieldList.push({type:'BigDecimal',value:'neednum',text:'需要金额',dictCode:''})\n      this.superFieldList = fieldList\n    }\n  }\n}\n", {"version": 3, "sources": ["AigcPlubShopList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+KA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AigcPlubShopList.vue", "sourceRoot": "src/views/aigcview/plubshop", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n        <a-row :gutter=\"24\">\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"插件名称\">\n              <a-input placeholder=\"请输入插件名称\" v-model=\"queryParam.plubname\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"插件创作者\">\n              <j-dict-select-tag placeholder=\"请选择插件创作者\" v-model=\"queryParam.plubwrite\" dictCode=\"aigc_plub_author,authorname,id\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"插件状态\">\n              <j-dict-select-tag placeholder=\"请选择插件状态\" v-model=\"queryParam.status\" dictCode=\"plugin_status\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"是否组合插件\">\n              <j-dict-select-tag placeholder=\"请选择是否组合插件\" v-model=\"queryParam.isCombined\" dictCode=\"isTrue\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"是否SVIP免费\">\n              <j-dict-select-tag placeholder=\"请选择是否SVIP免费\" v-model=\"queryParam.isSvipFree\" dictCode=\"isTrue\"/>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"toggleSearchStatus\">\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"组合插件名\">\n                <a-input placeholder=\"请输入组合插件名\" v-model=\"queryParam.combinedName\"></a-input>\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n              <a-form-item label=\"收益金额\">\n                <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.income_begin\"></a-input>\n                <span class=\"query-group-split-cust\"></span>\n                <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.income_end\"></a-input>\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n              <a-form-item label=\"调用次数\">\n                <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.usernum_begin\"></a-input>\n                <span class=\"query-group-split-cust\"></span>\n                <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.usernum_end\"></a-input>\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n              <a-form-item label=\"需要金额\">\n                <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.neednum_begin\"></a-input>\n                <span class=\"query-group-split-cust\"></span>\n                <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.neednum_end\"></a-input>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n                {{ toggleSearchStatus ? '收起' : '展开' }}\n                <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n    <!-- 查询区域-END -->\n\n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n      <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('插件商城')\">导出</a-button>\n\n      <!-- admin用户才能看到的功能 -->\n      <template v-if=\"isAdmin\">\n        <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\" @change=\"handleImportExcel\">\n          <a-button type=\"primary\" icon=\"import\">导入</a-button>\n        </a-upload>\n        <!-- 高级查询区域 -->\n        <j-super-query :fieldList=\"superFieldList\" ref=\"superQueryModal\" @handleSuperQuery=\"handleSuperQuery\"></j-super-query>\n        <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n          <a-menu slot=\"overlay\">\n            <a-menu-item key=\"1\" @click=\"batchDel\"><a-icon type=\"delete\"/>删除</a-menu-item>\n          </a-menu>\n          <a-button style=\"margin-left: 8px\"> 批量操作 <a-icon type=\"down\" /></a-button>\n        </a-dropdown>\n      </template>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        :scroll=\"{x:true}\"\n        bordered\n        rowKey=\"id\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        class=\"j-table-force-nowrap\"\n        @change=\"handleTableChange\">\n\n        <template slot=\"htmlSlot\" slot-scope=\"text\">\n          <div v-html=\"text\"></div>\n        </template>\n        <template slot=\"imgSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无图片</span>\n          <img v-else :src=\"getImgView(text)\" height=\"25px\" alt=\"\" style=\"max-width:80px;font-size: 12px;font-style: italic;\"/>\n        </template>\n        <template slot=\"fileSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无文件</span>\n          <a-button\n            v-else\n            :ghost=\"true\"\n            type=\"primary\"\n            icon=\"download\"\n            size=\"small\"\n            @click=\"downloadFile(text)\">\n            下载\n          </a-button>\n        </template>\n\n        <template slot=\"tutorialLinkSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">暂无</span>\n          <a v-else :href=\"text\" target=\"_blank\" style=\"color: #1890ff;\">\n            <a-icon type=\"link\" style=\"margin-right: 4px;\" />\n            查看教程\n          </a>\n        </template>\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n\n          <!-- 只有admin用户才能看到更多操作 -->\n          <template v-if=\"isAdmin\">\n            <a-divider type=\"vertical\" />\n            <a-dropdown>\n              <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n              <a-menu slot=\"overlay\">\n                <a-menu-item>\n                  <a @click=\"handleDetail(record)\">详情</a>\n                </a-menu-item>\n                <a-menu-item>\n                  <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                    <a>删除</a>\n                  </a-popconfirm>\n                </a-menu-item>\n              </a-menu>\n            </a-dropdown>\n          </template>\n        </span>\n\n      </a-table>\n    </div>\n\n    <aigc-plub-shop-modal ref=\"modalForm\" @ok=\"modalFormOk\"></aigc-plub-shop-modal>\n  </a-card>\n</template>\n\n<script>\n\n  import '@/assets/less/TableExpand.less'\n  import { mixinDevice } from '@/utils/mixin'\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import AigcPlubShopModal from './modules/AigcPlubShopModal'\n  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'\n\n  export default {\n    name: 'AigcPlubShopList',\n    mixins:[JeecgListMixin, mixinDevice],\n    components: {\n      AigcPlubShopModal\n    },\n    data () {\n      return {\n        description: '插件商城管理页面',\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key:'rowIndex',\n            width:60,\n            align:\"center\",\n            customRender:function (t,r,index) {\n              return parseInt(index)+1;\n            }\n          },\n          {\n            title:'插件名称',\n            align:\"center\",\n            dataIndex: 'plubname'\n          },\n          {\n            title:'图片',\n            align:\"center\",\n            dataIndex: 'plubimg',\n            scopedSlots: {customRender: 'imgSlot'}\n          },\n          {\n            title:'插件创作者',\n            align:\"center\",\n            dataIndex: 'plubwrite_dictText'\n          },\n          {\n            title:'插件介绍',\n            align:\"center\",\n            dataIndex: 'plubinfo'\n          },\n          {\n            title:'插件分类',\n            align:\"center\",\n            dataIndex: 'plubCategory_dictText'\n          },\n          {\n            title:'插件状态',\n            align:\"center\",\n            dataIndex: 'status_dictText'\n          },\n          {\n            title:'是否组合插件',\n            align:\"center\",\n            dataIndex: 'isCombined_dictText',\n            width: 120\n          },\n          {\n            title:'组合插件名',\n            align:\"center\",\n            dataIndex: 'combinedName',\n            width: 150,\n            customRender: (text) => {\n              return text || '-';\n            }\n          },\n          {\n            title:'组合插件图片',\n            align:\"center\",\n            dataIndex: 'combinedImage',\n            width: 120,\n            scopedSlots: {customRender: 'imgSlot'}\n          },\n          {\n            title:'插件唯一标识',\n            align:\"center\",\n            dataIndex: 'pluginKey'\n          },\n          {\n            title:'教程链接',\n            align:\"center\",\n            dataIndex: 'tutorialLink',\n            scopedSlots: {customRender: 'tutorialLinkSlot'}\n          },\n          {\n            title:'排序权重',\n            align:\"center\",\n            dataIndex: 'sortOrder',\n            sorter: true,\n            defaultSortOrder: 'ascend'\n          },\n          {\n            title:'插件教程视频',\n            align:\"center\",\n            dataIndex: 'plubvideo',\n            scopedSlots: {customRender: 'fileSlot'}\n          },\n          {\n            title:'收益金额',\n            align:\"center\",\n            dataIndex: 'income',\n            customRender: function (text) {\n              return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';\n            }\n          },\n          {\n            title:'调用次数',\n            align:\"center\",\n            dataIndex: 'usernum'\n          },\n          {\n            title:'需要金额',\n            align:\"center\",\n            dataIndex: 'neednum',\n            customRender: function (text) {\n              return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';\n            }\n          },\n          {\n            title:'SVIP是否免费',\n            align:\"center\",\n            dataIndex: 'isSvipFree_dictText'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align:\"center\",\n            fixed:\"right\",\n            width: this.isAdmin ? 200 : 80, // admin用户宽度147，非admin用户宽度80\n            scopedSlots: { customRender: 'action' }\n          }\n        ],\n        url: {\n          list: \"/plubshop/aigcPlubShop/list\",\n          delete: \"/plubshop/aigcPlubShop/delete\",\n          deleteBatch: \"/plubshop/aigcPlubShop/deleteBatch\",\n          exportXlsUrl: \"/plubshop/aigcPlubShop/exportXls\",\n          importExcelUrl: \"plubshop/aigcPlubShop/importExcel\",\n\n        },\n        // 🎯 覆盖默认排序：按排序权重升序排列（数字小的在前）\n        isorter: {\n          column: 'sortOrder',\n          order: 'asc',\n        },\n        dictOptions:{},\n        superFieldList:[],\n      }\n    },\n    created() {\n    this.getSuperFieldList();\n    },\n    computed: {\n      importExcelUrl: function(){\n        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n      },\n      isAdmin() {\n        // 判断当前用户是否为admin角色（基于role_code）\n        const userRole = localStorage.getItem('userRole');\n        return userRole && userRole.toLowerCase().includes('admin');\n      }\n    },\n    methods: {\n      initDictConfig(){\n      },\n      getSuperFieldList(){\n        let fieldList=[];\n        fieldList.push({type:'string',value:'plubname',text:'插件名称',dictCode:''})\n        fieldList.push({type:'string',value:'plubimg',text:'图片',dictCode:''})\n        fieldList.push({type:'string',value:'plubwrite',text:'插件创作者',dictCode:'aigc_plub_author,authorname,id'})\n        fieldList.push({type:'string',value:'plubinfo',text:'插件介绍',dictCode:''})\n        fieldList.push({type:'string',value:'plubContent',text:'插件详细内容',dictCode:''})\n        fieldList.push({type:'string',value:'plubCategory',text:'插件分类',dictCode:'plugin_category'})\n        fieldList.push({type:'int',value:'status',text:'插件状态',dictCode:'plugin_status'})\n        fieldList.push({type:'int',value:'isCombined',text:'是否组合插件',dictCode:'isTrue'})\n        fieldList.push({type:'int',value:'isSvipFree',text:'SVIP是否免费',dictCode:'isTrue'})\n        fieldList.push({type:'string',value:'combinedName',text:'组合插件名',dictCode:''})\n        fieldList.push({type:'string',value:'combinedDescription',text:'组合插件介绍',dictCode:''})\n        fieldList.push({type:'string',value:'combinedImage',text:'组合插件图片',dictCode:''})\n        fieldList.push({type:'string',value:'pluginKey',text:'插件唯一标识',dictCode:''})\n        fieldList.push({type:'int',value:'sortOrder',text:'排序权重',dictCode:''})\n        fieldList.push({type:'string',value:'plubvideo',text:'插件教程视频',dictCode:''})\n        fieldList.push({type:'BigDecimal',value:'income',text:'收益金额',dictCode:''})\n        fieldList.push({type:'int',value:'usernum',text:'调用次数',dictCode:''})\n        fieldList.push({type:'BigDecimal',value:'neednum',text:'需要金额',dictCode:''})\n        this.superFieldList = fieldList\n      }\n    }\n  }\n</script>\n<style scoped>\n  @import '~@assets/less/common.less';\n</style>"]}]}