{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue?vue&type=template&id=a4b5014e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue", "mtime": 1753945012309}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\"div\", { staticClass: \"plugin-detail-page\" }, [\n      _vm.loading\n        ? _c(\"div\", { staticClass: \"loading-container\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"loading-content\" },\n              [\n                _c(\n                  \"a-spin\",\n                  { attrs: { size: \"large\", tip: \"加载插件详情中...\" } },\n                  [\n                    _c(\"div\", { staticClass: \"loading-skeleton\" }, [\n                      _c(\"div\", { staticClass: \"skeleton-header\" }, [\n                        _c(\"div\", { staticClass: \"skeleton-breadcrumb\" }),\n                        _c(\"div\", { staticClass: \"skeleton-plugin-info\" }, [\n                          _c(\"div\", { staticClass: \"skeleton-image\" }),\n                          _c(\"div\", { staticClass: \"skeleton-details\" }, [\n                            _c(\"div\", { staticClass: \"skeleton-title\" }),\n                            _c(\"div\", { staticClass: \"skeleton-description\" }),\n                            _c(\"div\", { staticClass: \"skeleton-meta\" }, [\n                              _c(\"div\", { staticClass: \"skeleton-tag\" }),\n                              _c(\"div\", { staticClass: \"skeleton-tag\" }),\n                              _c(\"div\", { staticClass: \"skeleton-tag\" })\n                            ])\n                          ])\n                        ])\n                      ]),\n                      _c(\"div\", { staticClass: \"skeleton-tabs\" }, [\n                        _c(\"div\", { staticClass: \"skeleton-tab-bar\" }, [\n                          _c(\"div\", { staticClass: \"skeleton-tab\" }),\n                          _c(\"div\", { staticClass: \"skeleton-tab\" }),\n                          _c(\"div\", { staticClass: \"skeleton-tab\" }),\n                          _c(\"div\", { staticClass: \"skeleton-tab\" })\n                        ]),\n                        _c(\"div\", { staticClass: \"skeleton-tab-content\" })\n                      ])\n                    ])\n                  ]\n                )\n              ],\n              1\n            )\n          ])\n        : _vm.pluginDetail\n        ? _c(\"div\", { staticClass: \"plugin-detail-content\" }, [\n            _c(\"div\", { staticClass: \"navigation-bar\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"nav-left\" },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      staticClass: \"back-button\",\n                      attrs: { type: \"text\", size: \"large\" },\n                      on: { click: _vm.goBack }\n                    },\n                    [_c(\"a-icon\", { attrs: { type: \"arrow-left\" } })],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    { staticClass: \"breadcrumb-path\" },\n                    [\n                      _c(\n                        \"span\",\n                        {\n                          staticClass: \"path-item\",\n                          on: {\n                            click: function($event) {\n                              return _vm.$router.push(\"/\")\n                            }\n                          }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"home\" } }),\n                          _vm._v(\"\\n            首页\\n          \")\n                        ],\n                        1\n                      ),\n                      _c(\"a-icon\", {\n                        staticClass: \"path-separator\",\n                        attrs: { type: \"right\" }\n                      }),\n                      _c(\n                        \"span\",\n                        {\n                          staticClass: \"path-item\",\n                          on: { click: _vm.goBackToMarket }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"shop\" } }),\n                          _vm._v(\"\\n            商城\\n          \")\n                        ],\n                        1\n                      ),\n                      _c(\"a-icon\", {\n                        staticClass: \"path-separator\",\n                        attrs: { type: \"right\" }\n                      }),\n                      _c(\"span\", { staticClass: \"path-current\" }, [\n                        _vm._v(\n                          \"\\n            \" +\n                            _vm._s(_vm.pluginDetail.plubname || \"插件详情\") +\n                            \"\\n          \"\n                        )\n                      ])\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"nav-right\" },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      staticClass: \"share-button\",\n                      on: { click: _vm.sharePlugin }\n                    },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"share-alt\" } }),\n                      _vm._v(\"\\n          分享\\n        \")\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ]),\n            _c(\"div\", { staticClass: \"plugin-header\" }, [\n              _c(\"div\", { staticClass: \"plugin-header-content\" }, [\n                _c(\"div\", { staticClass: \"plugin-image\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: _vm.getPluginImage(_vm.pluginDetail),\n                      alt: _vm.pluginDetail.plubname\n                    },\n                    on: { error: _vm.handleImageError }\n                  })\n                ]),\n                _c(\"div\", { staticClass: \"plugin-info\" }, [\n                  _c(\"h1\", { staticClass: \"plugin-title\" }, [\n                    _vm._v(_vm._s(_vm.pluginDetail.plubname))\n                  ]),\n                  _c(\"p\", { staticClass: \"plugin-description\" }, [\n                    _vm._v(_vm._s(_vm.pluginDetail.plubinfo))\n                  ]),\n                  _c(\"div\", { staticClass: \"plugin-meta\" }, [\n                    _c(\"div\", { staticClass: \"meta-row\" }, [\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\n                            \"a-tag\",\n                            {\n                              attrs: {\n                                color: _vm.getCategoryColor(\n                                  _vm.pluginDetail.plubCategory\n                                ),\n                                size: \"large\"\n                              }\n                            },\n                            [\n                              _c(\"a-icon\", { attrs: { type: \"appstore\" } }),\n                              _vm._v(\n                                \"\\n                  \" +\n                                  _vm._s(_vm.categoryText) +\n                                  \"\\n                \"\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\n                            \"a-tag\",\n                            {\n                              attrs: {\n                                color: _vm.getStatusColor(\n                                  _vm.pluginDetail.status\n                                ),\n                                size: \"large\"\n                              }\n                            },\n                            [\n                              _c(\"a-icon\", { attrs: { type: \"check-circle\" } }),\n                              _vm._v(\n                                \"\\n                  \" +\n                                  _vm._s(\n                                    _vm.getStatusText(_vm.pluginDetail.status)\n                                  ) +\n                                  \"\\n                \"\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ]),\n                    _c(\"div\", { staticClass: \"meta-row\" }, [\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\"a-icon\", {\n                            staticClass: \"meta-icon\",\n                            attrs: { type: \"user\" }\n                          }),\n                          _c(\"span\", { staticClass: \"meta-label\" }, [\n                            _vm._v(\"创作者：\")\n                          ]),\n                          _c(\"span\", { staticClass: \"meta-value\" }, [\n                            _vm._v(_vm._s(_vm.authorInfo.authorname || \"未知\"))\n                          ])\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item price-item\" },\n                        [\n                          _c(\"a-icon\", {\n                            staticClass: \"meta-icon price-icon\",\n                            attrs: { type: \"dollar\" }\n                          }),\n                          _c(\"span\", { staticClass: \"meta-label\" }, [\n                            _vm._v(\"价格：\")\n                          ]),\n                          _c(\n                            \"span\",\n                            { staticClass: \"meta-value price-value\" },\n                            [_vm._v(_vm._s(_vm.getDetailPriceText()))]\n                          )\n                        ],\n                        1\n                      ),\n                      _vm.hasTutorial\n                        ? _c(\n                            \"div\",\n                            { staticClass: \"meta-item tutorial-item\" },\n                            [\n                              _c(\n                                \"span\",\n                                {\n                                  staticClass: \"tutorial-hint\",\n                                  on: { click: _vm.goToTutorial }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n                  本插件有教程视频，详细请点此观看\\n                \"\n                                  )\n                                ]\n                              )\n                            ]\n                          )\n                        : _vm._e()\n                    ]),\n                    _c(\"div\", { staticClass: \"meta-row\" }, [\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\"a-icon\", {\n                            staticClass: \"meta-icon\",\n                            attrs: { type: \"calendar\" }\n                          }),\n                          _c(\"span\", { staticClass: \"meta-label\" }, [\n                            _vm._v(\"发布时间：\")\n                          ]),\n                          _c(\"span\", { staticClass: \"meta-value\" }, [\n                            _vm._v(\n                              _vm._s(\n                                _vm.formatDate(_vm.pluginDetail.createTime)\n                              )\n                            )\n                          ])\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\"a-icon\", {\n                            staticClass: \"meta-icon\",\n                            attrs: { type: \"sync\" }\n                          }),\n                          _c(\"span\", { staticClass: \"meta-label\" }, [\n                            _vm._v(\"更新时间：\")\n                          ]),\n                          _c(\"span\", { staticClass: \"meta-value\" }, [\n                            _vm._v(\n                              _vm._s(\n                                _vm.formatDate(_vm.pluginDetail.updateTime)\n                              )\n                            )\n                          ])\n                        ],\n                        1\n                      )\n                    ])\n                  ])\n                ])\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"plugin-tabs-container\" }, [\n              _c(\"div\", { staticClass: \"custom-tabs-nav\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"tabs-nav-wrapper\" },\n                  _vm._l(_vm.tabList, function(tab, index) {\n                    return _c(\n                      \"div\",\n                      {\n                        key: tab.key,\n                        class: [\n                          \"tab-item\",\n                          { active: _vm.activeTab === tab.key }\n                        ],\n                        on: {\n                          click: function($event) {\n                            return _vm.handleTabClick(tab.key)\n                          }\n                        }\n                      },\n                      [\n                        _c(\"i\", { class: tab.icon }),\n                        _c(\"span\", [_vm._v(_vm._s(tab.label))])\n                      ]\n                    )\n                  }),\n                  0\n                )\n              ]),\n              _c(\"div\", { staticClass: \"custom-tabs-content\" }, [\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.activeTab === \"intro\",\n                        expression: \"activeTab === 'intro'\"\n                      }\n                    ],\n                    staticClass: \"tab-pane\"\n                  },\n                  [\n                    _c(\"plugin-introduction\", {\n                      attrs: {\n                        content: _vm.pluginDetail.plubContent,\n                        info: _vm.pluginDetail.plubinfo,\n                        \"plugin-name\": _vm.pluginDetail.plubname,\n                        \"plugin-detail\": _vm.pluginDetail\n                      }\n                    })\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.activeTab === \"tutorial\",\n                        expression: \"activeTab === 'tutorial'\"\n                      }\n                    ],\n                    staticClass: \"tab-pane\"\n                  },\n                  [\n                    _c(\"plugin-tutorial\", {\n                      attrs: {\n                        \"tutorial-link\": _vm.pluginDetail.tutorialLink,\n                        \"video-file\": _vm.pluginDetail.plubvideo,\n                        \"plugin-name\": _vm.pluginDetail.plubname,\n                        \"detailed-content\": _vm.pluginDetail.plubContent\n                      }\n                    })\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.activeTab === \"features\",\n                        expression: \"activeTab === 'features'\"\n                      }\n                    ],\n                    staticClass: \"tab-pane\"\n                  },\n                  [\n                    _c(\"plugin-features\", {\n                      attrs: {\n                        \"plugin-detail\": _vm.pluginDetail,\n                        category: _vm.pluginDetail.plubCategory\n                      }\n                    })\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  {\n                    directives: [\n                      {\n                        name: \"show\",\n                        rawName: \"v-show\",\n                        value: _vm.activeTab === \"tech\",\n                        expression: \"activeTab === 'tech'\"\n                      }\n                    ],\n                    staticClass: \"tab-pane\"\n                  },\n                  [\n                    _c(\"plugin-technical\", {\n                      attrs: {\n                        \"plugin-detail\": _vm.pluginDetail,\n                        \"plugin-key\": _vm.pluginDetail.pluginKey\n                      }\n                    })\n                  ],\n                  1\n                )\n              ])\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"author-section\" },\n              [\n                _c(\"author-info\", {\n                  attrs: {\n                    author: _vm.authorInfo,\n                    \"plugin-count\": _vm.authorPluginCount\n                  }\n                })\n              ],\n              1\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"recommendations-section\" },\n              [\n                _c(\"related-plugins\", {\n                  attrs: {\n                    recommendations: _vm.recommendations,\n                    \"current-category\": _vm.pluginDetail.plubCategory,\n                    \"current-plugin-id\": _vm.pluginDetail.id\n                  }\n                })\n              ],\n              1\n            )\n          ])\n        : _c(\n            \"div\",\n            { staticClass: \"error-container\" },\n            [\n              _c(\"a-result\", {\n                attrs: {\n                  status: _vm.errorStatus,\n                  title: _vm.errorTitle,\n                  \"sub-title\": _vm.errorMessage\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"extra\",\n                    fn: function() {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"error-actions\" },\n                          [\n                            _vm.canRetry\n                              ? _c(\n                                  \"a-button\",\n                                  {\n                                    attrs: { type: \"primary\" },\n                                    on: { click: _vm.retryLoad }\n                                  },\n                                  [\n                                    _c(\"a-icon\", { attrs: { type: \"reload\" } }),\n                                    _vm._v(\"\\n            重新加载\\n          \")\n                                  ],\n                                  1\n                                )\n                              : _vm._e(),\n                            _c(\n                              \"a-button\",\n                              {\n                                on: {\n                                  click: function($event) {\n                                    return _vm.$router.push(\"/market\")\n                                  }\n                                }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"shop\" } }),\n                                _vm._v(\"\\n            返回商城\\n          \")\n                              ],\n                              1\n                            ),\n                            _c(\n                              \"a-button\",\n                              {\n                                attrs: { type: \"dashed\" },\n                                on: { click: _vm.goHome }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"home\" } }),\n                                _vm._v(\"\\n            返回首页\\n          \")\n                              ],\n                              1\n                            )\n                          ],\n                          1\n                        )\n                      ]\n                    },\n                    proxy: true\n                  }\n                ])\n              })\n            ],\n            1\n          )\n    ])\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}