{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue", "mtime": 1753944273572}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { formatPluginPrice, truncateText, getPluginImageUrl, getPluginStatusClass } from '../utils/marketUtils';\nexport default {\n  name: 'PluginCard',\n  props: {\n    plugin: {\n      type: Object,\n      required: true,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      // 🔥 默认插件图片改为TOS路径（通过后端接口获取）\n      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg',\n      imageLoading: true,\n      disableTransition: true // 初始禁用过渡动画\n\n    };\n  },\n  computed: {\n    // 🔥 判断是否为组合插件\n    isCombinedPlugin: function isCombinedPlugin() {\n      return this.plugin.isCombined === 1 || this.plugin.isCombined === '1';\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n\n    // 页面加载完成后启用过渡动画\n    this.$nextTick(function () {\n      setTimeout(function () {\n        _this.disableTransition = false;\n      }, 100); // 延迟100ms启用动画\n    });\n  },\n  methods: {\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage: function getPluginImage(plugin) {\n      // 使用优化后的工具函数，支持组合插件图片优先级\n      var defaultImg = this.defaultPluginImage;\n      return getPluginImageUrl(plugin, defaultImg);\n    },\n    // 处理图片加载错误\n    handleImageError: function handleImageError(e) {\n      console.log('图片加载失败，使用默认图片:', e.target.src);\n      e.target.src = this.defaultPluginImage;\n      this.imageLoading = false;\n    },\n    // 处理图片加载完成\n    handleImageLoad: function handleImageLoad() {\n      this.imageLoading = false;\n    },\n    // 获取创作者头像\n    getAuthorAvatar: function getAuthorAvatar() {\n      // 可以根据创作者ID获取头像，暂时使用默认头像\n      return require('@/assets/logo.png');\n    },\n    // 获取分类图标\n    getCategoryIcon: function getCategoryIcon(category) {\n      var icons = {\n        '内容生成': '✍️',\n        '图片生成': '🎨',\n        '视频处理': '🎬',\n        '数据分析': '📊',\n        '开发工具': '⚙️',\n        '设计创意': '🎭',\n        '营销工具': '📈'\n      };\n      return icons[category] || '🔧';\n    },\n    // 文本截断\n    truncateText: truncateText,\n    // 格式化价格\n    formatPrice: formatPluginPrice,\n    // 获取状态标识样式类\n    getBadgeClass: getPluginStatusClass,\n    // 🔥 处理插件点击 - 区分组合插件和普通插件\n    handlePluginClick: function handlePluginClick(source) {\n      console.log('🎯 插件点击来源:', source, '插件:', this.isCombinedPlugin ? this.plugin.combinedName : this.plugin.plubname);\n      console.log('🎯 插件数据:', this.plugin);\n      console.log('🎯 是否组合插件:', this.isCombinedPlugin);\n      console.log('🎯 isCombined值:', this.plugin.isCombined, '类型:', _typeof(this.plugin.isCombined));\n\n      if (this.isCombinedPlugin) {\n        // 组合插件：触发组合插件详情事件\n        console.log('🔗 触发组合插件详情事件:', this.plugin);\n        this.$emit('combined-plugin-detail', this.plugin);\n      } else {\n        // 普通插件：触发普通插件详情事件\n        console.log('📄 触发插件详情事件:', this.plugin);\n        this.$emit('plugin-detail', this.plugin);\n      }\n    },\n    // 🔥 获取价格显示文本\n    getPriceText: function getPriceText() {\n      var price = this.plugin.neednum;\n      var isSvipFree = this.plugin.isSvipFree === 1 || this.plugin.isSvipFree === '1';\n\n      if (isSvipFree && price && price > 0) {\n        return \"SVIP\\u514D\\u8D39\\uFF0C\\u4F4E\\u81F3\\xA5\".concat(this.formatPrice(price), \"/\\u6B21\");\n      } else {\n        return \"\\u4F4E\\u81F3\\xA5\".concat(this.formatPrice(price), \"/\\u6B21\");\n      }\n    }\n  }\n};", {"version": 3, "sources": ["PluginCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA,SACA,iBADA,EAEA,YAFA,EAGA,iBAHA,EAIA,oBAJA,QAKA,sBALA;AAOA,eAAA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA,IAFA;AAGA,MAAA,OAAA,EAAA;AAAA,eAAA,EAAA;AAAA;AAHA;AADA,GAFA;AAUA,EAAA,IAVA,kBAUA;AACA,WAAA;AACA;AACA,MAAA,kBAAA,EAAA,2DAFA;AAGA,MAAA,YAAA,EAAA,IAHA;AAIA,MAAA,iBAAA,EAAA,IAJA,CAIA;;AAJA,KAAA;AAMA,GAjBA;AAmBA,EAAA,QAAA,EAAA;AACA;AACA,IAAA,gBAFA,8BAEA;AACA,aAAA,KAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,KAAA,MAAA,CAAA,UAAA,KAAA,GAAA;AACA;AAJA,GAnBA;AA0BA,EAAA,OA1BA,qBA0BA;AAAA;;AACA;AACA,SAAA,SAAA,CAAA,YAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,iBAAA,GAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA,CADA,CAGA;AACA,KAJA;AAKA,GAjCA;AAmCA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,cAFA,0BAEA,MAFA,EAEA;AACA;AACA,UAAA,UAAA,GAAA,KAAA,kBAAA;AACA,aAAA,iBAAA,CAAA,MAAA,EAAA,UAAA,CAAA;AACA,KANA;AAQA;AACA,IAAA,gBATA,4BASA,CATA,EASA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,CAAA,CAAA,MAAA,CAAA,GAAA;AACA,MAAA,CAAA,CAAA,MAAA,CAAA,GAAA,GAAA,KAAA,kBAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAbA;AAeA;AACA,IAAA,eAhBA,6BAgBA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAlBA;AAoBA;AACA,IAAA,eArBA,6BAqBA;AACA;AACA,aAAA,OAAA,CAAA,mBAAA,CAAA;AACA,KAxBA;AA0BA;AACA,IAAA,eA3BA,2BA2BA,QA3BA,EA2BA;AACA,UAAA,KAAA,GAAA;AACA,gBAAA,IADA;AAEA,gBAAA,IAFA;AAGA,gBAAA,IAHA;AAIA,gBAAA,IAJA;AAKA,gBAAA,IALA;AAMA,gBAAA,IANA;AAOA,gBAAA;AAPA,OAAA;AASA,aAAA,KAAA,CAAA,QAAA,CAAA,IAAA,IAAA;AACA,KAtCA;AAwCA;AACA,IAAA,YAAA,EAAA,YAzCA;AA2CA;AACA,IAAA,WAAA,EAAA,iBA5CA;AA8CA;AACA,IAAA,aAAA,EAAA,oBA/CA;AAiDA;AACA,IAAA,iBAlDA,6BAkDA,MAlDA,EAkDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,gBAAA,GAAA,KAAA,MAAA,CAAA,YAAA,GAAA,KAAA,MAAA,CAAA,QAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,gBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,EAAA,KAAA,MAAA,CAAA,UAAA,EAAA,KAAA,UAAA,KAAA,MAAA,CAAA,UAAA;;AAEA,UAAA,KAAA,gBAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,KAAA,MAAA;AACA,aAAA,KAAA,CAAA,wBAAA,EAAA,KAAA,MAAA;AACA,OAJA,MAIA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,KAAA,MAAA;AACA,aAAA,KAAA,CAAA,eAAA,EAAA,KAAA,MAAA;AACA;AACA,KAjEA;AAmEA;AACA,IAAA,YApEA,0BAoEA;AACA,UAAA,KAAA,GAAA,KAAA,MAAA,CAAA,OAAA;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,UAAA,KAAA,CAAA,IAAA,KAAA,MAAA,CAAA,UAAA,KAAA,GAAA;;AAEA,UAAA,UAAA,IAAA,KAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,+DAAA,KAAA,WAAA,CAAA,KAAA,CAAA;AACA,OAFA,MAEA;AACA,yCAAA,KAAA,WAAA,CAAA,KAAA,CAAA;AACA;AACA;AA7EA;AAnCA,CAAA", "sourcesContent": ["<template>\n  <div class=\"plugin-card\" :class=\"{ 'no-transition': disableTransition, 'combined-plugin': isCombinedPlugin }\" @click=\"handlePluginClick('card')\">\n    <!-- 插件图片区域 -->\n    <div class=\"plugin-image\">\n      <div class=\"image-overlay\"></div>\n      <img\n        :src=\"getPluginImage(plugin)\"\n        :alt=\"isCombinedPlugin ? plugin.combinedName : plugin.plubname\"\n        @error=\"handleImageError\"\n        @load=\"handleImageLoad\"\n      />\n\n      <!-- 🔥 组合插件标识 -->\n      <div v-if=\"isCombinedPlugin\" class=\"combined-badge\">\n        <span class=\"badge-icon\">🔗</span>\n        组合插件\n      </div>\n\n      <!-- 分类标签 -->\n      <div class=\"plugin-category\" v-if=\"plugin.plubCategory_dictText\">\n        <span class=\"category-icon\">{{ getCategoryIcon(plugin.plubCategory_dictText) }}</span>\n        {{ plugin.plubCategory_dictText }}\n      </div>\n\n      <!-- 状态标识 -->\n      <div\n        v-if=\"plugin.status_dictText && plugin.status_dictText !== '正常'\"\n        class=\"plugin-badge\"\n        :class=\"getBadgeClass(plugin.status_dictText)\"\n      >\n        {{ plugin.status_dictText }}\n      </div>\n\n      <!-- 图片加载状态 -->\n      <div v-if=\"imageLoading\" class=\"image-loading\">\n        <a-spin size=\"small\" />\n      </div>\n    </div>\n    \n    <!-- 插件信息区域 -->\n    <div class=\"plugin-info\">\n      <!-- 🔥 组合插件信息 -->\n      <div v-if=\"isCombinedPlugin\" class=\"combined-plugin-info\">\n        <!-- 组合插件名称和作者 -->\n        <div class=\"plugin-header-info\">\n          <h3 class=\"plugin-name\" :title=\"plugin.combinedName\">\n            {{ plugin.combinedName }}\n          </h3>\n          <span class=\"plugin-author\" v-if=\"plugin.plubwrite_dictText\">\n            <a-icon type=\"user\" />\n            创作者 {{ plugin.plubwrite_dictText }}\n          </span>\n        </div>\n\n        <!-- 组合插件描述 -->\n        <p class=\"plugin-description\" :title=\"plugin.combinedDescription\">\n          {{ truncateText(plugin.combinedDescription, 100) }}\n        </p>\n\n        <!-- 组合插件底部信息 -->\n        <div class=\"plugin-footer\">\n          <div class=\"plugin-price combined-price\">\n            <span class=\"price-hint\">收费请进入详情查看</span>\n          </div>\n          <a-button\n            type=\"primary\"\n            size=\"default\"\n            @click.stop=\"handlePluginClick('button')\"\n            class=\"detail-button combined-detail-btn\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 🔥 普通插件信息 -->\n      <div v-else class=\"normal-plugin-info\">\n        <!-- 插件名称和作者 -->\n        <div class=\"plugin-header-info\">\n          <h3 class=\"plugin-name\" :title=\"plugin.plubname\">\n            {{ plugin.plubname }}\n          </h3>\n          <span class=\"plugin-author\" v-if=\"plugin.plubwrite_dictText\">\n            <a-icon type=\"user\" />\n            创作者 {{ plugin.plubwrite_dictText }}\n          </span>\n        </div>\n\n        <!-- 插件描述 -->\n        <p class=\"plugin-description\" :title=\"plugin.plubinfo\">\n          {{ truncateText(plugin.plubinfo, 100) }}\n        </p>\n\n        <!-- 价格和操作区域 -->\n        <div class=\"plugin-footer\">\n          <div class=\"plugin-price\">\n            <span class=\"price-value\">{{ getPriceText() }}</span>\n          </div>\n          <a-button\n            type=\"primary\"\n            size=\"default\"\n            @click.stop=\"handlePluginClick('button')\"\n            class=\"detail-button\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  formatPluginPrice,\n  truncateText,\n  getPluginImageUrl,\n  getPluginStatusClass\n} from '../utils/marketUtils'\n\nexport default {\n  name: 'PluginCard',\n  props: {\n    plugin: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    }\n  },\n\n  data() {\n    return {\n      // 🔥 默认插件图片改为TOS路径（通过后端接口获取）\n      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg',\n      imageLoading: true,\n      disableTransition: true // 初始禁用过渡动画\n    }\n  },\n\n  computed: {\n    // 🔥 判断是否为组合插件\n    isCombinedPlugin() {\n      return this.plugin.isCombined === 1 || this.plugin.isCombined === '1'\n    }\n  },\n\n  mounted() {\n    // 页面加载完成后启用过渡动画\n    this.$nextTick(() => {\n      setTimeout(() => {\n        this.disableTransition = false\n      }, 100) // 延迟100ms启用动画\n    })\n  },\n\n  methods: {\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage(plugin) {\n      // 使用优化后的工具函数，支持组合插件图片优先级\n      const defaultImg = this.defaultPluginImage\n      return getPluginImageUrl(plugin, defaultImg)\n    },\n    \n    // 处理图片加载错误\n    handleImageError(e) {\n      console.log('图片加载失败，使用默认图片:', e.target.src)\n      e.target.src = this.defaultPluginImage\n      this.imageLoading = false\n    },\n    \n    // 处理图片加载完成\n    handleImageLoad() {\n      this.imageLoading = false\n    },\n    \n    // 获取创作者头像\n    getAuthorAvatar() {\n      // 可以根据创作者ID获取头像，暂时使用默认头像\n      return require('@/assets/logo.png')\n    },\n\n    // 获取分类图标\n    getCategoryIcon(category) {\n      const icons = {\n        '内容生成': '✍️',\n        '图片生成': '🎨',\n        '视频处理': '🎬',\n        '数据分析': '📊',\n        '开发工具': '⚙️',\n        '设计创意': '🎭',\n        '营销工具': '📈'\n      }\n      return icons[category] || '🔧'\n    },\n\n    // 文本截断\n    truncateText,\n\n    // 格式化价格\n    formatPrice: formatPluginPrice,\n\n    // 获取状态标识样式类\n    getBadgeClass: getPluginStatusClass,\n    \n    // 🔥 处理插件点击 - 区分组合插件和普通插件\n    handlePluginClick(source) {\n      console.log('🎯 插件点击来源:', source, '插件:', this.isCombinedPlugin ? this.plugin.combinedName : this.plugin.plubname)\n      console.log('🎯 插件数据:', this.plugin)\n      console.log('🎯 是否组合插件:', this.isCombinedPlugin)\n      console.log('🎯 isCombined值:', this.plugin.isCombined, '类型:', typeof this.plugin.isCombined)\n\n      if (this.isCombinedPlugin) {\n        // 组合插件：触发组合插件详情事件\n        console.log('🔗 触发组合插件详情事件:', this.plugin)\n        this.$emit('combined-plugin-detail', this.plugin)\n      } else {\n        // 普通插件：触发普通插件详情事件\n        console.log('📄 触发插件详情事件:', this.plugin)\n        this.$emit('plugin-detail', this.plugin)\n      }\n    },\n\n    // 🔥 获取价格显示文本\n    getPriceText() {\n      const price = this.plugin.neednum\n      const isSvipFree = this.plugin.isSvipFree === 1 || this.plugin.isSvipFree === '1'\n\n      if (isSvipFree && price && price > 0) {\n        return `SVIP免费，低至¥${this.formatPrice(price)}/次`\n      } else {\n        return `低至¥${this.formatPrice(price)}/次`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.plugin-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.08);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e2e8f0;\n  position: relative;\n}\n\n.plugin-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.plugin-card:hover {\n  box-shadow: 0 8px 25px rgba(0,0,0,0.12);\n  border-color: #3b82f6;\n}\n\n.plugin-card:hover::before {\n  opacity: 1;\n}\n\n/* 禁用过渡动画的类 */\n.plugin-card.no-transition,\n.plugin-card.no-transition *,\n.plugin-card.no-transition::before {\n  transition: none !important;\n}\n\n/* 图片区域 */\n.plugin-image {\n  position: relative;\n  height: 180px;\n  overflow: hidden;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  z-index: 1;\n}\n\n.plugin-card:hover .image-overlay {\n  opacity: 1;\n}\n\n.plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.1);\n  filter: brightness(1.1);\n}\n\n/* 悬浮操作按钮样式已移除 */\n\n/* 🔥 组合插件卡片样式 */\n.plugin-card.combined-plugin {\n  border: 2px solid #f59e0b;\n  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.15);\n}\n\n.plugin-card.combined-plugin:hover {\n  border-color: #d97706;\n  box-shadow: 0 12px 40px rgba(245, 158, 11, 0.25);\n}\n\n/* 🔥 组合插件标识 */\n.combined-badge {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: linear-gradient(135deg, #f59e0b, #d97706);\n  color: white;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\n  z-index: 4; /* 🔥 提高层级，确保覆盖分类标签 */\n  animation: pulse 2s infinite;\n  min-width: 80px; /* 🔥 设置最小宽度 */\n  justify-content: center; /* 🔥 居中对齐 */\n}\n\n.badge-icon {\n  font-size: 0.8rem;\n}\n\n@keyframes pulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n/* 🔥 组合插件价格提示 */\n.combined-price {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.price-hint {\n  color: #f59e0b;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-align: center;\n  padding: 0.25rem 0.5rem;\n  background: rgba(245, 158, 11, 0.1);\n  border-radius: 8px;\n  border: 1px solid rgba(245, 158, 11, 0.2);\n}\n\n/* 🔥 组合插件详情按钮 */\n.combined-detail-btn {\n  background: linear-gradient(135deg, #f59e0b, #d97706);\n  border: none;\n  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);\n}\n\n.combined-detail-btn:hover {\n  background: linear-gradient(135deg, #d97706, #b45309);\n  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);\n}\n\n.plugin-category {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  color: #3b82f6;\n  padding: 0.5rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  z-index: 2;\n}\n\n/* 🔥 组合插件时隐藏分类标签，避免重叠 */\n.plugin-card.combined-plugin .plugin-category {\n  display: none;\n}\n\n.category-icon {\n  font-size: 0.8rem;\n}\n\n.plugin-badge {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.badge-recommend {\n  background: linear-gradient(135deg, #ff6b6b, #ffa726);\n}\n\n.badge-hot {\n  background: linear-gradient(135deg, #ff4757, #ff3838);\n}\n\n.badge-new {\n  background: linear-gradient(135deg, #5f27cd, #341f97);\n}\n\n.badge-default {\n  background: linear-gradient(135deg, #74b9ff, #0984e3);\n}\n\n.image-loading {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(255, 255, 255, 0.9);\n  padding: 8px;\n  border-radius: 50%;\n}\n\n/* 信息区域 */\n.plugin-info {\n  padding: 1.5rem;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);\n}\n\n.plugin-header-info {\n  margin-bottom: 1rem;\n}\n\n.plugin-name {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n  line-height: 1.3;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.plugin-author {\n  font-size: 0.8rem;\n  color: #64748b;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.plugin-description {\n  color: #64748b;\n  font-size: 0.9rem;\n  line-height: 1.6;\n  margin: 0 0 1.5rem 0;\n  flex: 1;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.plugin-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: auto;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n}\n\n.plugin-price {\n  display: flex;\n  align-items: baseline;\n  gap: 0.25rem;\n}\n\n.price-value {\n  font-size: 1.4rem;\n  font-weight: 800;\n  background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n\n.price-unit {\n  font-size: 0.8rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.detail-button {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  height: 40px;\n  padding: 0 1.5rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n}\n\n.detail-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n  background: linear-gradient(135deg, #1d4ed8, #1e40af);\n}\n\n.detail-button:active {\n  transform: translateY(0);\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .plugin-image {\n    height: 150px;\n  }\n  \n  .plugin-info {\n    padding: 0.75rem;\n  }\n  \n  .plugin-name {\n    font-size: 1rem;\n  }\n  \n  .plugin-description {\n    font-size: 0.85rem;\n  }\n  \n  .plugin-footer {\n    flex-direction: column;\n    gap: 0.5rem;\n    align-items: stretch;\n  }\n  \n  .detail-button {\n    width: 100%;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/market/components"}]}