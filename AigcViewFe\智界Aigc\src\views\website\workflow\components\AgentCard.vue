<template>
  <div class="agent-card" @click="handleCardClick">
    <!-- SVIP推广标签 -->
    <div v-if="agent.showSvipPromo && agent.authorType === '1'" class="svip-promo-tag svip-free">
      SVIP免费
    </div>
    <div v-if="agent.showSvipPromo && agent.authorType === '2'" class="svip-promo-tag svip-discount">
      SVIP 5折
    </div>

    <!-- 已购买标签 -->
    <div v-if="agent.isPurchased" class="purchased-tag">
      <a-icon type="check-circle" />
      <span>已购买</span>
    </div>

    <!-- 智能体封面 -->
    <div class="agent-cover">
      <div class="cover-image">
        <!-- 如果有视频，显示视频 -->
        <video
          v-if="agent.demoVideo"
          :src="agent.demoVideo"
          muted
          loop
          preload="metadata"
          @mouseenter="handleVideoHover"
          @mouseleave="handleVideoLeave"
          @loadedmetadata="handleVideoLoaded"
          class="cover-video"
        ></video>
        <!-- 如果没有视频，显示图片 -->
        <img
          v-else-if="agent.agentAvatar"
          :src="agent.agentAvatar"
          :alt="agent.agentName"
          @error="handleImageError"
          class="cover-image-img"
        />
        <!-- 默认占位符 -->
        <div v-else class="cover-placeholder">
          <a-icon type="robot" />
        </div>
      </div>

      <!-- 作者类型标签 -->
      <div class="author-type-tag" :class="authorTypeClass">
        <a-icon :type="authorTypeIcon" />
        <span>{{ authorTypeText }}</span>
      </div>

      <!-- VIP折扣标签 -->
      <div v-if="agent.showDiscountPrice && agent.discountRate === 0.7" class="vip-discount-tag">
        <a-icon type="crown" />
        <span>VIP 7折</span>
      </div>

      <!-- SVIP免费标签 -->
      <div v-if="agent.isFree" class="svip-free-tag">
        <a-icon type="crown" />
        <span>SVIP 免费</span>
      </div>

      <!-- SVIP折扣标签 -->
      <div v-else-if="agent.showDiscountPrice && agent.discountRate === 0.5" class="svip-discount-tag">
        <a-icon type="crown" />
        <span>SVIP 5折</span>
      </div>

    </div>

    <!-- 智能体信息 -->
    <div class="agent-info">
      <div class="agent-header">
        <h4 class="agent-name" :title="agent.agentName">
          {{ agent.agentName }}
        </h4>
        <div class="agent-price">
          <!-- 显示免费 -->
          <div v-if="agent.isFree" class="price-container">
            <span class="free-price">免费</span>
          </div>
          <!-- 显示折扣价格 -->
          <div v-else-if="agent.showDiscountPrice" class="price-container">
            <span class="discount-price">¥{{ Math.round((agent.originalPrice || 0) * agent.discountRate) }}</span>
            <span class="original-price">¥{{ agent.originalPrice || 0 }}</span>
          </div>
          <!-- 显示原价 -->
          <div v-else class="price-container">
            <span class="current-price">¥{{ agent.originalPrice || 0 }}</span>
          </div>
        </div>
      </div>

      <div class="agent-description" v-if="agent.description">
        {{ agent.description }}
      </div>

      <div class="agent-meta">
        <span class="creator-info">
          <div class="creator-avatar">
            <img
              v-if="creatorAvatar"
              :src="creatorAvatar"
              :alt="creatorName"
              @error="handleCreatorAvatarError"
            />
            <a-icon v-else type="user" />
          </div>
          <span class="creator-name">{{ creatorName }}</span>
        </span>
        <span class="workflow-count">
          <a-icon type="deployment-unit" />
          {{ workflowCount }}个工作流
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgentCard',
  props: {
    agent: {
      type: Object,
      required: true
    }
  },
  computed: {

    // 作者类型样式类
    authorTypeClass() {
      return {
        'official': this.agent.authorType === '1',
        'creator': this.agent.authorType === '2'
      }
    },

    // 作者类型图标
    authorTypeIcon() {
      return this.agent.authorType === '1' ? 'crown' : 'user'
    },

    // 作者类型文本
    authorTypeText() {
      return this.agent.authorType === '1' ? '官方' : '创作者'
    },

    // 价格标签样式类
    priceTagClass() {
      return {
        'free': this.agent.isFree,
        'discount': this.agent.hasDiscount && !this.agent.isFree,
        'normal': !this.agent.hasDiscount && !this.agent.isFree
      }
    },

    // 创作者名称
    creatorName() {
      return this.agent.creatorName || this.agent.createBy || '未知创作者'
    },

    // 创作者头像
    creatorAvatar() {
      return this.agent.creatorAvatar || ''
    },

    // 工作流数量
    workflowCount() {
      return this.agent.workflowCount || 0
    }
  },
  methods: {
    // 处理卡片点击
    handleCardClick() {
      this.$emit('view-detail', this.agent)
    },

    // 处理查看详情
    handleViewDetail() {
      this.$emit('view-detail', this.agent)
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn('智能体封面加载失败:', this.agent.agentName)
      // 隐藏错误的图片，显示占位符
      event.target.style.display = 'none'
    },

    // 处理创作者头像加载错误
    handleCreatorAvatarError(event) {
      console.warn('创作者头像加载失败:', this.creatorName)
      // 隐藏错误的头像，显示默认图标
      event.target.style.display = 'none'
    },

    // 视频悬停播放
    handleVideoHover(event) {
      event.target.play().catch(err => {
        console.warn('视频播放失败:', err)
      })
    },

    // 视频离开暂停
    handleVideoLeave(event) {
      event.target.pause()
      event.target.currentTime = 0
    },

    // 视频元数据加载完成，设置为第一帧
    handleVideoLoaded(event) {
      // 设置视频到第一帧作为封面
      event.target.currentTime = 0
    }
  }
}
</script>

<style scoped>
.agent-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #f1f5f9;
  position: relative; /* 为SVIP标签提供定位基准 */
}

.agent-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

/* 智能体封面 */
.agent-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.cover-image {
  width: 100%;
  height: 100%;
  position: relative;
}

.cover-image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.cover-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.agent-card:hover .cover-image-img,
.agent-card:hover .cover-video {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-size: 3rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* 作者类型标签 */
.author-type-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.author-type-tag.official {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.author-type-tag.creator {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

/* VIP折扣标签 */
.vip-discount-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #7c3aed 0%, #6366f1 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.4);
}

/* SVIP免费标签 */
.svip-free-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

/* SVIP折扣标签 */
.svip-discount-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}



.free-price {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 600;
}

.promo-discount .discount-price {
  font-size: 0.875rem;
  color: #f59e0b;
  font-weight: 600;
}

/* 智能体信息 */
.agent-info {
  padding: 1.5rem;
}

.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.agent-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.agent-price {
  margin-left: 1rem;
  flex-shrink: 0;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
  min-height: 2.5rem; /* 固定高度确保对齐 */
  justify-content: center; /* 垂直居中 */
}

.discount-price {
  font-size: 1rem;
  font-weight: 600;
  color: #dc2626;
}

.original-price {
  font-size: 0.875rem;
  font-weight: 400;
  color: #9ca3af;
  text-decoration: line-through;
}

.current-price {
  font-size: 1rem;
  font-weight: 600;
  color: #059669;
}

.free-price {
  font-size: 1rem;
  font-weight: 600;
  color: #8b5cf6;
}

.agent-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 1rem;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.creator-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creator-avatar .anticon {
  font-size: 12px;
  color: #94a3b8;
}

.creator-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.workflow-count {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.agent-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.agent-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

.agent-actions {
  display: flex;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-cover {
    height: 160px;
  }

  .agent-info {
    padding: 1rem;
  }

  .agent-name {
    font-size: 1rem;
  }

  .agent-meta {
    font-size: 0.8rem;
  }

  .agent-footer {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .agent-stats {
    justify-content: center;
  }
}

/* SVIP推广标签 */
.svip-promo-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.2s ease;
}

.svip-promo-tag.svip-free {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

.svip-promo-tag.svip-discount {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.svip-promo-tag:hover {
  transform: scale(1.05);
}

/* 已购买标签 */
.purchased-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
  padding: 0.4rem 0.6rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.purchased-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.5);
}

.purchased-tag .anticon {
  font-size: 0.7rem;
}
</style>
