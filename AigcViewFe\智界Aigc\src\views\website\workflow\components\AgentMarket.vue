<template>
  <div class="agent-market">
    <!-- 固定的搜索和筛选区域 -->
    <div class="sticky-filters">
      <div class="market-filters">
        <div class="filter-row">
          <!-- 搜索框 -->
          <div class="search-box">
            <div class="search-wrapper">
              <a-icon type="search" class="search-icon" />
              <a-input
                v-model="searchQuery"
                placeholder="搜索智能体名称、描述或标签..."
                size="large"
                @pressEnter="handleSearch"
                @input="handleSearch"
                class="search-input"
              />
              <a-icon
                v-if="searchQuery"
                type="close-circle"
                class="clear-icon"
                @click="clearSearch"
              />
            </div>
          </div>

          <!-- 筛选器 -->
          <div class="filter-controls">
            <div class="filter-item-inline">
              <span class="filter-label">作者类型：</span>
              <a-select
                v-model="authorTypeFilter"
                placeholder="全部类型"
                size="large"
                class="filter-select"
                @change="handleFilterChange"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="1">
                  <a-icon type="crown" style="color: #f59e0b; margin-right: 4px;" />
                  官方
                </a-select-option>
                <a-select-option value="2">
                  <a-icon type="user" style="color: #3b82f6; margin-right: 4px;" />
                  创作者
                </a-select-option>
              </a-select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="agent-list" v-if="!loading">
      <div class="list-header">
        <h3 class="list-title">
          智能体列表
          <span class="list-count">({{ totalCount }}个)</span>
        </h3>
      </div>

      <!-- 智能体卡片网格 -->
      <div class="agent-grid" v-if="agentList.length > 0">
        <AgentCard
          v-for="agent in agentList"
          :key="agent.id"
          :agent="agent"
          @view-detail="handleViewDetail"
        />
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <a-empty
          description="暂无智能体数据"
        >
          <a-button type="primary" @click="handleRefresh">
            <a-icon type="reload" />
            刷新数据
          </a-button>
        </a-empty>
      </div>

      <!-- 加载更多提示 -->
      <div class="load-more-wrapper" v-if="agentList.length > 0">
        <div v-if="loadingMore" class="loading-more">
          <a-spin size="small" />
          <span>正在加载更多...</span>
        </div>
        <div v-else-if="hasMore" class="load-more-trigger" ref="loadMoreTrigger">
          <!-- 滚动到这里触发加载更多 -->
        </div>
        <div v-else class="no-more-data">
          <a-icon type="check-circle" />
          <span>已加载全部数据 (共{{ totalCount }}个)</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-state">
      <a-spin size="large" tip="正在加载智能体数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 智能体详情弹窗 -->
    <AgentDetailModal
      :visible="detailModalVisible"
      :agentId="selectedAgentId"
      :isPurchased="isSelectedAgentPurchased"
      @close="handleCloseDetailModal"
      @purchase="handlePurchaseFromModal"
      @purchase-success="handlePurchaseSuccess"
    />
  </div>
</template>

<script>
import AgentCard from './AgentCard.vue'
import AgentDetailModal from './AgentDetailModal.vue'
import { getUserRole } from '@/utils/roleUtils'

export default {
  name: 'AgentMarket',
  components: {
    AgentCard,
    AgentDetailModal
  },
  data() {
    return {
      loading: false,
      loadingMore: false,
      searchQuery: '',
      authorTypeFilter: '',
      agentList: [],
      userRole: 'user', // 用户角色
      currentPage: 1,
      pageSize: 16,
      totalCount: 0,
      hasMore: true,
      // 详情弹窗相关
      detailModalVisible: false, // 详情弹窗显示状态
      selectedAgentId: '', // 选中的智能体ID
      selectedAgent: null, // 选中的智能体数据
      purchasedAgents: [] // 已购买的智能体ID列表
    }
  },
  computed: {
    // 检查选中的智能体是否已购买
    isSelectedAgentPurchased() {
      return this.selectedAgentId && this.purchasedAgents.includes(this.selectedAgentId)
    }
  },
  async mounted() {
    await this.loadUserRole()
    await this.loadPurchasedAgents()
    await this.loadAgentList()
    this.setupIntersectionObserver()
    this.initDebounceSearch()
  },

  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    // 加载用户角色
    async loadUserRole() {
      try {
        const role = await getUserRole()
        this.userRole = role
        console.log('🔍 AgentMarket: 用户角色:', this.userRole)
      } catch (error) {
        console.error('获取用户角色失败:', error)
        this.userRole = null
      }
    },

    // 加载智能体列表（首次加载或搜索时重置）
    async loadAgentList(reset = true) {
      if (reset) {
        this.loading = true
        this.currentPage = 1
        this.agentList = []
        this.hasMore = true
      } else {
        this.loadingMore = true
      }

      try {
        const params = {
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          agentName: this.searchQuery || undefined,
          authorType: this.authorTypeFilter || undefined,
          auditStatus: '2' // 只显示审核通过的
        }

        // 调用后端API
        const response = await this.$http.get('/api/agent/market/list', { params })
        console.log('API响应:', response)

        // 兼容不同的响应格式
        const data = response.data || response
        if (data && data.success) {
          const newRecords = data.result.records || []

          // 对每个智能体添加价格信息和购买状态
          const processedRecords = newRecords.map(agent => {
            const priceInfo = this.calculatePrice(agent)
            const isPurchased = this.isAgentPurchased(agent.id)
            return {
              ...agent,
              ...priceInfo,
              isPurchased
            }
          })

          if (reset) {
            this.agentList = processedRecords
          } else {
            this.agentList.push(...processedRecords)
          }

          this.totalCount = data.result.total || 0

          // 判断是否还有更多数据
          this.hasMore = this.agentList.length < this.totalCount

          // 如果有数据，准备下一页
          if (newRecords.length > 0) {
            this.currentPage++
          }
        } else {
          this.$message.error((data && data.message) || '获取智能体列表失败')
        }
      } catch (error) {
        console.error('加载智能体列表失败:', error)
        this.$message.error('加载智能体列表失败，请稍后重试')
      } finally {
        this.loading = false
        this.loadingMore = false

        // 重新设置IntersectionObserver，确保监听新的DOM元素
        if (reset) {
          this.$nextTick(() => {
            this.setupIntersectionObserver()
          })
        }
      }
    },

    // 临时模拟数据
    async loadMockData() {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      this.agentList = []
      this.totalCount = 0
      this.hasMore = false
    },

    // 加载更多数据
    async loadMore() {
      if (!this.hasMore || this.loadingMore) {
        return
      }
      await this.loadAgentList(false)
    },

    // 计算价格和推广标签显示
    calculatePrice(agent) {
      let showSvipPromo = false
      let showDiscountPrice = false
      let discountRate = 1 // 默认无折扣
      let isFree = false // 是否免费

      // 根据用户角色计算价格和推广显示
      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {
        // 未登录、普通用户或管理员：显示SVIP推广标签
        showSvipPromo = true
        showDiscountPrice = false
      } else if (this.userRole === 'VIP') {
        // VIP用户：显示7折价格，不显示推广标签
        showSvipPromo = false
        showDiscountPrice = true
        discountRate = 0.7 // VIP 7折
      } else if (this.userRole === 'SVIP') {
        // SVIP用户：根据作者类型计算价格
        showSvipPromo = false

        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {
          // 官方智能体：免费
          isFree = true
          discountRate = 0
          showDiscountPrice = false // 免费时不显示折扣价格
        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {
          // 创作者智能体：5折
          isFree = false
          showDiscountPrice = true
          discountRate = 0.5
        }
      }

      console.log('🔍 calculatePrice [NEW VERSION]: 智能体名称:', agent && agent.agentName, '用户角色:', this.userRole, '作者类型:', agent && agent.authorType, '显示SVIP推广:', showSvipPromo, '显示折扣价:', showDiscountPrice, '折扣率:', discountRate, '是否免费:', isFree)

      return {
        showSvipPromo,
        showDiscountPrice,
        discountRate,
        isFree
      }
    },

    // 搜索处理
    handleSearch() {
      this.scrollToTop()
      this.loadAgentList(true)
    },

    // 筛选变化处理
    handleFilterChange() {
      this.scrollToTop()
      this.loadAgentList(true)
    },

    // 滚动到顶部
    scrollToTop() {
      // 滚动到页面顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 设置滚动监听
    setupIntersectionObserver() {
      // 先断开旧的observer
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }

      this.$nextTick(() => {
        const target = this.$refs.loadMoreTrigger
        if (!target) return

        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && this.hasMore && !this.loadingMore) {
              console.log('触发懒加载，hasMore:', this.hasMore, 'loadingMore:', this.loadingMore)
              this.loadMore()
            }
          })
        }, {
          rootMargin: '100px' // 提前100px开始加载
        })

        this.observer.observe(target)
        console.log('IntersectionObserver已重新设置')
      })
    },

    // 查看详情
    handleViewDetail(agent) {
      console.log('查看智能体详情:', agent)
      this.selectedAgent = agent
      this.selectedAgentId = agent.id
      this.detailModalVisible = true
    },

    // 关闭详情弹窗
    handleCloseDetailModal() {
      this.detailModalVisible = false
      this.selectedAgent = null
      this.selectedAgentId = ''
    },

    // 从弹窗发起购买
    handlePurchaseFromModal(agent) {
      console.log('从弹窗购买智能体:', agent)
      // 购买逻辑已在AgentDetailModal中实现
    },

    // 购买成功回调
    handlePurchaseSuccess(agentId) {
      console.log('购买成功回调:', agentId)
      this.onPurchaseSuccess(agentId)
      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了')
    },

    // 加载已购买的智能体列表
    async loadPurchasedAgents() {
      try {
        // 检查是否已登录
        const token = this.$store.getters.token
        if (!token) {
          console.log('用户未登录，跳过购买状态检查')
          return
        }

        // 检查缓存时效性（5分钟）
        const cacheKey = 'purchasedAgents'
        const cacheTimeKey = 'purchasedAgentsTime'
        const cacheTime = localStorage.getItem(cacheTimeKey)
        const now = Date.now()
        const cacheExpiry = 5 * 60 * 1000 // 5分钟

        // 从缓存中获取
        const cached = localStorage.getItem(cacheKey)
        if (cached && cacheTime && (now - parseInt(cacheTime)) < cacheExpiry) {
          try {
            this.purchasedAgents = JSON.parse(cached)
            console.log('🔍 从缓存加载已购买智能体:', this.purchasedAgents.length, '个')
            return // 缓存有效，直接返回
          } catch (e) {
            console.warn('购买状态缓存解析失败:', e)
            localStorage.removeItem(cacheKey)
            localStorage.removeItem(cacheTimeKey)
          }
        }

        // 缓存过期或不存在，从后端获取
        // TODO: 调用后端API获取最新的购买状态
        // const response = await this.$http.get('/api/agent/purchase/list')
        // if (response.data && response.data.success) {
        //   this.purchasedAgents = response.data.result || []
        //   localStorage.setItem(cacheKey, JSON.stringify(this.purchasedAgents))
        //   localStorage.setItem(cacheTimeKey, now.toString())
        //   console.log('✅ 购买状态更新成功:', this.purchasedAgents.length, '个')
        // }
      } catch (error) {
        console.error('❌ 加载购买状态失败:', error)
      }
    },

    // 检查智能体是否已购买
    isAgentPurchased(agentId) {
      return this.purchasedAgents.includes(agentId)
    },

    // 购买成功后更新状态
    onPurchaseSuccess(agentId) {
      if (!this.purchasedAgents.includes(agentId)) {
        this.purchasedAgents.push(agentId)

        // 更新缓存和时间戳
        const cacheKey = 'purchasedAgents'
        const cacheTimeKey = 'purchasedAgentsTime'
        localStorage.setItem(cacheKey, JSON.stringify(this.purchasedAgents))
        localStorage.setItem(cacheTimeKey, Date.now().toString())

        console.log('✅ 购买状态已更新:', agentId)

        // 更新智能体列表中的购买状态
        this.agentList.forEach(agent => {
          if (agent.id === agentId) {
            agent.isPurchased = true
          }
        })
      }
    },

    // 防抖搜索
    debounceSearch: null,

    // 初始化防抖搜索
    initDebounceSearch() {
      this.debounceSearch = this.debounce(() => {
        this.handleSearch()
      }, 300)
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 刷新数据
    handleRefresh() {
      this.loadAgentList()
    },

    // 获取创作者数量
    getCreatorCount() {
      return this.agentList.filter(agent => agent.authorType === '2').length
    },

    // 清空搜索
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    }
  }
}
</script>

<style scoped>
.agent-market {
  padding: 0;
}

/* 固定的搜索和筛选区域 */
.sticky-filters {
  position: sticky;
  top: 156px; /* 顶部导航栏100px + tab栏60px */
  z-index: 100;
  background: white;
  padding: 1rem 0;
  margin-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.market-filters {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);
  max-width: 1600px;
  margin: 0 auto;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.search-box {
  flex: 1;
  max-width: 600px;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 2px solid #cbd5e1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-wrapper:hover {
  border-color: #3b82f6;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.search-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  color: #64748b;
  font-size: 18px;
  transition: color 0.3s ease;
}

.search-wrapper:focus-within .search-icon {
  color: #3b82f6;
}

.clear-icon {
  position: absolute;
  right: 16px;
  z-index: 2;
  color: #94a3b8;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.clear-icon:hover {
  color: #64748b;
}

.search-input {
  flex: 1;
  border: none !important;
  box-shadow: none !important;
  padding-left: 48px !important;
  padding-right: 48px !important;
  font-size: 16px;
  height: 48px;
  background: transparent;
}

.search-input:focus {
  border: none !important;
  box-shadow: none !important;
}

.filter-controls {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.filter-item-inline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.filter-select {
  min-width: 160px;
}

.filter-select .ant-select-selector {
  border-radius: 12px !important;
  border: 2px solid #cbd5e1 !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: white !important;
  height: 48px !important;
}

.filter-select .ant-select-selection-item {
  line-height: 44px !important;
  font-weight: 500 !important;
  color: #374151 !important;
}

.filter-select:hover .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-1px) !important;
}

.filter-select.ant-select-focused .ant-select-selector,
.filter-select.ant-select-open .ant-select-selector {
  border-color: #3b82f6 !important;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px) !important;
}

.filter-select .ant-select-arrow {
  color: #64748b !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
}

.filter-select:hover .ant-select-arrow,
.filter-select.ant-select-focused .ant-select-arrow,
.filter-select.ant-select-open .ant-select-arrow {
  color: #3b82f6 !important;
  transform: scale(1.1) !important;
}

/* 下拉菜单样式 */
.filter-select .ant-select-dropdown {
  border-radius: 12px !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid #e2e8f0 !important;
  overflow: hidden !important;
}

.filter-select .ant-select-item {
  padding: 12px 16px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.filter-select .ant-select-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  color: #3b82f6 !important;
}

.filter-select .ant-select-item-option-selected {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
  font-weight: 600 !important;
}

.filter-select .ant-select-item-option-selected:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
}

/* 智能体列表 */
.agent-list {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.list-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.list-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.list-count {
  color: #64748b;
  font-weight: 400;
  font-size: 1rem;
}

/* 智能体网格 */
.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

/* 空状态 */
.empty-state {
  padding: 4rem 2rem;
  text-align: center;
}

/* 懒加载相关样式 */
.load-more-wrapper {
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: center;
  max-width: 1600px;
  margin: 0 auto;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
}

.load-more-trigger {
  height: 20px;
  width: 100%;
}

.no-more-data {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.no-more-data .anticon {
  color: #10b981;
}

/* 加载状态 */
.loading-state {
  padding: 4rem 2rem;
  text-align: center;
}

.loading-placeholder {
  height: 400px;
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 1rem;
  }

  .search-box {
    width: 100%;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }

  .agent-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .market-filters,
  .list-header,
  .load-more-wrapper {
    padding: 1rem;
  }
}
</style>
