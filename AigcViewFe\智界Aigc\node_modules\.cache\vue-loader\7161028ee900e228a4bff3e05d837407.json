{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue?vue&type=template&id=81cbcc72&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue", "mtime": 1753968167936}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-spin :spinning=\"confirmLoading\">\n  <j-form-container :disabled=\"formDisabled\">\n    <!-- 主表单区域 -->\n    <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\n      <a-row>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"作者类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"authorType\">\n            <j-dict-select-tag type=\"list\" v-model=\"model.authorType\" dictCode=\"author_type\" placeholder=\"请选择作者类型\" />\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"智能体ID\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentId\">\n            <a-input v-model=\"model.agentId\" placeholder=\"请输入智能体ID\" ></a-input>\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"智能体名称\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentName\">\n            <a-input v-model=\"model.agentName\" placeholder=\"请输入智能体名称\" ></a-input>\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"24\">\n          <a-form-model-item label=\"智能体描述\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"agentDescription\">\n            <a-textarea v-model=\"model.agentDescription\" rows=\"4\" placeholder=\"请输入智能体描述\" />\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"智能体头像\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentAvatar\">\n            <j-image-upload-deferred\n              ref=\"avatarUpload\"\n              v-model=\"model.agentAvatar\"\n              :isMultiple=\"false\"\n              bizPath=\"agent-avatar\"\n              text=\"上传头像\">\n            </j-image-upload-deferred>\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"展示视频\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"demoVideo\">\n            <j-upload v-model=\"model.demoVideo\" :beforeUpload=\"beforeVideoUpload\" text=\"上传视频(最大100MB)\"></j-upload>\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"体验链接\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"experienceLink\">\n            <a-input v-model=\"model.experienceLink\" placeholder=\"请输入体验链接\" ></a-input>\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"价格（元）\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"price\">\n            <a-input-number v-model=\"model.price\" placeholder=\"请输入价格（元）\" style=\"width: 100%\" />\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"8\" >\n          <a-form-model-item label=\"审核状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"auditStatus\">\n            <j-dict-select-tag type=\"list\" v-model=\"model.auditStatus\" dictCode=\"audit_status\" placeholder=\"请选择审核状态\" />\n          </a-form-model-item>\n        </a-col>\n        <a-col :span=\"24\">\n          <a-form-model-item label=\"审核备注\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"auditRemark\">\n            <a-textarea v-model=\"model.auditRemark\" rows=\"4\" placeholder=\"请输入审核备注\" />\n          </a-form-model-item>\n        </a-col>\n      </a-row>\n    </a-form-model>\n  </j-form-container>\n    <!-- 子表单区域 -->\n  <a-tabs v-model=\"activeKey\" @change=\"handleChangeTabs\">\n    <a-tab-pane tab=\"工作流表\" :key=\"refKeys[0]\" :forceRender=\"true\">\n      <j-editable-table\n        :ref=\"refKeys[0]\"\n        :loading=\"aigcWorkflowTable.loading\"\n        :columns=\"aigcWorkflowTable.columns\"\n        :dataSource=\"aigcWorkflowTable.dataSource\"\n        :maxHeight=\"300\"\n        :disabled=\"formDisabled\"\n        :rowNumber=\"true\"\n        :rowSelection=\"true\"\n        :actionButton=\"true\"/>\n    </a-tab-pane>\n  </a-tabs>\n</a-spin>\n", null]}