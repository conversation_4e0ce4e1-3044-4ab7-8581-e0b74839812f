{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue?vue&type=template&id=72112de5&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\plubshop\\AigcPlubShopList.vue", "mtime": 1753947183493}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-card :bordered=\"false\">\n  <!-- 查询区域 -->\n  <div class=\"table-page-search-wrapper\">\n    <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n      <a-row :gutter=\"24\">\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"插件名称\">\n            <a-input placeholder=\"请输入插件名称\" v-model=\"queryParam.plubname\"></a-input>\n          </a-form-item>\n        </a-col>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"插件创作者\">\n            <j-dict-select-tag placeholder=\"请选择插件创作者\" v-model=\"queryParam.plubwrite\" dictCode=\"aigc_plub_author,authorname,id\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"插件状态\">\n            <j-dict-select-tag placeholder=\"请选择插件状态\" v-model=\"queryParam.status\" dictCode=\"plugin_status\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"是否组合插件\">\n            <j-dict-select-tag placeholder=\"请选择是否组合插件\" v-model=\"queryParam.isCombined\" dictCode=\"isTrue\"/>\n          </a-form-item>\n        </a-col>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <a-form-item label=\"是否SVIP免费\">\n            <j-dict-select-tag placeholder=\"请选择是否SVIP免费\" v-model=\"queryParam.isSvipFree\" dictCode=\"isTrue\"/>\n          </a-form-item>\n        </a-col>\n        <template v-if=\"toggleSearchStatus\">\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"组合插件名\">\n              <a-input placeholder=\"请输入组合插件名\" v-model=\"queryParam.combinedName\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n            <a-form-item label=\"收益金额\">\n              <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.income_begin\"></a-input>\n              <span class=\"query-group-split-cust\"></span>\n              <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.income_end\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n            <a-form-item label=\"调用次数\">\n              <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.usernum_begin\"></a-input>\n              <span class=\"query-group-split-cust\"></span>\n              <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.usernum_end\"></a-input>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n            <a-form-item label=\"需要金额\">\n              <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.neednum_begin\"></a-input>\n              <span class=\"query-group-split-cust\"></span>\n              <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.neednum_end\"></a-input>\n            </a-form-item>\n          </a-col>\n        </template>\n        <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n          <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n            <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n            <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n            <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n              {{ toggleSearchStatus ? '收起' : '展开' }}\n              <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n            </a>\n          </span>\n        </a-col>\n      </a-row>\n    </a-form>\n  </div>\n  <!-- 查询区域-END -->\n\n  <!-- 操作按钮区域 -->\n  <div class=\"table-operator\">\n    <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n    <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('插件商城')\">导出</a-button>\n\n    <!-- admin用户才能看到的功能 -->\n    <template v-if=\"isAdmin\">\n      <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\" @change=\"handleImportExcel\">\n        <a-button type=\"primary\" icon=\"import\">导入</a-button>\n      </a-upload>\n      <!-- 高级查询区域 -->\n      <j-super-query :fieldList=\"superFieldList\" ref=\"superQueryModal\" @handleSuperQuery=\"handleSuperQuery\"></j-super-query>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\"><a-icon type=\"delete\"/>删除</a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作 <a-icon type=\"down\" /></a-button>\n      </a-dropdown>\n    </template>\n  </div>\n\n  <!-- table区域-begin -->\n  <div>\n    <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n      <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n      <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n    </div>\n\n    <a-table\n      ref=\"table\"\n      size=\"middle\"\n      :scroll=\"{x:true}\"\n      bordered\n      rowKey=\"id\"\n      :columns=\"columns\"\n      :dataSource=\"dataSource\"\n      :pagination=\"ipagination\"\n      :loading=\"loading\"\n      :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n      class=\"j-table-force-nowrap\"\n      @change=\"handleTableChange\">\n\n      <template slot=\"htmlSlot\" slot-scope=\"text\">\n        <div v-html=\"text\"></div>\n      </template>\n      <template slot=\"imgSlot\" slot-scope=\"text\">\n        <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无图片</span>\n        <img v-else :src=\"getImgView(text)\" height=\"25px\" alt=\"\" style=\"max-width:80px;font-size: 12px;font-style: italic;\"/>\n      </template>\n      <template slot=\"fileSlot\" slot-scope=\"text\">\n        <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无文件</span>\n        <a-button\n          v-else\n          :ghost=\"true\"\n          type=\"primary\"\n          icon=\"download\"\n          size=\"small\"\n          @click=\"downloadFile(text)\">\n          下载\n        </a-button>\n      </template>\n\n      <template slot=\"tutorialLinkSlot\" slot-scope=\"text\">\n        <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">暂无</span>\n        <a v-else :href=\"text\" target=\"_blank\" style=\"color: #1890ff;\">\n          <a-icon type=\"link\" style=\"margin-right: 4px;\" />\n          查看教程\n        </a>\n      </template>\n\n      <span slot=\"action\" slot-scope=\"text, record\">\n        <a @click=\"handleEdit(record)\">编辑</a>\n\n        <!-- 只有admin用户才能看到更多操作 -->\n        <template v-if=\"isAdmin\">\n          <a-divider type=\"vertical\" />\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a @click=\"handleDetail(record)\">详情</a>\n              </a-menu-item>\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </template>\n      </span>\n\n    </a-table>\n  </div>\n\n  <aigc-plub-shop-modal ref=\"modalForm\" @ok=\"modalFormOk\"></aigc-plub-shop-modal>\n</a-card>\n", null]}