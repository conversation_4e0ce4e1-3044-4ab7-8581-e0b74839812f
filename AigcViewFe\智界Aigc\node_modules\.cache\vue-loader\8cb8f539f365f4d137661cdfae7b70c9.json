{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue?vue&type=template&id=a83192ae&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\RelatedPlugins.vue", "mtime": 1753944973183}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"related-plugins\" }, [\n    _c(\"div\", { staticClass: \"related-card\" }, [\n      _c(\n        \"h3\",\n        { staticClass: \"section-title\" },\n        [\n          _c(\"a-icon\", { attrs: { type: \"appstore\" } }),\n          _vm._v(\"\\n      相关推荐\\n      \"),\n          _vm.categoryText\n            ? _c(\"span\", { staticClass: \"category-hint\" }, [\n                _vm._v(\"- \" + _vm._s(_vm.categoryText))\n              ])\n            : _vm._e()\n        ],\n        1\n      ),\n      _vm.recommendations && _vm.recommendations.length > 0\n        ? _c(\n            \"div\",\n            { staticClass: \"plugins-grid\" },\n            _vm._l(_vm.recommendations, function(plugin) {\n              return _c(\n                \"div\",\n                {\n                  key: plugin.id,\n                  staticClass: \"plugin-card\",\n                  on: {\n                    click: function($event) {\n                      return _vm.goToPlugin(plugin.id)\n                    }\n                  }\n                },\n                [\n                  _c(\"div\", { staticClass: \"plugin-image\" }, [\n                    _c(\"img\", {\n                      attrs: {\n                        src: _vm.getPluginImage(plugin),\n                        alt: plugin.plubname\n                      },\n                      on: { error: _vm.handleImageError }\n                    }),\n                    _c(\n                      \"div\",\n                      { staticClass: \"image-overlay\" },\n                      [\n                        _c(\"a-icon\", {\n                          staticClass: \"view-icon\",\n                          attrs: { type: \"eye\" }\n                        })\n                      ],\n                      1\n                    )\n                  ]),\n                  _c(\"div\", { staticClass: \"plugin-info\" }, [\n                    _c(\"h4\", { staticClass: \"plugin-name\" }, [\n                      _vm._v(_vm._s(plugin.plubname))\n                    ]),\n                    _c(\"p\", { staticClass: \"plugin-description\" }, [\n                      _vm._v(_vm._s(plugin.plubinfo || \"暂无描述\"))\n                    ]),\n                    _c(\"div\", { staticClass: \"plugin-meta\" }, [\n                      _c(\n                        \"div\",\n                        { staticClass: \"meta-item\" },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"dollar\" } }),\n                          _c(\"span\", [\n                            _vm._v(_vm._s(_vm.getPluginPriceText(plugin)))\n                          ])\n                        ],\n                        1\n                      )\n                    ]),\n                    _c(\n                      \"div\",\n                      { staticClass: \"plugin-tags\" },\n                      [\n                        _c(\n                          \"a-tag\",\n                          {\n                            attrs: {\n                              color: _vm.getCategoryColor(plugin.plubCategory),\n                              size: \"small\"\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(\n                                  _vm.getCategoryText(plugin.plubCategory)\n                                ) +\n                                \"\\n            \"\n                            )\n                          ]\n                        ),\n                        _c(\n                          \"a-tag\",\n                          {\n                            attrs: {\n                              color: _vm.getStatusColor(plugin.status),\n                              size: \"small\"\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.getStatusText(plugin.status)) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      ],\n                      1\n                    )\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"plugin-actions\" },\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          attrs: { type: \"primary\", size: \"small\" },\n                          on: {\n                            click: function($event) {\n                              $event.stopPropagation()\n                              return _vm.goToPlugin(plugin.id)\n                            }\n                          }\n                        },\n                        [_vm._v(\"\\n            查看详情\\n          \")]\n                      )\n                    ],\n                    1\n                  )\n                ]\n              )\n            }),\n            0\n          )\n        : _c(\n            \"div\",\n            { staticClass: \"no-recommendations\" },\n            [\n              _c(\n                \"a-empty\",\n                { attrs: { description: \"暂无相关推荐\" } },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.goToMarket }\n                    },\n                    [\n                      _c(\"a-icon\", { attrs: { type: \"shop\" } }),\n                      _vm._v(\"\\n          浏览更多插件\\n        \")\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          ),\n      _vm.recommendations && _vm.recommendations.length > 0\n        ? _c(\n            \"div\",\n            { staticClass: \"more-actions\" },\n            [\n              _c(\n                \"a-button\",\n                {\n                  staticClass: \"more-plugins-btn\",\n                  on: { click: _vm.viewMoreByCategory }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"appstore\" } }),\n                  _c(\"span\", [_vm._v(\"查看更多插件\")]),\n                  _c(\"a-icon\", { attrs: { type: \"right\" } })\n                ],\n                1\n              )\n            ],\n            1\n          )\n        : _vm._e()\n    ])\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}