<template>
  <div class="plugin-card" :class="{ 'no-transition': disableTransition, 'combined-plugin': isCombinedPlugin }" @click="handlePluginClick('card')">
    <!-- 插件图片区域 -->
    <div class="plugin-image">
      <div class="image-overlay"></div>
      <img
        :src="getPluginImage(plugin)"
        :alt="isCombinedPlugin ? plugin.combinedName : plugin.plubname"
        @error="handleImageError"
        @load="handleImageLoad"
      />

      <!-- 🔥 组合插件标识 -->
      <div v-if="isCombinedPlugin" class="combined-badge">
        <span class="badge-icon">🔗</span>
        组合插件
      </div>

      <!-- 分类标签 -->
      <div class="plugin-category" v-if="plugin.plubCategory_dictText">
        <span class="category-icon">{{ getCategoryIcon(plugin.plubCategory_dictText) }}</span>
        {{ plugin.plubCategory_dictText }}
      </div>

      <!-- 状态标识 -->
      <div
        v-if="plugin.status_dictText && plugin.status_dictText !== '正常'"
        class="plugin-badge"
        :class="getBadgeClass(plugin.status_dictText)"
      >
        {{ plugin.status_dictText }}
      </div>

      <!-- 图片加载状态 -->
      <div v-if="imageLoading" class="image-loading">
        <a-spin size="small" />
      </div>
    </div>
    
    <!-- 插件信息区域 -->
    <div class="plugin-info">
      <!-- 🔥 组合插件信息 -->
      <div v-if="isCombinedPlugin" class="combined-plugin-info">
        <!-- 组合插件名称和作者 -->
        <div class="plugin-header-info">
          <h3 class="plugin-name" :title="plugin.combinedName">
            {{ plugin.combinedName }}
          </h3>
          <span class="plugin-author" v-if="plugin.plubwrite_dictText">
            <a-icon type="user" />
            创作者 {{ plugin.plubwrite_dictText }}
          </span>
        </div>

        <!-- 组合插件描述 -->
        <p class="plugin-description" :title="plugin.combinedDescription">
          {{ truncateText(plugin.combinedDescription, 100) }}
        </p>

        <!-- 组合插件底部信息 -->
        <div class="plugin-footer">
          <div class="plugin-price combined-price">
            <span class="price-hint">收费请进入详情查看</span>
          </div>
          <a-button
            type="primary"
            size="default"
            @click.stop="handlePluginClick('button')"
            class="detail-button combined-detail-btn"
          >
            <a-icon type="eye" />
            查看详情
          </a-button>
        </div>
      </div>

      <!-- 🔥 普通插件信息 -->
      <div v-else class="normal-plugin-info">
        <!-- 插件名称和作者 -->
        <div class="plugin-header-info">
          <h3 class="plugin-name" :title="plugin.plubname">
            {{ plugin.plubname }}
          </h3>
          <span class="plugin-author" v-if="plugin.plubwrite_dictText">
            <a-icon type="user" />
            创作者 {{ plugin.plubwrite_dictText }}
          </span>
        </div>

        <!-- 插件描述 -->
        <p class="plugin-description" :title="plugin.plubinfo">
          {{ truncateText(plugin.plubinfo, 100) }}
        </p>

        <!-- 价格和操作区域 -->
        <div class="plugin-footer">
          <div class="plugin-price">
            <span class="price-value">{{ getPriceText() }}</span>
          </div>
          <a-button
            type="primary"
            size="default"
            @click.stop="handlePluginClick('button')"
            class="detail-button"
          >
            <a-icon type="eye" />
            查看详情
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  formatPluginPrice,
  truncateText,
  getPluginImageUrl,
  getPluginStatusClass
} from '../utils/marketUtils'

export default {
  name: 'PluginCard',
  props: {
    plugin: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },

  data() {
    return {
      // 🔥 默认插件图片改为TOS路径（通过后端接口获取）
      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg',
      imageLoading: true,
      disableTransition: true // 初始禁用过渡动画
    }
  },

  computed: {
    // 🔥 判断是否为组合插件
    isCombinedPlugin() {
      return this.plugin.isCombined === 1 || this.plugin.isCombined === '1'
    }
  },

  mounted() {
    // 页面加载完成后启用过渡动画
    this.$nextTick(() => {
      setTimeout(() => {
        this.disableTransition = false
      }, 100) // 延迟100ms启用动画
    })
  },

  methods: {
    // 🔥 获取插件图片（支持组合插件优先级处理）
    getPluginImage(plugin) {
      // 使用优化后的工具函数，支持组合插件图片优先级
      const defaultImg = this.defaultPluginImage
      return getPluginImageUrl(plugin, defaultImg)
    },
    
    // 处理图片加载错误
    handleImageError(e) {
      console.log('图片加载失败，使用默认图片:', e.target.src)
      e.target.src = this.defaultPluginImage
      this.imageLoading = false
    },
    
    // 处理图片加载完成
    handleImageLoad() {
      this.imageLoading = false
    },
    
    // 获取创作者头像
    getAuthorAvatar() {
      // 可以根据创作者ID获取头像，暂时使用默认头像
      return require('@/assets/logo.png')
    },

    // 获取分类图标
    getCategoryIcon(category) {
      const icons = {
        '内容生成': '✍️',
        '图片生成': '🎨',
        '视频处理': '🎬',
        '数据分析': '📊',
        '开发工具': '⚙️',
        '设计创意': '🎭',
        '营销工具': '📈'
      }
      return icons[category] || '🔧'
    },

    // 文本截断
    truncateText,

    // 格式化价格
    formatPrice: formatPluginPrice,

    // 获取状态标识样式类
    getBadgeClass: getPluginStatusClass,
    
    // 🔥 处理插件点击 - 区分组合插件和普通插件
    handlePluginClick(source) {
      console.log('🎯 插件点击来源:', source, '插件:', this.isCombinedPlugin ? this.plugin.combinedName : this.plugin.plubname)
      console.log('🎯 插件数据:', this.plugin)
      console.log('🎯 是否组合插件:', this.isCombinedPlugin)
      console.log('🎯 isCombined值:', this.plugin.isCombined, '类型:', typeof this.plugin.isCombined)

      if (this.isCombinedPlugin) {
        // 组合插件：触发组合插件详情事件
        console.log('🔗 触发组合插件详情事件:', this.plugin)
        this.$emit('combined-plugin-detail', this.plugin)
      } else {
        // 普通插件：触发普通插件详情事件
        console.log('📄 触发插件详情事件:', this.plugin)
        this.$emit('plugin-detail', this.plugin)
      }
    },

    // 🔥 获取价格显示文本
    getPriceText() {
      const price = this.plugin.neednum
      const isSvipFree = this.plugin.isSvipFree === 1 || this.plugin.isSvipFree === '1'

      if (isSvipFree && price && price > 0) {
        return `SVIP免费，低至¥${this.formatPrice(price)}/次`
      } else {
        return `低至¥${this.formatPrice(price)}/次`
      }
    }
  }
}
</script>

<style scoped>
.plugin-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e2e8f0;
  position: relative;
}

.plugin-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plugin-card:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.12);
  border-color: #3b82f6;
}

.plugin-card:hover::before {
  opacity: 1;
}

/* 禁用过渡动画的类 */
.plugin-card.no-transition,
.plugin-card.no-transition *,
.plugin-card.no-transition::before {
  transition: none !important;
}

/* 图片区域 */
.plugin-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.plugin-card:hover .image-overlay {
  opacity: 1;
}

.plugin-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.plugin-card:hover .plugin-image img {
  transform: scale(1.1);
  filter: brightness(1.1);
}

/* 悬浮操作按钮样式已移除 */

/* 🔥 组合插件卡片样式 */
.plugin-card.combined-plugin {
  border: 2px solid #f59e0b;
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.15);
}

.plugin-card.combined-plugin:hover {
  border-color: #d97706;
  box-shadow: 0 12px 40px rgba(245, 158, 11, 0.25);
}

/* 🔥 组合插件标识 */
.combined-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  z-index: 4; /* 🔥 提高层级，确保覆盖分类标签 */
  animation: pulse 2s infinite;
  min-width: 80px; /* 🔥 设置最小宽度 */
  justify-content: center; /* 🔥 居中对齐 */
}

.badge-icon {
  font-size: 0.8rem;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 🔥 组合插件价格提示 */
.combined-price {
  display: flex;
  align-items: center;
  justify-content: center;
}

.price-hint {
  color: #f59e0b;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  padding: 0.25rem 0.5rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 🔥 组合插件详情按钮 */
.combined-detail-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border: none;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.combined-detail-btn:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
}

.plugin-category {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.95);
  color: #3b82f6;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 2;
}

/* 🔥 组合插件时隐藏分类标签，避免重叠 */
.plugin-card.combined-plugin .plugin-category {
  display: none;
}

.category-icon {
  font-size: 0.8rem;
}

.plugin-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-recommend {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
}

.badge-hot {
  background: linear-gradient(135deg, #ff4757, #ff3838);
}

.badge-new {
  background: linear-gradient(135deg, #5f27cd, #341f97);
}

.badge-default {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 50%;
}

/* 信息区域 */
.plugin-info {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
}

.plugin-header-info {
  margin-bottom: 1rem;
}

.plugin-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plugin-author {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plugin-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.plugin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.plugin-price {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.price-value {
  font-size: 1.4rem;
  font-weight: 800;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.price-unit {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

.detail-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 12px;
  font-weight: 600;
  height: 40px;
  padding: 0 1.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.detail-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

.detail-button:active {
  transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .plugin-image {
    height: 150px;
  }
  
  .plugin-info {
    padding: 0.75rem;
  }
  
  .plugin-name {
    font-size: 1rem;
  }
  
  .plugin-description {
    font-size: 0.85rem;
  }
  
  .plugin-footer {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .detail-button {
    width: 100%;
  }
}
</style>
