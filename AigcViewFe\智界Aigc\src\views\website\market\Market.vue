<template>
  <WebsitePage>
    <div class="market-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">AI插件中心</h1>
        <p class="simple-subtitle">发现优质AI插件，提升创作效率，让每个想法都能完美实现</p>
      </div>

      <!-- 🔥 搜索区域 -->
      <section class="search-section">
        <div class="container">
          <div class="search-layout">
            <div class="search-input-group">
              <!-- 🔥 使用原生输入框，完全自定义样式 -->
              <div class="custom-search-input">
                <div class="search-icon">
                  <a-icon type="search" />
                </div>
                <input
                  v-model="searchKeyword"
                  @input="handleSearchInput"
                  @keyup.enter="handleSearch"
                  placeholder="搜索插件名称、描述..."
                  class="search-input-native"
                />
                <!-- 🔥 清空搜索框图标 -->
                <div
                  v-if="searchKeyword"
                  class="clear-search-icon"
                  @click="clearSearchInput"
                  title="清空搜索"
                >
                  <a-icon type="close-circle" />
                </div>
              </div>

              <!-- 🔥 清空筛选按钮移到搜索框右边 -->
              <a-button
                @click="clearAllFilters"
                size="large"
                class="clear-filters-btn"
                :disabled="!hasActiveFilters">
                <a-icon type="clear" />
                清空所有筛选
              </a-button>
            </div>
          </div>
        </div>
      </section>

      <!-- 主内容区域 -->
      <section class="main-content">
        <div class="container">
          <div class="content-layout">
            <!-- 左侧筛选栏 -->
            <aside class="sidebar">
              <div class="filter-panel">

                <!-- 分类筛选 -->
                <div class="filter-section">
                  <div class="filter-header" @click="toggleSection('category')">
                    <h3 class="filter-title">
                      <a-icon type="appstore" class="filter-icon" />
                      插件分类
                      <span class="filter-badge" v-if="currentFilters.category">1</span>
                    </h3>
                    <a-icon
                      :type="collapsedSections.category ? 'down' : 'up'"
                      class="collapse-icon"
                    />
                  </div>
                  <a-collapse-transition>
                    <div v-show="!collapsedSections.category" class="filter-content">
                      <div class="category-grid">
                        <div
                          class="category-tag"
                          :class="{ active: currentFilters.category === '' }"
                          @click="selectCategory('')"
                        >
                          <span class="tag-icon">🌟</span>
                          <span class="tag-text">全部</span>
                          <span class="tag-count">{{ totalPlugins }}</span>
                        </div>
                        <div
                          v-for="category in categories"
                          :key="category.value"
                          class="category-tag"
                          :class="{ active: currentFilters.category === category.value }"
                          @click="selectCategory(category.value)"
                        >
                          <span class="tag-icon">{{ getCategoryIcon(category.value) }}</span>
                          <span class="tag-text">{{ category.text }}</span>
                          <span class="tag-count">{{ categoryCounts[category.value] || 0 }}</span>
                        </div>
                      </div>
                    </div>
                  </a-collapse-transition>
                </div>

                <!-- 价格筛选 -->
                <div class="filter-section">
                  <div class="filter-header" @click="toggleSection('price')">
                    <h3 class="filter-title">
                      <a-icon type="dollar" class="filter-icon" />
                      价格范围
                      <span class="filter-badge" v-if="currentFilters.priceRange">1</span>
                    </h3>
                    <a-icon
                      :type="collapsedSections.price ? 'down' : 'up'"
                      class="collapse-icon"
                    />
                  </div>
                  <a-collapse-transition>
                    <div v-show="!collapsedSections.price" class="filter-content">
                      <div class="price-grid">
                        <div
                          class="price-tag"
                          :class="{ active: currentFilters.priceRange === '' && !showCustomPrice }"
                          @click="selectPriceRange('')"
                        >
                          <span class="tag-icon">💰</span>
                          <span class="tag-text">全部</span>
                        </div>
                        <div
                          class="price-tag"
                          :class="{ active: currentFilters.priceRange === '0-1' }"
                          @click="selectPriceRange('0-1')"
                        >
                          <span class="tag-icon">🪙</span>
                          <span class="tag-text">¥0-1</span>
                        </div>
                        <div
                          class="price-tag"
                          :class="{ active: currentFilters.priceRange === '1-5' }"
                          @click="selectPriceRange('1-5')"
                        >
                          <span class="tag-icon">💵</span>
                          <span class="tag-text">¥1-5</span>
                        </div>
                        <div
                          class="price-tag"
                          :class="{ active: currentFilters.priceRange === '5+' }"
                          @click="selectPriceRange('5+')"
                        >
                          <span class="tag-icon">💎</span>
                          <span class="tag-text">¥5+</span>
                        </div>
                      </div>
                      <!-- 自定义价格范围 -->
                      <div class="custom-price-range">
                        <div class="custom-price-header">
                          <span class="custom-price-icon">⚙️</span>
                          <span class="custom-price-label">自定义价格</span>
                        </div>
                        <div class="price-inputs">
                          <div class="price-input-row">
                            <label class="input-label">最低价格</label>
                            <a-input-number
                              v-model="customPriceMin"
                              :min="0"
                              :max="999"
                              placeholder="请输入最低价格"
                              size="default"
                              class="price-input"
                              @pressEnter="applyCustomPrice"
                            />
                          </div>
                          <div class="price-input-row">
                            <label class="input-label">最高价格</label>
                            <a-input-number
                              v-model="customPriceMax"
                              :min="customPriceMin || 0"
                              :max="999"
                              placeholder="请输入最高价格"
                              size="default"
                              class="price-input"
                              @pressEnter="applyCustomPrice"
                            />
                          </div>
                          <a-button
                            type="primary"
                            @click="applyCustomPrice"
                            :disabled="!customPriceMin && !customPriceMax"
                            class="apply-custom-btn"
                            block
                          >
                            确定筛选
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </a-collapse-transition>
                </div>

                <!-- 排序方式 -->
                <div class="filter-section">
                  <div class="filter-header" @click="toggleSection('sort')">
                    <h3 class="filter-title">
                      <a-icon type="sort-ascending" class="filter-icon" />
                      排序方式
                      <span class="filter-badge" v-if="currentFilters.sortType !== 'default'">1</span>
                    </h3>
                    <a-icon
                      :type="collapsedSections.sort ? 'down' : 'up'"
                      class="collapse-icon"
                    />
                  </div>
                  <a-collapse-transition>
                    <div v-show="!collapsedSections.sort" class="filter-content">
                      <div class="sort-grid">
                        <div
                          class="sort-tag"
                          :class="{ active: currentFilters.sortType === 'default' }"
                          @click="selectSort('default')"
                        >
                          <span class="tag-icon">🌟</span>
                          <span class="tag-text">默认</span>
                        </div>
                        <div
                          class="sort-tag"
                          :class="{ active: currentFilters.sortType === 'newest' }"
                          @click="selectSort('newest')"
                        >
                          <span class="tag-icon">⏰</span>
                          <span class="tag-text">最新</span>
                        </div>
                        <div
                          class="sort-tag"
                          :class="{ active: currentFilters.sortType === 'price-asc' }"
                          @click="selectSort('price-asc')"
                        >
                          <span class="tag-icon">📈</span>
                          <span class="tag-text">价格↑</span>
                        </div>
                        <div
                          class="sort-tag"
                          :class="{ active: currentFilters.sortType === 'price-desc' }"
                          @click="selectSort('price-desc')"
                        >
                          <span class="tag-icon">📉</span>
                          <span class="tag-text">价格↓</span>
                        </div>
                      </div>
                    </div>
                  </a-collapse-transition>
                </div>


              </div>
            </aside>

            <!-- 右侧主内容 -->
            <main class="main-area">
              <!-- 结果头部 -->
              <div class="results-header">
                <div class="header-left">
                  <h2 class="results-title">
                    <span v-if="currentFilters.category">{{ getCategoryText(currentFilters.category) }}插件</span>
                    <span v-else-if="currentFilters.keyword">搜索结果</span>
                    <span v-else>全部插件</span>
                  </h2>
                  <div class="active-filters" v-if="currentFilters.keyword || currentFilters.priceRange || currentFilters.sortType !== 'default'">
                    <a-tag
                      v-if="currentFilters.keyword"
                      closable
                      color="green"
                      @close="clearKeywordFilter"
                      class="filter-tag"
                    >
                      "{{ currentFilters.keyword }}"
                    </a-tag>
                    <a-tag
                      v-if="currentFilters.priceRange"
                      closable
                      color="orange"
                      @close="clearPriceFilter"
                      class="filter-tag"
                    >
                      {{ getPriceRangeText(currentFilters.priceRange) }}
                    </a-tag>
                    <a-tag
                      v-if="currentFilters.sortType !== 'default'"
                      closable
                      color="purple"
                      @close="clearSortFilter"
                      class="filter-tag"
                    >
                      {{ getSortTypeText(currentFilters.sortType) }}
                    </a-tag>
                  </div>
                </div>
                <div class="header-right">
                  <div class="results-count">
                    <span class="count-number">{{ filteredPlugins.length }}</span> 个一级插件，共 <span class="count-number">{{ filteredTotalPlugins }}</span> 个插件
                  </div>
                </div>
              </div>

              <!-- 插件网格 -->
              <div class="plugins-grid-wrapper">
                <PluginGrid
                  :plugins="currentPagePlugins"
                  :loading="loading"
                  :error="error"
                  @plugin-use="handlePluginUse"
                  @plugin-detail="handlePluginDetail"
                  @combined-plugin-detail="viewCombinedPluginDetails"
                  @retry="handleRetry"
                  @clear-filters="clearAllFilters"
                />
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper" v-if="filteredPlugins.length > pageSize">
                <a-pagination
                  :current="currentPage"
                  :total="filteredPlugins.length"
                  :page-size="pageSize"
                  :page-size-options="['8', '12', '16', '24']"
                  :show-size-changer="true"
                  :show-quick-jumper="true"
                  :show-total="(total, range) => `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
                  @change="handlePageChange"
                  @showSizeChange="handlePageSizeChange"
                />
              </div>
            </main>
          </div>
        </div>
      </section>
    </div>

    <!-- 🔥 组合插件子插件选择弹窗 -->
    <div v-if="showCombinedModal" class="combined-modal-overlay" @click="closeCombinedModal">
      <div class="combined-modal-content" @click.stop>
        <div class="combined-modal-header">
          <div class="header-content1">
            <div class="header-icon">🔗</div>
            <div class="header-text">
              <h2>选择插件</h2>
              <p class="header-subtitle">{{ selectedCombinedPlugin && selectedCombinedPlugin.combinedName }}</p>
            </div>
          </div>
          <button class="combined-modal-close" @click="closeCombinedModal">×</button>
        </div>
        <div class="combined-modal-body">
          <div v-if="combinedModalLoading" class="loading-state">
            <a-spin size="large" />
            <p>正在加载子插件...</p>
          </div>
          <div v-else-if="combinedSubPlugins.length === 0" class="empty-state">
            <p>暂无子插件</p>
          </div>
          <div v-else class="sub-plugins-grid">
            <div
              v-for="subPlugin in combinedSubPlugins"
              :key="subPlugin.id"
              class="sub-plugin-card"
              @click="selectSubPlugin(subPlugin)">
              <!-- 🔥 插件图片区域 -->
              <div class="sub-plugin-image">
                <img :src="getPluginImage(subPlugin)" :alt="subPlugin.plubname" />
                <!-- 分类标签 -->
                <div class="sub-plugin-category">
                  <span class="category-icon">🔧</span>
                  <span>{{ subPlugin.plubCategory_dictText || '其他' }}</span>
                </div>
                <!-- 状态标签 -->
                <div class="sub-plugin-status">上架</div>
              </div>

              <!-- 🔥 插件内容区域 -->
              <div class="sub-plugin-content">
                <div class="sub-plugin-header">
                  <h3 class="sub-plugin-title">{{ subPlugin.plubname }}</h3>
                  <div class="sub-plugin-author">
                    <span class="author-icon">👤</span>
                    <span>创作者 {{ subPlugin.plubwrite_dictText || '未知' }}</span>
                  </div>
                </div>

                <p class="sub-plugin-description">{{ subPlugin.plubinfo || '暂无描述' }}</p>

                <div class="sub-plugin-footer">
                  <div class="sub-plugin-price">
                    <span class="price-amount">{{ getSubPluginPriceText(subPlugin) }}</span>
                  </div>
                  <button class="sub-plugin-btn">
                    <span class="btn-icon">👁</span>
                    <span>查看详情</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔥 悬浮式剪映小助手推广组件 -->
    <div v-if="showJianYingFloat" class="jianying-float-container">
      <button class="jianying-float-btn" @click="goToJianYingDraft">
        <!-- 关闭按钮 -->
        <div class="float-close-btn" @click.stop="hideJianYingFloat">
          <a-icon type="close" />
        </div>

        <!-- 按钮图标 -->
        <div class="btn-icon">
          <a-icon type="download" />
        </div>

        <!-- 按钮文字 -->
        <div class="btn-text">剪映小助手下载</div>

        <!-- 发光效果 -->
        <div class="btn-glow"></div>

        <!-- 粒子效果容器 -->
        <div class="btn-particles" ref="particles"></div>

        <!-- 波纹效果 -->
        <div class="jianying-waves"></div>
      </button>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import CategoryFilter from './components/CategoryFilter.vue'
import SearchFilter from './components/SearchFilter.vue'
import PluginGrid from './components/PluginGrid.vue'
import marketApi from '@/api/market'
import { formatCategories, validatePluginData, getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils'
import { HeartbeatMixin } from '@/mixins/HeartbeatMixin'
import { getCurrentPageConfig } from '@/utils/heartbeatConfig'

export default {
  name: 'Market', // 确保组件名称与路由配置中的componentName一致
  mixins: [HeartbeatMixin],
  components: {
    WebsitePage,
    CategoryFilter,
    SearchFilter,
    PluginGrid
  },

  data() {
    return {
      // 心跳配置 - 商城页面使用中频心跳
      heartbeatConfig: getCurrentPageConfig('market', {
        apiKey: 'market-page-heartbeat-key', // 商城页面专用API密钥
        enableDebugLog: process.env.NODE_ENV === 'development',
      }),

      // 插件数据
      allPlugins: [],
      filteredPlugins: [],
      currentPagePlugins: [],
      originalPluginsData: [], // 🔥 存储原始插件数据（用于搜索子插件）

      // 分类数据
      categories: [],
      categoryCounts: {},

      // 分页状态
      currentPage: 1,
      pageSize: 12,

      // 筛选条件
      currentFilters: {
        category: '',
        keyword: '',
        priceRange: '',
        sortType: 'default',
        author: ''
      },

      // 加载状态
      loading: false,
      error: null,

      // 统计数据
      totalPlugins: 0,

      // 搜索相关
      searchKeyword: '',
      showSuggestions: false,
      suggestions: ['文案生成', '图片处理', '视频剪辑', '数据分析', '代码助手'],

      // 筛选面板折叠状态
      collapsedSections: {
        search: false,
        category: false,
        price: true,
        sort: true
      },

      // 自定义价格范围
      showCustomPrice: false,
      customPriceMin: null,
      customPriceMax: null,

      // 🔥 组合插件弹窗
      showCombinedModal: false,
      selectedCombinedPlugin: null,
      combinedModalLoading: false,
      combinedSubPlugins: [],

      // 🔥 悬浮式剪映小助手推广组件
      showJianYingFloat: true // 是否显示悬浮组件
    }
  },

  computed: {
    hasActiveFilters() {
      return this.currentFilters.category ||
             this.currentFilters.keyword ||
             this.currentFilters.priceRange ||
             this.currentFilters.sortType !== 'default'
    },

    // 🔥 计算原始插件总数（包括组合插件的所有子插件）
    totalOriginalPlugins() {
      return this.originalPluginsData ? this.originalPluginsData.length : 0
    },

    // 🔥 计算筛选后的总插件数量（包括组合插件的子插件）
    filteredTotalPlugins() {
      let totalCount = 0

      this.filteredPlugins.forEach(plugin => {
        if (plugin.isCombined === 1) {
          // 组合插件：计算其子插件数量
          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)
          totalCount += subPlugins.length
        } else {
          // 普通插件：计数为1
          totalCount += 1
        }
      })

      return totalCount
    },

    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）
    defaultPluginImage() {
      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    }
  },

  async created() {
    // 悬浮组件默认显示，不需要检查localStorage
    this.showJianYingFloat = true
    console.log('🔥 悬浮组件默认显示')

    // 只有在没有数据时才初始化，避免重复加载
    if (this.allPlugins.length === 0) {
      await this.initializeMarket()
    } else {
      // 如果已有数据，只恢复筛选状态
      this.restoreMarketState()
      this.applyFilters()
      console.log('商城数据已存在，跳过重新加载')
    }
  },

  // 组件销毁前恢复滚动
  beforeDestroy() {
    document.body.style.overflow = ''
  },

  // 监听筛选条件变化，保存到localStorage
  watch: {
    currentFilters: {
      handler(newFilters) {
        this.saveMarketState(newFilters);
      },
      deep: true
    },

    searchKeyword(newKeyword) {
      this.saveMarketState({ ...this.currentFilters, keyword: newKeyword });
    }
  },

  methods: {
    // 初始化商城
    async initializeMarket() {
      this.loading = true

      try {
        // 恢复筛选状态
        this.restoreMarketState();

        // 并行加载分类和插件数据
        await Promise.all([
          this.loadCategories(),
          this.loadPlugins()
        ])

        console.log('商城初始化完成')

      } catch (error) {
        console.error('商城初始化失败:', error)
        this.error = '商城初始化失败，请刷新页面重试'
      } finally {
        this.loading = false
      }
    },

    // 加载分类数据
    async loadCategories() {
      try {
        const response = await marketApi.getPluginCategories()
        if (response.success) {
          this.categories = formatCategories(response.result || [])
          console.log('分类数据加载成功:', this.categories)
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
      }
    },

    // 加载插件数据
    async loadPlugins() {
      try {
        const params = {
          pageNo: 1,
          pageSize: 1000, // 获取所有数据，前端分页
          status: 1 // 只获取已上架的插件
        }

        const response = await marketApi.getPluginList(params)

        if (response.success) {
          const result = response.result || response.data
          const originalData = result.records || result || []

          // 🔥 保存原始数据（用于搜索子插件）
          this.originalPluginsData = [...originalData]

          // 🔥 前端处理组合插件去重逻辑（使用统一工具函数）
          const processedPlugins = processPluginsWithCombined(originalData)

          this.allPlugins = processedPlugins
          this.totalPlugins = processedPlugins.length

          // 计算分类统计
          this.calculateCategoryCounts()

          // 应用筛选
          this.applyFilters()

          console.log('插件数据加载成功:', this.allPlugins.length, '个插件（包含组合插件）')
        } else {
          throw new Error(response.message || '获取插件数据失败')
        }

      } catch (error) {
        console.error('加载插件数据失败:', error)
        this.error = error.message || '加载插件数据失败'
      }
    },



    // 🔥 检查组合插件是否包含指定分类
    combinedPluginHasCategory(combinedPlugin, targetCategory) {
      // 简化版本：直接检查组合插件本身的分类
      // 在实际应用中，这里应该查询该组合插件的所有子插件
      // 但为了避免复杂的异步查询，我们先用简化逻辑
      return combinedPlugin.plubCategory === targetCategory
    },

    // 🔥 计算分类统计（支持组合插件）
    calculateCategoryCounts() {
      this.categoryCounts = {}

      this.categories.forEach(category => {
        this.categoryCounts[category.value] = 0
      })

      // 统计组合插件数量
      let combinedPluginCount = 0

      this.allPlugins.forEach(plugin => {
        if (plugin.isCombined === 1 || plugin.isCombined === '1') {
          // 组合插件计数
          combinedPluginCount++

          // 组合插件也按其分类计数（用于其他分类的统计）
          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {
            this.categoryCounts[plugin.plubCategory]++
          }
        } else {
          // 普通插件计数
          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {
            this.categoryCounts[plugin.plubCategory]++
          }
        }
      })

      // 设置组合插件分类的数量
      this.categoryCounts['combine'] = combinedPluginCount

      console.log('🔥 分类统计完成:', this.categoryCounts)
    },

    // 🔥 应用筛选条件（支持组合插件）
    applyFilters() {
      let filtered = [...this.allPlugins]

      // 🔥 分类筛选（支持组合插件）
      if (this.currentFilters.category) {
        if (this.currentFilters.category === 'combine') {
          // 只显示组合插件
          filtered = filtered.filter(plugin => plugin.isCombined === 1 || plugin.isCombined === '1')
        } else {
          // 显示指定分类的普通插件 + 包含该分类的组合插件
          filtered = filtered.filter(plugin => {
            // 普通插件：直接匹配分类
            if (plugin.isCombined !== 1 && plugin.isCombined !== '1') {
              return plugin.plubCategory === this.currentFilters.category
            }
            // 组合插件：需要检查是否包含该分类的子插件
            return this.combinedPluginHasCategory(plugin, this.currentFilters.category)
          })
        }
      }

      // 🔥 关键词筛选（支持组合插件及其子插件）
      if (this.currentFilters.keyword) {
        const keyword = this.currentFilters.keyword.toLowerCase()
        filtered = this.filterByKeyword(filtered, keyword)
      }

      // 价格范围筛选（只对普通插件有效）
      if (this.currentFilters.priceRange) {
        filtered = this.filterByPriceRange(filtered, this.currentFilters.priceRange)
      }

      // 排序（包括默认排序）
      filtered = this.sortPlugins(filtered, this.currentFilters.sortType)

      this.filteredPlugins = filtered
      this.updateCurrentPagePlugins()
    },

    // 🔥 关键词筛选（支持搜索组合插件的子插件）
    filterByKeyword(plugins, keyword) {
      return plugins.filter(plugin => {
        if (plugin.isCombined === 1 || plugin.isCombined === '1') {
          // 组合插件：搜索组合插件名、描述和子插件名

          // 1. 搜索组合插件本身的名称和描述
          if ((plugin.combinedName && plugin.combinedName.toLowerCase().includes(keyword)) ||
              (plugin.combinedDescription && plugin.combinedDescription.toLowerCase().includes(keyword))) {
            return true
          }

          // 2. 搜索组合插件的子插件名称
          // 从原始数据中查找同名的组合插件的所有子插件
          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)
          return subPlugins.some(subPlugin =>
            subPlugin.plubname && subPlugin.plubname.toLowerCase().includes(keyword)
          )
        } else {
          // 普通插件：搜索插件名和描述
          return (plugin.plubname && plugin.plubname.toLowerCase().includes(keyword)) ||
                 (plugin.plubinfo && plugin.plubinfo.toLowerCase().includes(keyword))
        }
      })
    },

    // 🔥 从原始数据中获取组合插件的子插件
    getSubPluginsFromOriginalData(combinedName) {
      // 从loadPlugins时获取的原始数据中查找
      // 这里需要访问处理前的原始插件数据
      if (!this.originalPluginsData) {
        return []
      }

      return this.originalPluginsData.filter(plugin =>
        plugin.isCombined === 1 &&
        plugin.combinedName === combinedName
      )
    },

    // 价格范围筛选
    filterByPriceRange(plugins, priceRange) {
      if (!priceRange) return plugins

      return plugins.filter(plugin => {
        const price = parseFloat(plugin.neednum) || 0

        switch (priceRange) {
          case '0-1':
            return price >= 0 && price <= 1
          case '1-5':
            return price > 1 && price <= 5
          case '5+':
            return price > 5
          default:
            // 自定义范围 格式: "min-max"
            if (priceRange.includes('-')) {
              const [min, max] = priceRange.split('-').map(p => parseFloat(p) || 0)
              if (max === 999) {
                return price >= min
              } else {
                return price >= min && price <= max
              }
            }
            return true
        }
      })
    },

    // 插件排序
    sortPlugins(plugins, sortType) {
      const sorted = [...plugins]

      switch (sortType) {
        case 'default':
          // 默认排序：按照aigc_plub_shop表的sort_order字段排序（权重越小越靠前）
          return sorted.sort((a, b) => {
            const weightA = parseFloat(a.sortOrder || a.sort_order || 999999) // 如果没有权重，设置为很大的数
            const weightB = parseFloat(b.sortOrder || b.sort_order || 999999)
            return weightA - weightB // 权重小的在前
          })
        case 'newest':
          // 按创建时间排序（假设有createTime字段，如果没有可以用id或其他字段）
          return sorted.sort((a, b) => {
            const timeA = new Date(a.createTime || a.createBy || 0).getTime()
            const timeB = new Date(b.createTime || b.createBy || 0).getTime()
            return timeB - timeA // 最新的在前
          })
        case 'price-asc':
          // 价格从低到高
          return sorted.sort((a, b) => {
            const priceA = parseFloat(a.neednum) || 0
            const priceB = parseFloat(b.neednum) || 0
            return priceA - priceB
          })
        case 'price-desc':
          // 价格从高到低
          return sorted.sort((a, b) => {
            const priceA = parseFloat(a.neednum) || 0
            const priceB = parseFloat(b.neednum) || 0
            return priceB - priceA
          })
        case 'name-asc':
          // 名称A-Z
          return sorted.sort((a, b) => {
            const nameA = (a.plubname || '').toLowerCase()
            const nameB = (b.plubname || '').toLowerCase()
            return nameA.localeCompare(nameB)
          })
        default:
          // 如果是未知的排序类型，也使用默认排序
          return sorted.sort((a, b) => {
            const weightA = parseFloat(a.sortWeight || a.sort_weight || 999999)
            const weightB = parseFloat(b.sortWeight || b.sort_weight || 999999)
            return weightA - weightB
          })
      }
    },

    // 更新当前页插件
    updateCurrentPagePlugins() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      this.currentPagePlugins = this.filteredPlugins.slice(start, end)
    },

    // 处理分类变更
    handleCategoryChange(data) {
      this.currentFilters.category = data.category
      this.currentPage = 1
      this.applyFilters()
      console.log('分类筛选变更:', data)
    },

    // 处理搜索变更
    handleSearchChange(data) {
      this.currentFilters.keyword = data.keyword
      this.currentPage = 1
      this.applyFilters()
      console.log('搜索变更:', data)
    },

    // 处理筛选变更
    handleFilterChange(filters) {
      this.currentFilters = { ...this.currentFilters, ...filters }
      this.currentPage = 1
      this.applyFilters()
      console.log('筛选条件变更:', filters)
    },

    // 处理分页变更
    handlePageChange(page, pageSize) {
      this.currentPage = page
      if (pageSize) {
        this.pageSize = pageSize
      }
      this.updateCurrentPagePlugins()

      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    // 处理页面大小变更
    handlePageSizeChange(page, pageSize) {
      this.currentPage = page
      this.pageSize = pageSize
      this.updateCurrentPagePlugins()
    },

    // 处理插件使用 - 跳转到详情页
    handlePluginUse(plugin) {
      console.log('使用插件:', plugin)

      // 检查插件数据有效性
      if (!validatePluginData(plugin)) {
        this.$notification.error({
          message: '插件数据异常',
          description: '无法查看详情',
          placement: 'topRight'
        })
        return
      }

      // 跳转到插件详情页
      this.$router.push(`/market/plugin/${plugin.id}`)
    },

    // 处理插件详情
    handlePluginDetail(plugin) {
      console.log('查看插件详情:', plugin)

      // 检查插件数据有效性
      if (!plugin || !plugin.id) {
        this.$notification.error({
          message: '插件数据异常',
          description: '无法查看详情',
          placement: 'topRight'
        })
        return
      }

      // 跳转到插件详情页
      this.$router.push(`/market/plugin/${plugin.id}`)
    },

    // 处理重试
    handleRetry() {
      this.error = null
      this.loadPlugins()
    },

    // 🔥 获取子插件价格显示文本
    getSubPluginPriceText(subPlugin) {
      const price = subPlugin.neednum
      const isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1'

      if (!price || price <= 0) {
        return '免费'
      }

      if (isSvipFree) {
        return `SVIP免费，低至¥${price}/次`
      } else {
        return `低至¥${price}/次`
      }
    },

    // 清空所有筛选
    clearAllFilters() {
      this.currentFilters = {
        category: '',
        keyword: '',
        priceRange: '',
        sortType: 'default',
        author: ''
      }
      this.searchKeyword = ''
      // 清空自定义价格输入框
      this.customPriceMin = null
      this.customPriceMax = null
      this.currentPage = 1

      // 清空localStorage中的筛选状态
      this.clearMarketState()

      // 重置子组件
      if (this.$refs.categoryFilter) {
        this.$refs.categoryFilter.resetCategory()
      }
      if (this.$refs.searchFilter) {
        this.$refs.searchFilter.resetFilters()
      }

      this.applyFilters()
      this.$notification.info({
        message: '筛选已清空',
        description: '已清空所有筛选条件',
        placement: 'topRight'
      })
    },

    // 实时搜索输入处理
    handleSearchInput() {
      // 实时更新搜索关键词
      this.currentFilters.keyword = this.searchKeyword
      this.currentPage = 1
      this.applyFilters()
    },

    // 🔥 搜索按钮点击或回车搜索
    handleSearch() {
      this.currentFilters.keyword = this.searchKeyword
      this.currentPage = 1
      this.applyFilters()

      if (this.searchKeyword) {
        this.$notification.success({
          message: '搜索执行中',
          description: `正在搜索"${this.searchKeyword}"相关插件`,
          placement: 'topRight'
        })
      }
    },

    // 🔥 清空搜索框
    clearSearchInput() {
      this.searchKeyword = ''
      this.currentFilters.keyword = ''
      this.currentPage = 1
      this.applyFilters()

      this.$notification.info({
        message: '搜索已清空',
        description: '已清空搜索关键词',
        placement: 'topRight'
      })
    },

    selectCategory(category) {
      this.currentFilters.category = category
      this.currentPage = 1
      this.applyFilters()
    },

    selectPriceRange(range) {
      this.currentFilters.priceRange = range
      this.currentPage = 1
      // 选择预设价格范围时清空自定义输入
      this.customPriceMin = null
      this.customPriceMax = null
      this.applyFilters()
    },

    selectSort(sortType) {
      this.currentFilters.sortType = sortType
      this.currentPage = 1
      this.applyFilters()
    },

    getCategoryIcon(category) {
      const icons = {
        // 按分类值匹配
        'ai-chat': '💬',
        'ai-image': '🎨',
        'ai-video': '🎬',
        'ai-audio': '🎵',
        'social-share': '📱',
        'tools': '⚙️',
        'entertainment': '🎮',
        'combine': '🔗', // 🔥 组合插件图标
        'other': '🔧',

        // 按分类文本匹配（兼容旧数据）
        '内容生成': '✍️',
        '图片生成': '🎨',
        '视频处理': '🎬',
        '数据分析': '📊',
        '开发工具': '⚙️',
        '设计创意': '🎭',
        '营销工具': '📈',
        'AI对话': '💬',
        'AI绘画': '🎨',
        'AI视频': '🎬',
        'AI音频': '🎵',
        '社交分享': '📱',
        '工具类': '⚙️',
        '娱乐': '🎮',
        '组合插件': '🔗', // 🔥 组合插件图标
        '其他': '🔧'
      }
      return icons[category] || '🔧'
    },

    getCategoryText(categoryValue) {
      const category = this.categories.find(cat => cat.value === categoryValue)
      return category ? category.text : categoryValue
    },

    // 获取价格范围显示文字
    getPriceRangeText(priceRange) {
      const priceTexts = {
        '0-1': '¥0 - ¥1',
        '1-5': '¥1 - ¥5',
        '5+': '¥5以上'
      }

      // 如果是预设范围，返回对应文字
      if (priceTexts[priceRange]) {
        return priceTexts[priceRange]
      }

      // 如果是自定义范围，格式化显示
      if (priceRange && priceRange.includes('-')) {
        const [min, max] = priceRange.split('-')
        if (max === '999') {
          return `¥${min}以上`
        } else {
          return `¥${min} - ¥${max}`
        }
      }

      return priceRange
    },

    // 获取排序方式显示文字
    getSortTypeText(sortType) {
      const sortTexts = {
        'newest': '最新发布',
        'price-asc': '价格从低到高',
        'price-desc': '价格从高到低',
        'name-asc': '名称A-Z'
      }
      return sortTexts[sortType] || sortType
    },

    clearCategoryFilter() {
      this.currentFilters.category = ''
      this.currentPage = 1
      this.applyFilters()
    },

    clearKeywordFilter() {
      this.currentFilters.keyword = ''
      this.searchKeyword = ''
      this.currentPage = 1
      this.applyFilters()
    },

    clearPriceFilter() {
      this.currentFilters.priceRange = ''
      this.showCustomPrice = false
      this.customPriceMin = null
      this.customPriceMax = null
      this.currentPage = 1
      this.applyFilters()
    },

    clearSortFilter() {
      this.currentFilters.sortType = 'default'
      this.currentPage = 1
      this.applyFilters()
    },

    // 切换筛选面板折叠状态
    toggleSection(section) {
      this.collapsedSections[section] = !this.collapsedSections[section]
    },

    // 应用自定义价格
    applyCustomPrice() {
      if (this.customPriceMin !== null || this.customPriceMax !== null) {
        const min = this.customPriceMin || 0
        const max = this.customPriceMax || 999
        this.currentFilters.priceRange = `${min}-${max}`
        this.currentPage = 1
        this.applyFilters()
        this.$notification.success({
          message: '价格筛选已应用',
          description: `已应用价格范围：¥${min} - ${max === 999 ? '以上' : '¥' + max}`,
          placement: 'topRight'
        })
      }
    },

    // 切换自定义价格
    toggleCustomPrice() {
      this.showCustomPrice = !this.showCustomPrice
      if (this.showCustomPrice) {
        // 打开自定义价格时，设置一个特殊标识，不清除筛选
        // 这样"全部"就不会亮起，但也不会有实际的价格筛选
      } else {
        // 如果关闭自定义价格，清除自定义筛选
        if (this.currentFilters.priceRange && this.currentFilters.priceRange.includes('-') && !['0-1', '1-5', '5+'].includes(this.currentFilters.priceRange)) {
          this.currentFilters.priceRange = ''
          this.applyFilters()
        }
      }
    },

    // 保存商城筛选状态到localStorage
    saveMarketState(filters) {
      try {
        const state = {
          category: filters.category || '',
          search: filters.keyword || this.searchKeyword || '',
          priceRange: filters.priceRange || '',
          sortBy: filters.sortType || 'default',
          timestamp: Date.now()
        };

        localStorage.setItem('market_filter_state', JSON.stringify(state));
      } catch (error) {
        console.warn('保存商城筛选状态失败:', error);
      }
    },

    // 恢复商城筛选状态
    restoreMarketState() {
      try {
        const stateStr = localStorage.getItem('market_filter_state');
        if (!stateStr) return;

        const state = JSON.parse(stateStr);

        // 检查状态是否过期（24小时）
        const now = Date.now();
        const stateAge = now - (state.timestamp || 0);
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (stateAge > maxAge) {
          localStorage.removeItem('market_filter_state');
          return;
        }

        // 恢复筛选状态
        if (state.category) {
          this.currentFilters.category = state.category;
        }
        if (state.search) {
          this.currentFilters.keyword = state.search;
          this.searchKeyword = state.search;
        }
        if (state.priceRange) {
          this.currentFilters.priceRange = state.priceRange;
        }
        if (state.sortBy && state.sortBy !== 'default') {
          this.currentFilters.sortType = state.sortBy;
        }

        console.log('已恢复商城筛选状态:', state);

      } catch (error) {
        console.warn('恢复商城筛选状态失败:', error);
        localStorage.removeItem('market_filter_state');
      }
    },

    // 清空筛选状态
    clearMarketState() {
      try {
        localStorage.removeItem('market_filter_state');
      } catch (error) {
        console.warn('清空商城筛选状态失败:', error);
      }
    },

    // 🔥 查看组合插件详情（显示子插件选择弹窗）
    async viewCombinedPluginDetails(plugin) {
      console.log('🔗 查看组合插件详情:', plugin.combinedName)

      this.selectedCombinedPlugin = plugin
      this.showCombinedModal = true
      this.combinedModalLoading = true
      this.combinedSubPlugins = []

      // 禁止背景滚动
      document.body.style.overflow = 'hidden'

      // 加载子插件
      try {
        const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)
        this.combinedSubPlugins = subPlugins
        console.log('🔗 加载子插件成功:', subPlugins.length)
      } catch (error) {
        console.error('🔗 加载子插件失败:', error)
        this.$notification.error({
          message: '加载失败',
          description: '获取子插件列表失败',
          placement: 'topRight'
        })
      } finally {
        this.combinedModalLoading = false
      }
    },

    // 🔥 选择子插件（跳转到具体插件详情页）
    handleSelectSubPlugin(subPlugin) {
      console.log('🎯 选择子插件:', subPlugin.plubname)
      this.showCombinedModal = false

      // 跳转到插件详情页
      this.$router.push({
        name: 'PluginDetail',
        params: { id: subPlugin.id }
      })
    },

    // 🔥 关闭组合插件弹窗
    closeCombinedModal() {
      this.showCombinedModal = false
      this.selectedCombinedPlugin = null
      this.combinedSubPlugins = []

      // 恢复背景滚动
      document.body.style.overflow = ''
    },

    // 🔥 选择子插件
    selectSubPlugin(subPlugin) {
      console.log('🎯 选择子插件:', subPlugin.plubname)
      this.closeCombinedModal()

      // 跳转到插件详情页
      this.$router.push({
        name: 'PluginDetail',
        params: { id: subPlugin.id }
      })
    },

    // 🔥 获取插件图片（支持组合插件优先级处理）
    getPluginImage(plugin) {
      return getPluginImageUrl(plugin, this.defaultPluginImage)
    },

    // 🔥 跳转到剪映小助手页面
    goToJianYingDraft() {
      this.$router.push('/JianYingDraft')
      console.log('跳转到剪映小助手页面')
    },

    // 🔥 悬浮组件交互方法
    // 隐藏悬浮组件（仅在当前会话中隐藏，刷新页面后重新显示）
    hideJianYingFloat() {
      this.showJianYingFloat = false
      console.log('🔥 隐藏悬浮组件（仅当前会话）')
    }
  }
}
</script>

<style scoped>
.market-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 🔥 剪映小助手推广横幅样式 */
.jianying-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 0;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.jianying-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.banner-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.banner-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.banner-text {
  color: white;
}

.banner-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: white;
}

.banner-subtitle {
  font-size: 1rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.banner-right {
  flex-shrink: 0;
}

.jianying-btn.ant-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
  border: none !important;
  color: white !important;
  font-weight: 600;
  height: 48px !important;
  padding: 0 2rem !important;
  border-radius: 24px !important;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4) !important;
  transition: all 0.3s ease !important;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.jianying-btn.ant-btn:hover:not(:disabled),
.jianying-btn.ant-btn:focus:not(:disabled) {
  background: linear-gradient(135deg, #ee5a24, #d63031) !important;
  box-shadow: 0 6px 25px rgba(255, 107, 107, 0.5) !important;
  transform: translateY(-2px);
  border: none !important;
  color: white !important;
}

.jianying-btn.ant-btn:active:not(:disabled) {
  transform: translateY(0);
  background: linear-gradient(135deg, #d63031, #c0392b) !important;
}

.jianying-btn.ant-btn .anticon {
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .banner-left {
    flex-direction: column;
    gap: 0.75rem;
  }

  .banner-title {
    font-size: 1.3rem;
  }

  .banner-subtitle {
    font-size: 0.9rem;
  }

  .jianying-btn.ant-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 主内容区域 */
.main-content {
  background: #f8fafc;
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 2.5rem;
  align-items: start;
}

/* 左侧筛选栏 */
.sidebar {
  position: sticky;
  top: 2rem;
}

.filter-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.filter-section {
  border-bottom: 1px solid #f1f5f9;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.filter-header:hover {
  background: #f8fafc;
}

.filter-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-icon {
  color: #3b82f6;
  font-size: 1.1rem;
}

.filter-badge {
  background: #3b82f6;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  margin-left: 0.5rem;
  font-weight: 700;
}

.collapse-icon {
  color: #64748b;
  transition: transform 0.2s ease;
}

.filter-content {
  padding: 0 1.5rem 1.5rem;
}

/* 🔥 搜索区域样式 */
.search-section {
  background: transparent;
  padding: 0 0 2rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.search-layout {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.search-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

/* 🔥 自定义搜索输入框样式 */
.custom-search-input {
  flex: 1;
  position: relative;
  height: 56px;
  border-radius: 28px;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.custom-search-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  pointer-events: none;
  z-index: 1;
}

.custom-search-input:hover::before {
  transform: translateX(100%);
}

.custom-search-input:hover,
.custom-search-input:focus-within {
  background: #ffffff;
  border-color: #3b82f6;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.search-icon {
  position: absolute;
  left: 1.5rem;
  color: #3b82f6;
  font-size: 1.2rem;
  z-index: 2;
  display: flex;
  align-items: center;
  height: 100%;
}

.search-input-native {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0 3rem 0 3.5rem; /* 🔥 右侧增加padding为清空图标留空间 */
  color: #1e293b;
  position: relative;
  z-index: 2;
}

.search-input-native::placeholder {
  color: #64748b;
  font-weight: 400;
}

/* 🔥 清空搜索图标样式 */
.clear-search-icon {
  position: absolute;
  right: 1.5rem;
  color: #94a3b8;
  font-size: 1rem;
  z-index: 2;
  display: flex;
  align-items: center;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 0.25rem;
  border-radius: 50%;
}

.clear-search-icon:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  transform: scale(1.1);
}

.clear-search-icon:active {
  transform: scale(0.95);
}

/* 🔥 清空筛选按钮新样式 */
.clear-filters-btn.ant-btn {
  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
  border: none !important;
  color: white !important;
  font-weight: 600;
  height: 56px !important;
  padding: 0 2rem !important;
  border-radius: 28px !important;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-width: 140px;
  line-height: 56px !important;
}

.clear-filters-btn.ant-btn:hover:not(:disabled),
.clear-filters-btn.ant-btn:focus:not(:disabled) {
  background: linear-gradient(135deg, #4f46e5, #4338ca) !important;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4) !important;
  transform: translateY(-2px);
  border: none !important;
  color: white !important;
}

.clear-filters-btn.ant-btn:active:not(:disabled) {
  transform: translateY(0);
  background: linear-gradient(135deg, #4338ca, #3730a3) !important;
}

.clear-filters-btn.ant-btn:disabled,
.clear-filters-btn.ant-btn[disabled] {
  background: #e2e8f0 !important;
  color: #94a3b8 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  cursor: not-allowed !important;
  transform: none !important;
  border: 1px solid #e2e8f0 !important;
}

.clear-filters-btn.ant-btn .anticon {
  margin-right: 0.5rem;
}



/* 清空筛选按钮 */
/* 删除重复的红色样式 */

/* 搜索框 */
.search-input {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.category-tag {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: #f8fafc;
  text-align: center;
}

.category-tag:hover {
  background: #e2e8f0;
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.category-tag.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tag-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.tag-text {
  font-weight: 500;
  font-size: 0.85rem;
}

.tag-count {
  font-size: 0.7rem;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 600;
  min-width: 20px;
}

.category-tag.active .tag-count {
  background: rgba(255, 255, 255, 0.25);
}

/* 价格网格 */
.price-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.price-tag {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: #f0fdf4;
  text-align: center;
}

.price-tag:hover {
  background: #dcfce7;
  border-color: #10b981;
  transform: translateY(-2px);
}

.price-tag.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-color: #10b981;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 排序网格 */
.sort-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.sort-tag {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 0.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: #faf5ff;
  text-align: center;
}

.sort-tag:hover {
  background: #f3e8ff;
  border-color: #a855f7;
  transform: translateY(-2px);
}

.sort-tag.active {
  background: linear-gradient(135deg, #a855f7, #7c3aed);
  color: white;
  border-color: #a855f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

/* 自定义价格范围 */
.custom-price-range {
  margin-top: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.custom-price-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.custom-price-icon {
  font-size: 1.1rem;
}

.custom-price-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.price-inputs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-input-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #374151;
}

.price-input {
  width: 100%;
  border-radius: 10px;
  border: 2px solid #d1d5db;
  height: 40px;
  transition: all 0.3s ease;
}

.price-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.apply-custom-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 10px;
  font-weight: 600;
  height: 44px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.apply-custom-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.apply-custom-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
  transform: none;
}

/* 右侧主内容 */
.main-area {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 结果头部 */
.results-header {
  padding: 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.results-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.results-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

/* 删除重复的样式定义 */

/* 插件网格区域 */
.plugins-grid-wrapper {
  padding: 2rem;
  min-height: 400px;
}

/* 分页区域 */
.pagination-wrapper {
  padding: 1.5rem 2rem;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
  text-align: center;
}

/* 插件展示区域 */
.plugins-showcase {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
  padding: 2rem 0;
  min-height: 600px;
}

/* 结果头部 */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.header-left {
  flex: 1;
}

.results-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.75rem 0;
}

.active-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tag {
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
}

.header-right {
  flex-shrink: 0;
  text-align: right;
}

.results-count {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.count-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #3b82f6;
}

.plugins-grid-wrapper {
  margin-bottom: 2rem;
}

/* 现代化分页 */
.modern-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.modern-pagination::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.pagination-info {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.custom-pagination .ant-pagination-item {
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  background: white;
  margin: 0 3px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.custom-pagination .ant-pagination-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.custom-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.custom-pagination .ant-pagination-item-active a {
  color: white;
  font-weight: 600;
}

.custom-pagination .ant-pagination-prev,
.custom-pagination .ant-pagination-next {
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.custom-pagination .ant-pagination-prev:hover,
.custom-pagination .ant-pagination-next:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-layout {
    grid-template-columns: 300px 1fr;
    gap: 2rem;
  }
}

@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 280px 1fr;
    gap: 1.5rem;
  }

  .filter-section {
    padding: 1rem;
  }

  .plugins-grid-wrapper {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .main-content {
    padding: 1rem 0;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .main-area {
    order: 1;
  }

  .results-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .header-right {
    text-align: left;
  }

  .plugins-grid-wrapper {
    padding: 1rem;
  }

  .pagination-wrapper {
    padding: 1rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .filter-section {
    padding: 0.75rem;
  }

  .results-header {
    padding: 0.75rem;
  }

  .plugins-grid-wrapper {
    padding: 0.75rem;
  }
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

.plugin-card:hover .plugin-image img {
  transform: scale(1.05);
}

.plugin-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plugin-info {
  padding: 1.5rem;
}

.plugin-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.plugin-description {
  color: #64748b;
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.plugin-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.plugin-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #3b82f6;
}

.plugin-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #fbbf24;
  font-weight: 600;
}

.btn-plugin-buy {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  color: white;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-plugin-buy:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .header-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .plugins-section {
    padding: 1rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  /* 🔥 搜索区域响应式 */
  .search-section {
    padding: 2rem 0;
  }

  .search-input-group {
    flex-direction: column;
    gap: 1rem;
    max-width: 100%;
    padding: 0 1rem;
  }

  .custom-search-input {
    height: 50px;
  }

  .search-input-native {
    font-size: 1rem;
    padding: 0 2.5rem 0 3rem; /* 🔥 移动端调整padding */
  }

  .clear-search-icon {
    right: 1rem; /* 🔥 移动端调整位置 */
    font-size: 0.9rem;
  }

  .clear-filters-btn.ant-btn {
    height: 50px !important;
    width: 100%;
    min-width: auto;
    line-height: 50px !important;
  }
}

/* 🔥 自定义组合插件弹窗样式 */
.combined-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.combined-modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1200px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.combined-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  border-bottom: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.combined-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10px);
}

.header-content1 {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 2;
}

.header-icon {
  font-size: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-text h2 {
  margin: 0 0 4px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
}

.header-subtitle {
  margin: 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.combined-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white !important;
  font-size: 1.8rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
  font-weight: 300;
}

.combined-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.combined-modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

/* 🔥 子插件网格布局 */
.sub-plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 8px;
}

/* 🔥 子插件卡片样式 - 与一级插件保持一致 */
.sub-plugin-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
}

.sub-plugin-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

/* 🔥 子插件图片区域 */
.sub-plugin-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.sub-plugin-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.sub-plugin-card:hover .sub-plugin-image img {
  transform: scale(1.1);
}

/* 🔥 子插件分类标签 */
.sub-plugin-category {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  color: #1e293b;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 2;
}

/* 🔥 子插件状态标签 */
.sub-plugin-status {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  z-index: 2;
}

/* 🔥 子插件内容区域 */
.sub-plugin-content {
  padding: 20px;
}

.sub-plugin-header {
  margin-bottom: 12px;
}

.sub-plugin-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.sub-plugin-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.author-icon {
  font-size: 1rem;
}

.sub-plugin-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 0 0 20px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 🔥 子插件底部区域 */
.sub-plugin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.sub-plugin-price {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.price-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
}

.price-unit {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* 🔥 子插件按钮 */
.sub-plugin-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.sub-plugin-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-icon {
  font-size: 1rem;
}

/* 🔥 响应式设计 */
@media (max-width: 768px) {
  .combined-modal-content {
    width: 95%;
    margin: 20px;
  }

  .sub-plugins-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .combined-modal-header {
    padding: 16px;
  }

  .combined-modal-header h2 {
    font-size: 1.2rem;
  }

  .combined-modal-body {
    padding: 16px;
  }

  .sub-plugin-card {
    border-radius: 16px;
  }

  .sub-plugin-image {
    height: 160px;
  }

  .sub-plugin-content {
    padding: 16px;
  }

  .sub-plugin-title {
    font-size: 1.1rem;
  }

  .sub-plugin-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sub-plugin-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
