{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue", "mtime": 1753959561940}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport AigcAgentForm from './AigcAgentForm';\nexport default {\n  name: 'AigcAgentModal',\n  components: {\n    AigcAgentForm: AigcAgentForm\n  },\n  data: function data() {\n    return {\n      title: '',\n      width: 800,\n      visible: false,\n      disableSubmit: false\n    };\n  },\n  methods: {\n    add: function add() {\n      var _this = this;\n\n      this.visible = true;\n      this.$nextTick(function () {\n        _this.$refs.realForm.add();\n      });\n    },\n    edit: function edit(record) {\n      var _this2 = this;\n\n      this.visible = true;\n      this.$nextTick(function () {\n        _this2.$refs.realForm.edit(record);\n      });\n    },\n    close: function close() {\n      // 🔥 关闭时回滚头像变更\n      if (this.$refs.realForm && this.$refs.realForm.handleClose) {\n        this.$refs.realForm.handleClose();\n      }\n\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk: function handleOk() {\n      this.$refs.realForm.handleOk();\n    },\n    submitCallback: function submitCallback() {\n      this.$emit('ok');\n      this.visible = false;\n    },\n    handleCancel: function handleCancel() {\n      this.close();\n    }\n  }\n};", {"version": 3, "sources": ["AigcAgentModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAgBA,OAAA,aAAA,MAAA,iBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,aAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,KAAA,EAAA,GAFA;AAGA,MAAA,OAAA,EAAA,KAHA;AAIA,MAAA,aAAA,EAAA;AAJA,KAAA;AAMA,GAZA;AAaA,EAAA,OAAA,EAAA;AACA,IAAA,GADA,iBACA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA;AACA,OAFA;AAGA,KANA;AAOA,IAAA,IAPA,gBAOA,MAPA,EAOA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,MAAA;AACA,OAFA;AAGA,KAZA;AAaA,IAAA,KAbA,mBAaA;AACA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,IAAA,KAAA,KAAA,CAAA,QAAA,CAAA,WAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,WAAA;AACA;;AACA,WAAA,KAAA,CAAA,OAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,KApBA;AAqBA,IAAA,QArBA,sBAqBA;AACA,WAAA,KAAA,CAAA,QAAA,CAAA,QAAA;AACA,KAvBA;AAwBA,IAAA,cAxBA,4BAwBA;AACA,WAAA,KAAA,CAAA,IAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,KA3BA;AA4BA,IAAA,YA5BA,0BA4BA;AACA,WAAA,KAAA;AACA;AA9BA;AAbA,CAAA", "sourcesContent": ["<template>\n  <j-modal\n    :title=\"title\"\n    :width=\"1200\"\n    :visible=\"visible\"\n    :maskClosable=\"false\"\n    switchFullscreen\n    @ok=\"handleOk\"\n    :okButtonProps=\"{ class:{'jee-hidden': disableSubmit} }\"\n    @cancel=\"handleCancel\">\n    <aigc-agent-form ref=\"realForm\" @ok=\"submitCallback\" :disabled=\"disableSubmit\"/>\n  </j-modal>\n</template>\n\n<script>\n\n  import AigcAgentForm from './AigcAgentForm'\n\n  export default {\n    name: 'AigcAgentModal',\n    components: {\n      AigcAgentForm\n    },\n    data() {\n      return {\n        title:'',\n        width:800,\n        visible: false,\n        disableSubmit: false\n      }\n    },\n    methods:{\n      add () {\n        this.visible=true\n        this.$nextTick(()=>{\n          this.$refs.realForm.add();\n        })\n      },\n      edit (record) {\n        this.visible=true\n        this.$nextTick(()=>{\n          this.$refs.realForm.edit(record);\n        })\n      },\n      close () {\n        // 🔥 关闭时回滚头像变更\n        if (this.$refs.realForm && this.$refs.realForm.handleClose) {\n          this.$refs.realForm.handleClose();\n        }\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        this.$refs.realForm.handleOk();\n      },\n      submitCallback(){\n        this.$emit('ok');\n        this.visible = false;\n      },\n      handleCancel () {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n</style>"], "sourceRoot": "src/views/aigcview/agent/modules"}]}