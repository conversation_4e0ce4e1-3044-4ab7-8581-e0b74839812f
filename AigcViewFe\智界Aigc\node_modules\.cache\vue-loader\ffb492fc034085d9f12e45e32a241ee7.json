{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue?vue&type=template&id=409c6ff1&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue", "mtime": 1753973678761}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<WebsitePage>\n  <div class=\"market-container\">\n    <!-- 简洁页面标题 -->\n    <div class=\"simple-header\">\n      <h1 class=\"simple-title\">AI插件中心</h1>\n      <p class=\"simple-subtitle\">发现优质AI插件，提升创作效率，让每个想法都能完美实现</p>\n    </div>\n\n    <!-- 🔥 搜索区域 -->\n    <section class=\"search-section\">\n      <div class=\"container\">\n        <div class=\"search-layout\">\n          <div class=\"search-input-group\">\n            <!-- 🔥 使用原生输入框，完全自定义样式 -->\n            <div class=\"custom-search-input\">\n              <div class=\"search-icon\">\n                <a-icon type=\"search\" />\n              </div>\n              <input\n                v-model=\"searchKeyword\"\n                @input=\"handleSearchInput\"\n                @keyup.enter=\"handleSearch\"\n                placeholder=\"搜索插件名称、描述...\"\n                class=\"search-input-native\"\n              />\n              <!-- 🔥 清空搜索框图标 -->\n              <div\n                v-if=\"searchKeyword\"\n                class=\"clear-search-icon\"\n                @click=\"clearSearchInput\"\n                title=\"清空搜索\"\n              >\n                <a-icon type=\"close-circle\" />\n              </div>\n            </div>\n\n            <!-- 🔥 清空筛选按钮移到搜索框右边 -->\n            <a-button\n              @click=\"clearAllFilters\"\n              size=\"large\"\n              class=\"clear-filters-btn\"\n              :disabled=\"!hasActiveFilters\">\n              <a-icon type=\"clear\" />\n              清空所有筛选\n            </a-button>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 主内容区域 -->\n    <section class=\"main-content\">\n      <div class=\"container\">\n        <div class=\"content-layout\">\n          <!-- 左侧筛选栏 -->\n          <aside class=\"sidebar\">\n            <div class=\"filter-panel\">\n\n              <!-- 分类筛选 -->\n              <div class=\"filter-section\">\n                <div class=\"filter-header\" @click=\"toggleSection('category')\">\n                  <h3 class=\"filter-title\">\n                    <a-icon type=\"appstore\" class=\"filter-icon\" />\n                    插件分类\n                    <span class=\"filter-badge\" v-if=\"currentFilters.category\">1</span>\n                  </h3>\n                  <a-icon\n                    :type=\"collapsedSections.category ? 'down' : 'up'\"\n                    class=\"collapse-icon\"\n                  />\n                </div>\n                <a-collapse-transition>\n                  <div v-show=\"!collapsedSections.category\" class=\"filter-content\">\n                    <div class=\"category-grid\">\n                      <div\n                        class=\"category-tag\"\n                        :class=\"{ active: currentFilters.category === '' }\"\n                        @click=\"selectCategory('')\"\n                      >\n                        <span class=\"tag-icon\">🌟</span>\n                        <span class=\"tag-text\">全部</span>\n                        <span class=\"tag-count\">{{ totalPlugins }}</span>\n                      </div>\n                      <div\n                        v-for=\"category in categories\"\n                        :key=\"category.value\"\n                        class=\"category-tag\"\n                        :class=\"{ active: currentFilters.category === category.value }\"\n                        @click=\"selectCategory(category.value)\"\n                      >\n                        <span class=\"tag-icon\">{{ getCategoryIcon(category.value) }}</span>\n                        <span class=\"tag-text\">{{ category.text }}</span>\n                        <span class=\"tag-count\">{{ categoryCounts[category.value] || 0 }}</span>\n                      </div>\n                    </div>\n                  </div>\n                </a-collapse-transition>\n              </div>\n\n              <!-- 价格筛选 -->\n              <div class=\"filter-section\">\n                <div class=\"filter-header\" @click=\"toggleSection('price')\">\n                  <h3 class=\"filter-title\">\n                    <a-icon type=\"dollar\" class=\"filter-icon\" />\n                    价格范围\n                    <span class=\"filter-badge\" v-if=\"currentFilters.priceRange\">1</span>\n                  </h3>\n                  <a-icon\n                    :type=\"collapsedSections.price ? 'down' : 'up'\"\n                    class=\"collapse-icon\"\n                  />\n                </div>\n                <a-collapse-transition>\n                  <div v-show=\"!collapsedSections.price\" class=\"filter-content\">\n                    <div class=\"price-grid\">\n                      <div\n                        class=\"price-tag\"\n                        :class=\"{ active: currentFilters.priceRange === '' && !showCustomPrice }\"\n                        @click=\"selectPriceRange('')\"\n                      >\n                        <span class=\"tag-icon\">💰</span>\n                        <span class=\"tag-text\">全部</span>\n                      </div>\n                      <div\n                        class=\"price-tag\"\n                        :class=\"{ active: currentFilters.priceRange === '0-1' }\"\n                        @click=\"selectPriceRange('0-1')\"\n                      >\n                        <span class=\"tag-icon\">🪙</span>\n                        <span class=\"tag-text\">¥0-1</span>\n                      </div>\n                      <div\n                        class=\"price-tag\"\n                        :class=\"{ active: currentFilters.priceRange === '1-5' }\"\n                        @click=\"selectPriceRange('1-5')\"\n                      >\n                        <span class=\"tag-icon\">💵</span>\n                        <span class=\"tag-text\">¥1-5</span>\n                      </div>\n                      <div\n                        class=\"price-tag\"\n                        :class=\"{ active: currentFilters.priceRange === '5+' }\"\n                        @click=\"selectPriceRange('5+')\"\n                      >\n                        <span class=\"tag-icon\">💎</span>\n                        <span class=\"tag-text\">¥5+</span>\n                      </div>\n                    </div>\n                    <!-- 自定义价格范围 -->\n                    <div class=\"custom-price-range\">\n                      <div class=\"custom-price-header\">\n                        <span class=\"custom-price-icon\">⚙️</span>\n                        <span class=\"custom-price-label\">自定义价格</span>\n                      </div>\n                      <div class=\"price-inputs\">\n                        <div class=\"price-input-row\">\n                          <label class=\"input-label\">最低价格</label>\n                          <a-input-number\n                            v-model=\"customPriceMin\"\n                            :min=\"0\"\n                            :max=\"999\"\n                            placeholder=\"请输入最低价格\"\n                            size=\"default\"\n                            class=\"price-input\"\n                            @pressEnter=\"applyCustomPrice\"\n                          />\n                        </div>\n                        <div class=\"price-input-row\">\n                          <label class=\"input-label\">最高价格</label>\n                          <a-input-number\n                            v-model=\"customPriceMax\"\n                            :min=\"customPriceMin || 0\"\n                            :max=\"999\"\n                            placeholder=\"请输入最高价格\"\n                            size=\"default\"\n                            class=\"price-input\"\n                            @pressEnter=\"applyCustomPrice\"\n                          />\n                        </div>\n                        <a-button\n                          type=\"primary\"\n                          @click=\"applyCustomPrice\"\n                          :disabled=\"!customPriceMin && !customPriceMax\"\n                          class=\"apply-custom-btn\"\n                          block\n                        >\n                          确定筛选\n                        </a-button>\n                      </div>\n                    </div>\n                  </div>\n                </a-collapse-transition>\n              </div>\n\n              <!-- 排序方式 -->\n              <div class=\"filter-section\">\n                <div class=\"filter-header\" @click=\"toggleSection('sort')\">\n                  <h3 class=\"filter-title\">\n                    <a-icon type=\"sort-ascending\" class=\"filter-icon\" />\n                    排序方式\n                    <span class=\"filter-badge\" v-if=\"currentFilters.sortType !== 'default'\">1</span>\n                  </h3>\n                  <a-icon\n                    :type=\"collapsedSections.sort ? 'down' : 'up'\"\n                    class=\"collapse-icon\"\n                  />\n                </div>\n                <a-collapse-transition>\n                  <div v-show=\"!collapsedSections.sort\" class=\"filter-content\">\n                    <div class=\"sort-grid\">\n                      <div\n                        class=\"sort-tag\"\n                        :class=\"{ active: currentFilters.sortType === 'default' }\"\n                        @click=\"selectSort('default')\"\n                      >\n                        <span class=\"tag-icon\">🌟</span>\n                        <span class=\"tag-text\">默认</span>\n                      </div>\n                      <div\n                        class=\"sort-tag\"\n                        :class=\"{ active: currentFilters.sortType === 'newest' }\"\n                        @click=\"selectSort('newest')\"\n                      >\n                        <span class=\"tag-icon\">⏰</span>\n                        <span class=\"tag-text\">最新</span>\n                      </div>\n                      <div\n                        class=\"sort-tag\"\n                        :class=\"{ active: currentFilters.sortType === 'price-asc' }\"\n                        @click=\"selectSort('price-asc')\"\n                      >\n                        <span class=\"tag-icon\">📈</span>\n                        <span class=\"tag-text\">价格↑</span>\n                      </div>\n                      <div\n                        class=\"sort-tag\"\n                        :class=\"{ active: currentFilters.sortType === 'price-desc' }\"\n                        @click=\"selectSort('price-desc')\"\n                      >\n                        <span class=\"tag-icon\">📉</span>\n                        <span class=\"tag-text\">价格↓</span>\n                      </div>\n                    </div>\n                  </div>\n                </a-collapse-transition>\n              </div>\n\n\n            </div>\n          </aside>\n\n          <!-- 右侧主内容 -->\n          <main class=\"main-area\">\n            <!-- 结果头部 -->\n            <div class=\"results-header\">\n              <div class=\"header-left\">\n                <h2 class=\"results-title\">\n                  <span v-if=\"currentFilters.category\">{{ getCategoryText(currentFilters.category) }}插件</span>\n                  <span v-else-if=\"currentFilters.keyword\">搜索结果</span>\n                  <span v-else>全部插件</span>\n                </h2>\n                <div class=\"active-filters\" v-if=\"currentFilters.keyword || currentFilters.priceRange || currentFilters.sortType !== 'default'\">\n                  <a-tag\n                    v-if=\"currentFilters.keyword\"\n                    closable\n                    color=\"green\"\n                    @close=\"clearKeywordFilter\"\n                    class=\"filter-tag\"\n                  >\n                    \"{{ currentFilters.keyword }}\"\n                  </a-tag>\n                  <a-tag\n                    v-if=\"currentFilters.priceRange\"\n                    closable\n                    color=\"orange\"\n                    @close=\"clearPriceFilter\"\n                    class=\"filter-tag\"\n                  >\n                    {{ getPriceRangeText(currentFilters.priceRange) }}\n                  </a-tag>\n                  <a-tag\n                    v-if=\"currentFilters.sortType !== 'default'\"\n                    closable\n                    color=\"purple\"\n                    @close=\"clearSortFilter\"\n                    class=\"filter-tag\"\n                  >\n                    {{ getSortTypeText(currentFilters.sortType) }}\n                  </a-tag>\n                </div>\n              </div>\n              <div class=\"header-right\">\n                <div class=\"results-count\">\n                  <span class=\"count-number\">{{ filteredPlugins.length }}</span> 个一级插件，共 <span class=\"count-number\">{{ filteredTotalPlugins }}</span> 个插件\n                </div>\n              </div>\n            </div>\n\n            <!-- 插件网格 -->\n            <div class=\"plugins-grid-wrapper\">\n              <PluginGrid\n                :plugins=\"currentPagePlugins\"\n                :loading=\"loading\"\n                :error=\"error\"\n                @plugin-use=\"handlePluginUse\"\n                @plugin-detail=\"handlePluginDetail\"\n                @combined-plugin-detail=\"viewCombinedPluginDetails\"\n                @retry=\"handleRetry\"\n                @clear-filters=\"clearAllFilters\"\n              />\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination-wrapper\" v-if=\"filteredPlugins.length > pageSize\">\n              <a-pagination\n                :current=\"currentPage\"\n                :total=\"filteredPlugins.length\"\n                :page-size=\"pageSize\"\n                :page-size-options=\"['8', '12', '16', '24']\"\n                :show-size-changer=\"true\"\n                :show-quick-jumper=\"true\"\n                :show-total=\"(total, range) => `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n                @change=\"handlePageChange\"\n                @showSizeChange=\"handlePageSizeChange\"\n              />\n            </div>\n          </main>\n        </div>\n      </div>\n    </section>\n  </div>\n\n  <!-- 🔥 组合插件子插件选择弹窗 -->\n  <div v-if=\"showCombinedModal\" class=\"combined-modal-overlay\" @click=\"closeCombinedModal\">\n    <div class=\"combined-modal-content\" @click.stop>\n      <div class=\"combined-modal-header\">\n        <div class=\"header-content1\">\n          <div class=\"header-icon\">🔗</div>\n          <div class=\"header-text\">\n            <h2>选择插件</h2>\n            <p class=\"header-subtitle\">{{ selectedCombinedPlugin && selectedCombinedPlugin.combinedName }}</p>\n          </div>\n        </div>\n        <button class=\"combined-modal-close\" @click=\"closeCombinedModal\">×</button>\n      </div>\n      <div class=\"combined-modal-body\">\n        <div v-if=\"combinedModalLoading\" class=\"loading-state\">\n          <a-spin size=\"large\" />\n          <p>正在加载子插件...</p>\n        </div>\n        <div v-else-if=\"combinedSubPlugins.length === 0\" class=\"empty-state\">\n          <p>暂无子插件</p>\n        </div>\n        <div v-else class=\"sub-plugins-grid\">\n          <div\n            v-for=\"subPlugin in combinedSubPlugins\"\n            :key=\"subPlugin.id\"\n            class=\"sub-plugin-card\"\n            @click=\"selectSubPlugin(subPlugin)\">\n            <!-- 🔥 插件图片区域 -->\n            <div class=\"sub-plugin-image\">\n              <img :src=\"getPluginImage(subPlugin)\" :alt=\"subPlugin.plubname\" />\n              <!-- 分类标签 -->\n              <div class=\"sub-plugin-category\">\n                <span class=\"category-icon\">🔧</span>\n                <span>{{ subPlugin.plubCategory_dictText || '其他' }}</span>\n              </div>\n              <!-- 状态标签 -->\n              <div class=\"sub-plugin-status\">上架</div>\n            </div>\n\n            <!-- 🔥 插件内容区域 -->\n            <div class=\"sub-plugin-content\">\n              <div class=\"sub-plugin-header\">\n                <h3 class=\"sub-plugin-title\">{{ subPlugin.plubname }}</h3>\n                <div class=\"sub-plugin-author\">\n                  <span class=\"author-icon\">👤</span>\n                  <span>创作者 {{ subPlugin.plubwrite_dictText || '未知' }}</span>\n                </div>\n              </div>\n\n              <p class=\"sub-plugin-description\">{{ subPlugin.plubinfo || '暂无描述' }}</p>\n\n              <div class=\"sub-plugin-footer\">\n                <div class=\"sub-plugin-price\">\n                  <span class=\"price-amount\">{{ getSubPluginPriceText(subPlugin) }}</span>\n                </div>\n                <button class=\"sub-plugin-btn\">\n                  <span class=\"btn-icon\">👁</span>\n                  <span>查看详情</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 🔥 悬浮式剪映小助手推广组件 -->\n  <div v-if=\"showJianYingFloat\" class=\"jianying-float-container\">\n    <button class=\"jianying-float-btn\" @click=\"goToJianYingDraft\">\n      <!-- 关闭按钮 -->\n      <div class=\"float-close-btn\" @click.stop=\"hideJianYingFloat\">\n        <a-icon type=\"close\" />\n      </div>\n\n      <!-- 按钮图标 -->\n      <div class=\"btn-icon\">\n        <a-icon type=\"download\" />\n      </div>\n\n      <!-- 按钮文字 -->\n      <div class=\"btn-text\">剪映小助手下载</div>\n\n      <!-- 发光效果 -->\n      <div class=\"btn-glow\"></div>\n\n      <!-- 粒子效果容器 -->\n      <div class=\"btn-particles\" ref=\"particles\"></div>\n\n      <!-- 波纹效果 -->\n      <div class=\"jianying-waves\"></div>\n    </button>\n  </div>\n</WebsitePage>\n", null]}