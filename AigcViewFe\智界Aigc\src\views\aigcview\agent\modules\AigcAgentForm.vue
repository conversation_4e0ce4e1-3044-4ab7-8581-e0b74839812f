<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8" >
            <a-form-model-item label="作者类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="authorType">
              <j-dict-select-tag type="list" v-model="model.authorType" dictCode="author_type" placeholder="请选择作者类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="智能体ID" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="agentId">
              <a-input v-model="model.agentId" placeholder="请输入智能体ID" ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="智能体名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="agentName">
              <a-input v-model="model.agentName" placeholder="请输入智能体名称" ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="智能体描述" :labelCol="labelCol2" :wrapperCol="wrapperCol2" prop="agentDescription">
              <a-textarea v-model="model.agentDescription" rows="4" placeholder="请输入智能体描述" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="智能体头像" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="agentAvatar">
              <j-image-upload-deferred
                ref="avatarUpload"
                v-model="model.agentAvatar"
                :isMultiple="false"
                bizPath="agent-avatar"
                text="上传头像">
              </j-image-upload-deferred>
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="展示视频" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="demoVideo">
              <j-upload v-model="model.demoVideo" :beforeUpload="beforeVideoUpload" text="上传视频(最大100MB)"></j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="体验链接" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="experienceLink">
              <a-input v-model="model.experienceLink" placeholder="请输入体验链接" ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="价格（元）" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="price">
              <a-input-number v-model="model.price" placeholder="请输入价格（元）" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" >
            <a-form-model-item label="审核状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditStatus">
              <j-dict-select-tag type="list" v-model="model.auditStatus" dictCode="audit_status" placeholder="请选择审核状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核备注" :labelCol="labelCol2" :wrapperCol="wrapperCol2" prop="auditRemark">
              <a-textarea v-model="model.auditRemark" rows="4" placeholder="请输入审核备注" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
      <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="工作流表" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          :ref="refKeys[0]"
          :loading="aigcWorkflowTable.loading"
          :columns="aigcWorkflowTable.columns"
          :dataSource="aigcWorkflowTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :actionButton="true"/>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>

  import { getAction, httpAction } from '@/api/manage'
  import { FormTypes,getRefPromise,VALIDATE_NO_PASSED,validateFormModelAndTables } from '@/utils/JEditableTableUtil'
  import { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin'
  import { validateDuplicateValue } from '@/utils/util'
  import JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'

  export default {
    name: 'AigcAgentForm',
    mixins: [JEditableTableModelMixin],
    components: {
      JImageUploadDeferred
    },
    data() {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        labelCol2: {
          xs: { span: 24 },
          sm: { span: 3 },
        },
        wrapperCol2: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        model:{
          authorType: '1', // 默认作者类型：官方
          auditStatus: '2' // 默认审核状态：审核通过
        },
        // 新增时子表默认添加几行空数据
        addDefaultRowNum: 1,
        validatorRules: {
           authorType: [
              { required: true, message: '请输入作者类型!'},
           ],
           agentId: [
              { required: true, message: '请输入智能体ID!'},
           ],
           agentName: [
              { required: true, message: '请输入智能体名称!'},
           ],
           agentDescription: [
              { required: true, message: '请输入智能体描述!'},
           ],
           agentAvatar: [
              { required: true, message: '请输入智能体头像!'},
           ],
           price: [
              { required: true, message: '请输入价格（元）!'},
           ],
           auditStatus: [
              { required: true, message: '请输入审核状态!'},
           ],
        },
        refKeys: ['aigcWorkflow', ],
        tableKeys:['aigcWorkflow', ],
        activeKey: 'aigcWorkflow',
        // 工作流表
        aigcWorkflowTable: {
          loading: false,
          dataSource: [],
          columns: [
            {
              title: '智能体ID',
              key: 'agentId',
              type: FormTypes.hidden,
              defaultValue: ''
            },
            {
              title: '工作流ID',
              key: 'workflowId',
              type: FormTypes.input,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
            {
              title: '工作流名称',
              key: 'workflowName',
              type: FormTypes.input,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
            {
              title: '工作流描述',
              key: 'workflowDescription',
              type: FormTypes.input,
              width:"200px",
              placeholder: '请输入${title}',
              defaultValue:'',
            },
            {
              title: '工作流压缩包',
              key: 'workflowPackage',
              type: FormTypes.file,
              token:true,
              responseName:"message",
              width:"200px",
              placeholder: '请选择文件',
              defaultValue:'',
            },
          ]
        },
        url: {
          add: "/aigc_agent/aigcAgent/add",
          edit: "/aigc_agent/aigcAgent/edit",
          queryById: "/aigc_agent/aigcAgent/queryById",
          aigcWorkflow: {
            list: '/aigc_agent/aigcAgent/queryAigcWorkflowByMainId'
          },
        }
      }
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
    },
    methods: {
      addBefore(){
        this.aigcWorkflowTable.dataSource=[]
      },
      getAllTable() {
        let values = this.tableKeys.map(key => getRefPromise(this, key))
        return Promise.all(values)
      },
      /** 调用完edit()方法之后会自动调用此方法 */
      editAfter() {
        this.$nextTick(() => {
        })
        // 加载子表数据
        if (this.model.id) {
          let params = { id: this.model.id }
          this.requestSubTableData(this.url.aigcWorkflow.list, params, this.aigcWorkflowTable)
        }
      },
      //校验所有一对一子表表单
      validateSubForm(allValues){
          return new Promise((resolve,reject)=>{
            Promise.all([
            ]).then(() => {
              resolve(allValues)
            }).catch(e => {
              if (e.error === VALIDATE_NO_PASSED) {
                // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]
              } else {
                console.error(e)
              }
            })
          })
      },
      /** 整理成formData */
      classifyIntoFormData(allValues) {
        let main = Object.assign(this.model, allValues.formValue)
        return {
          ...main, // 展开
          aigcWorkflowList: allValues.tablesValue[0].values,
        }
      },
      validateError(msg){
        this.$message.error(msg)
      },

      // 视频上传前验证
      beforeVideoUpload(file) {
        // 检查文件类型
        const isVideo = file.type.indexOf('video/') === 0
        if (!isVideo) {
          this.$message.error('只能上传视频文件!')
          return false
        }

        // 检查文件大小（100MB）
        const isLt100M = file.size / 1024 / 1024 < 100
        if (!isLt100M) {
          this.$message.error('视频大小不能超过 100MB!')
          return false
        }

        console.log('🎯 视频上传验证通过:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB')
        return true
      },

      // 🔥 重写handleOk方法，集成延迟上传逻辑
      async handleOk() {
        console.log('🎯 AigcAgentForm: 开始保存智能体...')

        /** 先处理头像上传，更新model值 */
        try {
          // 🔥 先上传待上传的头像，更新model.agentAvatar
          await this.uploadPendingImages()
        } catch (error) {
          console.error('🎯 AigcAgentForm: 头像上传失败:', error)
          this.$message.error('头像上传失败: ' + (error.message || '未知错误'))
          return
        }

        /** 触发表单验证 */
        this.getAllTable().then(tables => {
          // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证
          const originalAvatar = this.model.agentAvatar
          if (!this.model.agentAvatar && this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {
            console.log('🎯 AigcAgentForm: 检测到待上传头像，临时设置头像值以通过验证')
            this.model.agentAvatar = 'pending_upload' // 临时值
          }

          /** 一次性验证主表和所有的次表 */
          return validateFormModelAndTables(this.$refs.form, this.model, tables).then(result => {
            // 恢复原始值
            this.model.agentAvatar = originalAvatar
            return result
          }).catch(error => {
            // 恢复原始值
            this.model.agentAvatar = originalAvatar
            throw error
          })
        }).then(allValues => {
          /** 一次性验证一对一的所有子表 */
          return this.validateSubForm(allValues)
        }).then(async (allValues) => {
          try {
            // 整理表单数据
            let formData = this.classifyIntoFormData(allValues)

            // 发起保存请求
            const result = await this.request(formData)

            // 🔥 保存成功后，确认删除被替换的原始头像文件
            await this.confirmDeleteOriginalFiles()

            return result
          } catch (error) {
            console.error('🎯 AigcAgentForm: 保存失败:', error)
            throw error
          }
        }).catch(e => {
          if (e.error === VALIDATE_NO_PASSED) {
            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
            this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]
          } else {
            console.error(e)
          }
        })
      },

      // 🔥 上传待上传的头像
      async uploadPendingImages() {
        console.log('🎯 AigcAgentForm: 开始检查待上传头像...')

        if (this.$refs.avatarUpload) {
          if (this.$refs.avatarUpload.hasPendingFiles()) {
            console.log('🎯 AigcAgentForm: 发现待上传的头像，开始上传...')
            try {
              const uploadedFiles = await this.$refs.avatarUpload.performUpload()
              console.log('🎯 AigcAgentForm: 头像上传结果:', uploadedFiles)

              // 上传完成后，获取最终的值
              const finalValue = this.$refs.avatarUpload.getCurrentValue()
              this.model.agentAvatar = finalValue
              console.log('🎯 AigcAgentForm: 头像最终值:', this.model.agentAvatar)
            } catch (error) {
              console.error('🎯 AigcAgentForm: 头像上传失败:', error)
              throw error
            }
          } else {
            // 没有待上传文件，检查是否有删除操作
            const currentValue = this.$refs.avatarUpload.getCurrentValue()
            this.model.agentAvatar = currentValue
            console.log('🎯 AigcAgentForm: 头像无新上传，当前值:', this.model.agentAvatar)
          }
        }

        console.log('🎯 AigcAgentForm: 头像处理完成')
      },

      // 🔥 确认删除被替换的原始头像文件
      async confirmDeleteOriginalFiles() {
        if (this.$refs.avatarUpload) {
          try {
            await this.$refs.avatarUpload.confirmDeleteOriginalFiles()
            console.log('🎯 AigcAgentForm: 原始头像文件清理完成')
          } catch (error) {
            console.warn('🎯 AigcAgentForm: 原始头像文件清理失败:', error)
            // 删除失败不影响主流程
          }
        }
      },

      // 🔥 关闭表单时回滚头像变更
      handleClose() {
        console.log('🎯 AigcAgentForm: 表单关闭，回滚头像变更')

        if (this.$refs.avatarUpload) {
          this.$refs.avatarUpload.rollbackChanges()
        }

        // 发出关闭事件
        this.$emit('close')
      }

    }
  }
</script>

<style scoped>
</style>