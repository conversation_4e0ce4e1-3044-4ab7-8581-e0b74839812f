<template>
  <div class="creator-center">
    <div class="coming-soon-container">
      <div class="coming-soon-content">
        <!-- 图标 -->
        <div class="coming-soon-icon">
          <a-icon type="build" />
        </div>

        <!-- 标题 -->
        <h2 class="coming-soon-title">创作者中心</h2>
        
        <!-- 描述 -->
        <p class="coming-soon-description">
          创作者中心正在紧张开发中，即将为您带来：
        </p>

        <!-- 功能预告 -->
        <div class="feature-preview">
          <div class="feature-item">
            <a-icon type="plus-circle" class="feature-icon" />
            <div class="feature-text">
              <h4>智能体创建</h4>
              <p>轻松创建和发布您的AI智能体</p>
            </div>
          </div>
          
          <div class="feature-item">
            <a-icon type="bar-chart" class="feature-icon" />
            <div class="feature-text">
              <h4>数据统计</h4>
              <p>查看智能体使用情况和收益统计</p>
            </div>
          </div>
          
          <div class="feature-item">
            <a-icon type="setting" class="feature-icon" />
            <div class="feature-text">
              <h4>智能体管理</h4>
              <p>管理您的智能体和工作流配置</p>
            </div>
          </div>
          
          <div class="feature-item">
            <a-icon type="dollar" class="feature-icon" />
            <div class="feature-text">
              <h4>收益管理</h4>
              <p>查看和提现您的创作收益</p>
            </div>
          </div>
        </div>

        <!-- 通知订阅 -->
        <div class="notification-signup">
          <h3>第一时间获取上线通知</h3>
          <div class="signup-form">
            <a-input
              v-model="email"
              placeholder="请输入您的邮箱地址"
              size="large"
              style="width: 300px;"
            />
            <a-button 
              type="primary" 
              size="large"
              @click="handleSubscribe"
              :loading="subscribing"
            >
              订阅通知
            </a-button>
          </div>
          <p class="signup-note">
            我们会在创作者中心上线时第一时间通知您
          </p>
        </div>

        <!-- 联系我们 */
        <div class="contact-info">
          <p>如有疑问或建议，欢迎联系我们：</p>
          <div class="contact-methods">
            <a-button type="link" @click="handleContact('email')">
              <a-icon type="mail" />
              邮箱咨询
            </a-button>
            <a-button type="link" @click="handleContact('wechat')">
              <a-icon type="wechat" />
              微信客服
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CreatorCenter',
  data() {
    return {
      email: '',
      subscribing: false
    }
  },
  methods: {
    // 处理订阅通知
    async handleSubscribe() {
      if (!this.email) {
        this.$message.warning('请输入邮箱地址')
        return
      }

      // 简单的邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(this.email)) {
        this.$message.warning('请输入有效的邮箱地址')
        return
      }

      this.subscribing = true
      try {
        // TODO: 调用后端API保存订阅信息
        // const response = await this.$http.post('/api/notification/subscribe', {
        //   email: this.email,
        //   type: 'creator_center_launch'
        // })
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        this.$message.success('订阅成功！我们会在创作者中心上线时通知您')
        this.email = ''
      } catch (error) {
        console.error('订阅失败:', error)
        this.$message.error('订阅失败，请稍后重试')
      } finally {
        this.subscribing = false
      }
    },

    // 处理联系我们
    handleContact(type) {
      if (type === 'email') {
        this.$message.info('客服邮箱：<EMAIL>')
      } else if (type === 'wechat') {
        this.$message.info('微信客服功能正在开发中')
      }
    }
  }
}
</script>

<style scoped>
.creator-center {
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.coming-soon-container {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

.coming-soon-content {
  background: white;
  padding: 3rem;
  border-radius: 24px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

/* 图标 */
.coming-soon-icon {
  font-size: 4rem;
  color: #3b82f6;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 标题 */
.coming-soon-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 描述 */
.coming-soon-description {
  font-size: 1.125rem;
  color: #64748b;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

/* 功能预告 */
.feature-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 1.5rem;
  color: #3b82f6;
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.feature-text {
  text-align: left;
}

.feature-text h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.feature-text p {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

/* 通知订阅 */
.notification-signup {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 2rem;
}

.notification-signup h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.signup-form {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.signup-note {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* 联系我们 */
.contact-info {
  color: #64748b;
}

.contact-info p {
  margin-bottom: 1rem;
}

.contact-methods {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .creator-center {
    padding: 1rem;
  }

  .coming-soon-content {
    padding: 2rem 1.5rem;
  }

  .coming-soon-title {
    font-size: 2rem;
  }

  .coming-soon-description {
    font-size: 1rem;
  }

  .feature-preview {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-item {
    padding: 1rem;
  }

  .notification-signup {
    padding: 1.5rem;
  }

  .signup-form {
    flex-direction: column;
    gap: 1rem;
  }

  .signup-form .ant-input {
    width: 100% !important;
  }

  .contact-methods {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
