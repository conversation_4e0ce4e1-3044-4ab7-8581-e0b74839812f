<template>
  <WebsitePage>
    <div class="plugin-detail-page">
    <!-- 页面加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <a-spin size="large" tip="加载插件详情中...">
          <div class="loading-skeleton">
            <!-- 骨架屏效果 -->
            <div class="skeleton-header">
              <div class="skeleton-breadcrumb"></div>
              <div class="skeleton-plugin-info">
                <div class="skeleton-image"></div>
                <div class="skeleton-details">
                  <div class="skeleton-title"></div>
                  <div class="skeleton-description"></div>
                  <div class="skeleton-meta">
                    <div class="skeleton-tag"></div>
                    <div class="skeleton-tag"></div>
                    <div class="skeleton-tag"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="skeleton-tabs">
              <div class="skeleton-tab-bar">
                <div class="skeleton-tab"></div>
                <div class="skeleton-tab"></div>
                <div class="skeleton-tab"></div>
                <div class="skeleton-tab"></div>
              </div>
              <div class="skeleton-tab-content"></div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 页面内容 -->
    <div v-else-if="pluginDetail" class="plugin-detail-content">
      <!-- 优化后的导航栏 -->
      <div class="navigation-bar">
        <!-- 左侧导航信息 -->
        <div class="nav-left">
          <!-- 返回按钮 -->
          <a-button
            type="text"
            size="large"
            @click="goBack"
            class="back-button">
            <a-icon type="arrow-left" />
          </a-button>

          <!-- 面包屑路径 -->
          <div class="breadcrumb-path">
            <span class="path-item" @click="$router.push('/')">
              <a-icon type="home" />
              首页
            </span>
            <a-icon type="right" class="path-separator" />
            <span class="path-item" @click="goBackToMarket">
              <a-icon type="shop" />
              商城
            </span>
            <a-icon type="right" class="path-separator" />
            <span class="path-current">
              {{ pluginDetail.plubname || '插件详情' }}
            </span>
          </div>
        </div>

        <!-- 右侧操作按钮 -->
        <div class="nav-right">
          <a-button @click="sharePlugin" class="share-button">
            <a-icon type="share-alt" />
            分享
          </a-button>
        </div>
      </div>

      <!-- 插件头部信息区域 -->
      <div class="plugin-header">
        <div class="plugin-header-content">
          <!-- 插件封面图 -->
          <div class="plugin-image">
            <img
              :src="getPluginImage(pluginDetail)"
              :alt="pluginDetail.plubname"
              @error="handleImageError"
            />
          </div>

          <!-- 插件基础信息 -->
          <div class="plugin-info">
            <h1 class="plugin-title">{{ pluginDetail.plubname }}</h1>
            <p class="plugin-description">{{ pluginDetail.plubinfo }}</p>
            
            <div class="plugin-meta">
              <!-- 第一行：分类和状态 -->
              <div class="meta-row">
                <div class="meta-item">
                  <a-tag :color="getCategoryColor(pluginDetail.plubCategory)" size="large">
                    <a-icon type="appstore" />
                    {{ categoryText }}
                  </a-tag>
                </div>
                <div class="meta-item">
                  <a-tag :color="getStatusColor(pluginDetail.status)" size="large">
                    <a-icon type="check-circle" />
                    {{ getStatusText(pluginDetail.status) }}
                  </a-tag>
                </div>
              </div>

              <!-- 第二行：创作者和价格 -->
              <div class="meta-row">
                <div class="meta-item">
                  <a-icon type="user" class="meta-icon" />
                  <span class="meta-label">创作者：</span>
                  <span class="meta-value">{{ authorInfo.authorname || '未知' }}</span>
                </div>
                <div class="meta-item price-item">
                  <a-icon type="dollar" class="meta-icon price-icon" />
                  <span class="meta-label">价格：</span>
                  <span class="meta-value price-value">{{ getDetailPriceText() }}</span>
                </div>
                <div v-if="hasTutorial" class="meta-item tutorial-item">
                  <span class="tutorial-hint" @click="goToTutorial">
                    本插件有教程视频，详细请点此观看
                  </span>
                </div>
              </div>

              <!-- 第三行：时间信息 -->
              <div class="meta-row">
                <div class="meta-item">
                  <a-icon type="calendar" class="meta-icon" />
                  <span class="meta-label">发布时间：</span>
                  <span class="meta-value">{{ formatDate(pluginDetail.createTime) }}</span>
                </div>
                <div class="meta-item">
                  <a-icon type="sync" class="meta-icon" />
                  <span class="meta-label">更新时间：</span>
                  <span class="meta-value">{{ formatDate(pluginDetail.updateTime) }}</span>
                </div>
              </div>

              <!-- 统计信息已移除 -->
            </div>
          </div>
        </div>
      </div>

      <!-- Tab切换内容区域 -->
      <div class="plugin-tabs-container">
        <!-- 自定义Tab导航 -->
        <div class="custom-tabs-nav">
          <div class="tabs-nav-wrapper">
            <div
              v-for="(tab, index) in tabList"
              :key="tab.key"
              :class="['tab-item', { 'active': activeTab === tab.key }]"
              @click="handleTabClick(tab.key)"
            >
              <i :class="tab.icon"></i>
              <span>{{ tab.label }}</span>
            </div>
          </div>
        </div>

        <!-- Tab内容区域 -->
        <div class="custom-tabs-content">
          <div v-show="activeTab === 'intro'" class="tab-pane">
            <plugin-introduction
              :content="pluginDetail.plubContent"
              :info="pluginDetail.plubinfo"
              :plugin-name="pluginDetail.plubname"
              :plugin-detail="pluginDetail"
            />
          </div>
          <div v-show="activeTab === 'tutorial'" class="tab-pane">
            <plugin-tutorial
              :tutorial-link="pluginDetail.tutorialLink"
              :video-file="pluginDetail.plubvideo"
              :plugin-name="pluginDetail.plubname"
              :detailed-content="pluginDetail.plubContent"
            />
          </div>
          <div v-show="activeTab === 'features'" class="tab-pane">
            <plugin-features
              :plugin-detail="pluginDetail"
              :category="pluginDetail.plubCategory"
            />
          </div>
          <div v-show="activeTab === 'tech'" class="tab-pane">
            <plugin-technical
              :plugin-detail="pluginDetail"
              :plugin-key="pluginDetail.pluginKey"
            />
          </div>
        </div>
      </div>

      <!-- 创作者信息区域 -->
      <div class="author-section">
        <author-info
          :author="authorInfo"
          :plugin-count="authorPluginCount"
        />
      </div>

      <!-- 相关推荐区域 -->
      <div class="recommendations-section">
        <related-plugins
          :recommendations="recommendations"
          :current-category="pluginDetail.plubCategory"
          :current-plugin-id="pluginDetail.id"
        />
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <a-result
        :status="errorStatus"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <div class="error-actions">
            <a-button type="primary" @click="retryLoad" v-if="canRetry">
              <a-icon type="reload" />
              重新加载
            </a-button>
            <a-button @click="$router.push('/market')">
              <a-icon type="shop" />
              返回商城
            </a-button>
            <a-button type="dashed" @click="goHome">
              <a-icon type="home" />
              返回首页
            </a-button>
          </div>
        </template>
      </a-result>
    </div>
    </div>
  </WebsitePage>
</template>

<script>
import { getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils'
import marketApi from '@/api/market'
import WebsitePage from '@/components/website/WebsitePage.vue'
import PluginIntroduction from './components/PluginIntroduction.vue'
import PluginTutorial from './components/PluginTutorial.vue'
import PluginFeatures from './components/PluginFeatures.vue'
import PluginTechnical from './components/PluginTechnical.vue'
import AuthorInfo from './components/AuthorInfo.vue'
import RelatedPlugins from './components/RelatedPlugins.vue'

export default {
  name: 'PluginDetail',

  components: {
    WebsitePage,
    PluginIntroduction,
    PluginTutorial,
    PluginFeatures,
    PluginTechnical,
    AuthorInfo,
    RelatedPlugins
  },

  data() {
    return {
      loading: true,
      pluginDetail: null,
      authorInfo: {},
      authorPluginCount: 0,
      recommendations: [],
      activeTab: 'tutorial',
      // Tab列表
      tabList: [
        { key: 'tutorial', label: '使用教程', icon: 'anticon anticon-play-circle' },
        { key: 'intro', label: '插件介绍', icon: 'anticon anticon-file-text' },
        { key: 'features', label: '功能特点', icon: 'anticon anticon-star' },
        { key: 'tech', label: '技术说明', icon: 'anticon anticon-code' }
      ],
      // 错误处理相关
      errorStatus: '404',
      errorTitle: '插件不存在',
      errorMessage: '抱歉，您访问的插件不存在或已被删除',
      canRetry: false,
      retryCount: 0,
      maxRetries: 3,
      // 调试模式
      debugMode: process.env.NODE_ENV === 'development',
      // 🔥 使用TOS统一管理的默认插件图片
      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    }
  },

  computed: {
    pluginId() {
      return this.$route.params.id;
    },

    // 判断是否有教程视频
    hasTutorial() {
      if (!this.pluginDetail) return false;

      // 检查是否有教程链接或视频文件
      const hasLink = this.pluginDetail.tutorialLink && this.pluginDetail.tutorialLink.trim() !== '';
      const hasVideo = this.pluginDetail.plubvideo && this.pluginDetail.plubvideo.trim() !== '';

      return hasLink || hasVideo;
    },

    categoryText() {
      // 🔥 使用全局分类字典服务获取分类文本
      if (!this.pluginDetail || !this.pluginDetail.plubCategory) {
        return '未知分类';
      }
      return this.$categoryService.getCategoryText(this.pluginDetail.plubCategory);
    }
  },

  async created() {
    // 添加页面访问统计
    this.trackPageView();

    // 🔥 初始化分类数据
    await this.$categoryService.getCategories();

    await this.loadPluginDetail();
  },

  // 监听路由变化
  watch: {
    '$route'(to, from) {
      // 如果是同一个组件但插件ID变了，重新加载数据
      if (to.params.id !== from.params.id) {
        this.loadPluginDetail();
      }
    }
  },

  methods: {
    async loadPluginDetail() {
      try {
        this.loading = true;
        this.canRetry = false;

        // 验证插件ID
        if (!this.pluginId) {
          this.handleError('参数错误', '插件ID不能为空', '400', false);
          return;
        }

        // 调用插件详情API
        const response = await marketApi.getPluginDetail(this.pluginId);

        if (response.success && response.result) {
          this.pluginDetail = response.result.plugin;
          this.authorInfo = response.result.author || {};
          this.authorPluginCount = this.authorInfo.plubnum || 0;
          // 重置错误状态
          this.retryCount = 0;

          if (this.debugMode) {
            console.log('插件详情加载成功:', this.pluginDetail);
            console.log('创作者信息:', this.authorInfo);
            console.log('推荐插件:', this.recommendations);
            console.log('推荐插件数量:', this.recommendations.length);
            console.log('创作者职位原始值:', this.authorInfo.title);
            console.log('创作者职位字典文本:', this.authorInfo.title_dictText);
            console.log('创作者专业领域原始值:', this.authorInfo.expertise);
            console.log('创作者专业领域字典文本:', this.authorInfo.expertise_dictText);
            console.log('插件图片字段 plubimg:', this.pluginDetail.plubimg);
            console.log('最终图片URL:', this.getPluginImage(this.pluginDetail));
            console.log('默认图片URL:', this.defaultPluginImage);
            console.log('环境变量 VUE_APP_IMG_BASE_URL:', process.env.VUE_APP_IMG_BASE_URL);
          }

          // 更新页面标题
          if (this.pluginDetail.plubname) {
            document.title = `${this.pluginDetail.plubname} - 插件详情 - 智界AIGC`;
          }

          // 获取相关推荐（根据分类）
          await this.fetchRecommendations();
        } else {
          const errorCode = response.code || 500;
          const errorMsg = response.message || '获取插件详情失败';

          if (errorCode === 404) {
            this.handleError('插件不存在', '抱歉，您访问的插件不存在或已被删除', '404', false);
          } else if (errorCode >= 500) {
            this.handleError('服务器错误', '服务器暂时无法响应，请稍后重试', '500', true);
          } else {
            this.handleError('加载失败', errorMsg, 'warning', true);
          }
        }

      } catch (error) {
        console.error('加载插件详情失败:', error);

        if (error.message && error.message.includes('Network Error')) {
          this.handleError('网络错误', '网络连接失败，请检查网络后重试', '500', true);
        } else if (error.message && error.message.includes('timeout')) {
          this.handleError('请求超时', '请求超时，请稍后重试', '500', true);
        } else {
          this.handleError('加载失败', '加载插件详情时发生未知错误', 'warning', true);
        }
      } finally {
        this.loading = false;
      }
    },

    // 处理错误状态
    handleError(title, message, status, canRetry) {
      this.pluginDetail = null;
      this.errorTitle = title;
      this.errorMessage = message;
      this.errorStatus = status;
      this.canRetry = canRetry && this.retryCount < this.maxRetries;

      // 显示错误提示
      if (canRetry && this.retryCount < this.maxRetries) {
        this.$message.error(`${message}，可以尝试重新加载`);
      } else {
        this.$message.error(message);
      }
    },

    // 重试加载
    async retryLoad() {
      if (this.retryCount >= this.maxRetries) {
        this.$message.warning('重试次数已达上限');
        return;
      }

      this.retryCount++;
      this.$message.info(`正在重试加载... (${this.retryCount}/${this.maxRetries})`);

      await this.loadPluginDetail();
    },

    // 返回首页
    goHome() {
      this.$router.push('/');
    },



    // 🔥 获取插件图片（支持组合插件优先级处理）
    getPluginImage(plugin) {
      return getPluginImageUrl(plugin, this.defaultPluginImage)
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.log('图片加载失败，使用默认图片:', event.target.src);
      console.log('原始图片路径 plubimg:', this.pluginDetail && this.pluginDetail.plubimg);
      console.log('切换到默认图片:', this.defaultPluginImage);
      event.target.src = this.defaultPluginImage;
    },

    formatDate(dateStr) {
      if (!dateStr) return '未知';
      return new Date(dateStr).toLocaleDateString('zh-CN');
    },

    formatNumber(num) {
      if (!num) return '0';
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toLocaleString();
    },

    getCategoryColor(category) {
      // 🔥 使用全局分类字典服务获取分类颜色
      return this.$categoryService.getCategoryColor(category);
    },

    getStatusColor(status) {
      const colors = {
        0: 'red',     // 下架
        1: 'green',   // 上架
        2: 'orange',  // 审核中
        3: 'red'      // 已拒绝
      };
      return colors[status] || 'default';
    },

    getStatusText(status) {
      const texts = {
        0: '已下架',
        1: '已上架',
        2: '审核中',
        3: '已拒绝'
      };
      return texts[status] || '未知状态';
    },

    // Tab切换事件
    onTabChange(activeKey) {
      console.log('切换到Tab:', activeKey);
      // 可以在这里添加Tab切换的统计或其他逻辑
    },

    // 自定义Tab切换方法
    handleTabClick(tabKey) {
      this.activeTab = tabKey;
      console.log('切换到Tab:', tabKey);
    },

    // 跳转到使用教程tab
    goToTutorial() {
      this.activeTab = 'tutorial';
      console.log('点击教程提示，跳转到使用教程tab');

      // 滚动到tab区域
      // this.$nextTick(() => {
      //   const tabsElement = document.querySelector('.plugin-tabs-container');
      //   if (tabsElement) {
      //     tabsElement.scrollIntoView({
      //       behavior: 'smooth',
      //       block: 'start'
      //     });
      //   }
      // });
    },

    // 🔥 获取详情页价格显示文本
    getDetailPriceText() {
      const price = this.pluginDetail.neednum
      const isSvipFree = this.pluginDetail.isSvipFree === 1 || this.pluginDetail.isSvipFree === '1'

      if (!price || price <= 0) {
        return '免费'
      }

      if (isSvipFree) {
        return `SVIP免费，低至¥${price}/次`
      } else {
        return `低至¥${price}/次`
      }
    },

    // 获取相关推荐（根据分类）
    async fetchRecommendations() {
      try {
        if (!this.pluginDetail || !this.pluginDetail.plubCategory) {
          console.log('没有插件分类信息，跳过推荐获取');
          this.recommendations = [];
          return;
        }

        // 🔥 扩大推荐范围：不限制分类，获取更多插件
        const params = {
          // category: this.pluginDetail.plubCategory, // 移除分类限制，获取所有分类
          pageSize: 50, // 增加获取数量，确保有足够的插件进行筛选
          status: 1 // 只获取已上架的插件
        };

        const response = await marketApi.getPluginList(params);

        if (this.debugMode) {
          console.log('相关推荐API响应:', response);
          console.log('推荐参数:', params);
        }

        if (response.success && response.result && response.result.records) {
          // 🔥 使用与Market页面相同的组合插件处理逻辑
          const allPlugins = response.result.records

          // 1. 先过滤掉当前插件
          const filteredPlugins = allPlugins.filter(plugin => plugin.id !== this.pluginDetail.id)

          // 2. 使用统一的组合插件处理逻辑（普通插件 + 组合插件代表）
          const processedPlugins = processPluginsWithCombined(filteredPlugins)

          // 3. 智能推荐：优先同分类，然后是其他分类
          const currentCategory = this.pluginDetail.plubCategory
          const sameCategoryPlugins = processedPlugins.filter(plugin => plugin.plubCategory === currentCategory)
          const otherCategoryPlugins = processedPlugins.filter(plugin => plugin.plubCategory !== currentCategory)

          // 4. 合并推荐：优先同分类，不足时补充其他分类
          let recommendations = [
            ...sameCategoryPlugins.sort(() => Math.random() - 0.5), // 同分类随机排序
            ...otherCategoryPlugins.sort(() => Math.random() - 0.5)  // 其他分类随机排序
          ].slice(0, 3); // 🎯 最多显示3个推荐

          this.recommendations = recommendations;

          if (this.debugMode) {
            console.log('推荐插件:', this.recommendations);
            console.log('推荐插件数量:', this.recommendations.length);
            console.log('当前插件分类:', this.pluginDetail.plubCategory);
          }
        } else {
          console.log('获取推荐失败或无数据');
          this.recommendations = [];
        }
      } catch (error) {
        console.error('获取相关推荐失败:', error);
        this.recommendations = [];
      }
    },

    // 返回上一页
    goBack() {
      // 检查是否有历史记录且上一页是商城页面
      if (window.history.length > 1 && document.referrer.includes('/market')) {
        this.$router.go(-1);
      } else {
        // 返回商城，保持筛选状态
        this.goBackToMarket();
      }
    },

    // 返回商城页面，保持筛选状态
    goBackToMarket() {
      // 从localStorage获取商城的筛选状态
      const marketState = this.getMarketState();

      if (marketState && Object.keys(marketState).length > 0) {
        // 如果有保存的筛选状态，构建查询参数
        const query = {};

        if (marketState.category) {
          query.category = marketState.category;
        }
        if (marketState.search) {
          query.search = marketState.search;
        }
        if (marketState.priceRange) {
          query.priceRange = marketState.priceRange;
        }
        if (marketState.sortBy) {
          query.sortBy = marketState.sortBy;
        }

        this.$router.push({ path: '/market', query });
      } else {
        // 没有筛选状态，直接返回商城
        this.$router.push('/market');
      }
    },

    // 获取商城筛选状态
    getMarketState() {
      try {
        const stateStr = localStorage.getItem('market_filter_state');
        return stateStr ? JSON.parse(stateStr) : {};
      } catch (error) {
        console.warn('获取商城筛选状态失败:', error);
        return {};
      }
    },

    // 分享插件 - 复制链接
    sharePlugin() {
      const url = window.location.href;

      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('插件详情链接已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyToClipboard(url);
        });
      } else {
        this.fallbackCopyToClipboard(url);
      }
    },

    // 兜底复制方法
    fallbackCopyToClipboard(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        this.$message.success('插件详情链接已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制链接');
      }
      document.body.removeChild(textArea);
    },

    // 页面访问统计
    trackPageView() {
      try {
        const trackData = {
          page: 'plugin-detail',
          pluginId: this.pluginId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          referrer: document.referrer
        };

        console.log('页面访问统计:', trackData);

        // 这里可以发送统计数据到后端
        // analytics.track('page_view', trackData);
      } catch (error) {
        console.warn('统计数据发送失败:', error);
      }
    },

    // 性能监控
    trackPerformance() {
      try {
        if (window.performance && window.performance.timing) {
          const timing = window.performance.timing;
          const loadTime = timing.loadEventEnd - timing.navigationStart;

          console.log('页面加载性能:', {
            loadTime: loadTime + 'ms',
            domReady: (timing.domContentLoadedEventEnd - timing.navigationStart) + 'ms',
            firstPaint: (timing.responseStart - timing.navigationStart) + 'ms'
          });
        }
      } catch (error) {
        console.warn('性能监控失败:', error);
      }
    },

    // 错误上报
    reportError(error, context = '') {
      try {
        const errorData = {
          message: error.message,
          stack: error.stack,
          context,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        };

        console.error('错误上报:', errorData);

        // 这里可以发送错误数据到后端
        // errorReporting.report(errorData);
      } catch (reportError) {
        console.warn('错误上报失败:', reportError);
      }
    }
  },

  // 组件销毁时的清理
  beforeDestroy() {
    // 清理定时器、事件监听器等
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
    }
  },

  // 错误边界
  errorCaptured(err, instance, info) {
    this.reportError(err, `组件错误: ${info}`);
    return false;
  }
}
</script>

<style scoped>
.plugin-detail-page {
  /* WebsitePage组件已经提供了基本布局，这里只需要设置背景色 */
  background-color: #f5f5f5;
}

.loading-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-skeleton {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.skeleton-header {
  margin-bottom: 24px;
}

.skeleton-breadcrumb {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 20px;
  width: 300px;
}

.skeleton-plugin-info {
  display: flex;
  gap: 32px;
}

.skeleton-image {
  width: 300px;
  height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
  flex-shrink: 0;
}

.skeleton-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-title {
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 60%;
}

.skeleton-description {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  width: 80%;
}

.skeleton-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.skeleton-tag {
  height: 24px;
  width: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 12px;
}

.skeleton-tabs {
  margin-top: 24px;
}

.skeleton-tab-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
}

.skeleton-tab {
  height: 40px;
  width: 120px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

.skeleton-tab-content {
  height: 300px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.plugin-detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.navigation-bar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  padding: 16px 24px;
  border-radius: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(59, 130, 246, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #475569;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.navigation-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0.6;
}

.back-button {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.1) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.back-button:hover {
  background: rgba(59, 130, 246, 0.15) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  color: #2563eb !important;
  transform: translateX(-3px) scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.back-button .anticon {
  font-size: 16px;
  font-weight: bold;
}

.breadcrumb-path {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  flex-wrap: wrap;
}

.path-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.path-item:hover {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.08);
  transform: translateY(-1px);
}

.path-item .anticon {
  font-size: 12px;
}

.path-separator {
  color: #cbd5e1;
  font-size: 12px;
  margin: 0 4px;
}

.path-current {
  color: #1e293b;
  font-weight: 600;
  font-size: 15px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: rgba(59, 130, 246, 0.1);
  padding: 6px 12px;
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.nav-right {
  flex-shrink: 0;
}

.share-button {
  color: #64748b !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(203, 213, 225, 0.6) !important;
  border-radius: 12px;
  transition: all 0.3s ease;
  padding: 8px 16px;
  height: 44px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.share-button:hover {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.08) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.share-button .anticon {
  font-size: 14px;
}

.plugin-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.plugin-header-content {
  display: flex;
  gap: 32px;
}

.plugin-image {
  flex-shrink: 0;
}

.plugin-image img {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.plugin-info {
  flex: 1;
}

.plugin-title {
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #1a1a1a;
}

.plugin-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
}

.plugin-meta {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.meta-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
  min-width: 200px;
}

.meta-icon {
  color: #1890ff;
  font-size: 16px;
}

.meta-label {
  color: #666;
  font-weight: 500;
}

.meta-value {
  color: #333;
  font-weight: 600;
}

.price-item {
  background: linear-gradient(165deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
}

.price-item .meta-icon,
.price-item .meta-label,
.price-item .meta-value {
  color: white;
}

.price-icon {
  color: #ffd700 !important;
}

.price-value {
  font-size: 16px;
  font-weight: bold;
}

.tutorial-hint {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  margin-left: 12px;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  padding: 8px 16px;
  border-radius: 16px;
  border: 1px solid #91d5ff;
  display: inline-flex;
  align-items: center;
  animation: pulse-hint 2s ease-in-out infinite;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  min-height: 32px;
}

.tutorial-hint:hover {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  transform: translateY(-3px) scale(1.08);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
  border-color: #1890ff;
  font-weight: 700;
}

.tutorial-hint:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
}

@keyframes pulse-hint {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);
  }
}

/* 自定义Tab导航样式 */
.plugin-tabs-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.plugin-tabs-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 300% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.custom-tabs-nav {
  margin-bottom: 32px;
}

.tabs-nav-wrapper {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 50px;
  padding: 8px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
  gap: 8px;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 18px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: none;
  background: transparent;
  color: #64748b;
  white-space: nowrap;
  user-select: none;
  z-index: 10;
  
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 50px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.tab-item:hover {
  color: #3b82f6;
  transform: translateY(-2px) scale(1.02);
}

.tab-item:hover::before {
  opacity: 1;
  transform: scale(1);
}

.tab-item.active {
  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
  position: relative;
  z-index: 2;
  top: 3px;
}

.tab-item.active::before {
  opacity: 0;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(165deg, #3b82f6, #8b5cf6);
  border-radius: 50px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(12px);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% { opacity: 0.3; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(1.05); }
}

.tab-item.active:hover {
  color: white;
  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);
  transform: translateY(-3px) scale(1.05);
}

.tab-item i {
  font-size: 18px;
  transition: all 0.3s ease;
}

.tab-item:hover i {
  transform: scale(1.1) rotate(5deg);
}

.tab-item.active i {
  transform: scale(1.15) rotate(0deg);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

.custom-tabs-content {
  position: relative;
}

.tab-pane {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.plugin-tabs {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.plugin-tabs::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 300% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.plugin-tabs .ant-tabs-bar {
  border-bottom: none !important;
  margin-bottom: 32px !important;
  background: transparent !important;
  padding: 0 !important;
  position: relative !important;
}

/* 使用最高优先级选择器覆盖Ant Design的tab导航容器 */
div.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,
div.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav-container,
.modern-tabs-container .ant-tabs-nav-container {
  position: relative !important;
  background: rgba(248, 250, 252, 0.8) !important;
  border-radius: 50px !important;
  padding: 8px !important;
  backdrop-filter: blur(10px) !important;
  border: 2px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1) !important;
}

/* 使用最高优先级选择器覆盖Ant Design的tab按钮 */
div.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,
div.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab,
.modern-tabs-container .ant-tabs-tab {
  padding: 18px 32px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  border-radius: 50px !important;
  margin: 0 4px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  border: none !important;
  background: transparent !important;
  color: #64748b !important;
  white-space: nowrap !important;
}

.plugin-tabs .ant-tabs-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(165deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 50px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.plugin-tabs .ant-tabs-tab:hover {
  color: #3b82f6;
  transform: translateY(-2px) scale(1.02);
}

.plugin-tabs .ant-tabs-tab:hover::before {
  opacity: 1;
  transform: scale(1);
}

/* 使用最高优先级选择器覆盖Ant Design的激活tab */
div.plugin-detail-content div.plugin-tabs .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,
div.plugin-tabs.modern-tabs-container .ant-tabs .ant-tabs-bar .ant-tabs-nav .ant-tabs-tab-active,
.modern-tabs-container .ant-tabs-tab-active {
  background: linear-gradient(165deg, #3b82f6 0%, #8b5cf6 100%) !important;
  color: white !important;
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;
  position: relative !important;
  z-index: 2 !important;
  border-radius: 50px !important;
}

.plugin-tabs .ant-tabs-tab-active::before {
  opacity: 0;
}

.plugin-tabs .ant-tabs-tab-active::after {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(165deg, #3b82f6, #8b5cf6);
  border-radius: 50px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(12px);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% { opacity: 0.3; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(1.05); }
}

.plugin-tabs .ant-tabs-tab-active:hover {
  color: white;
  background: linear-gradient(165deg, #2563eb 0%, #7c3aed 100%);
  transform: translateY(-3px) scale(1.05);
}

.plugin-tabs .ant-tabs-ink-bar {
  display: none;
}

.plugin-tabs .ant-tabs-tab .anticon {
  margin-right: 10px;
  font-size: 18px;
  transition: all 0.3s ease;
}

.plugin-tabs .ant-tabs-tab:hover .anticon {
  transform: scale(1.1) rotate(5deg);
}

.plugin-tabs .ant-tabs-tab-active .anticon {
  transform: scale(1.15) rotate(0deg);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

/* 添加流畅的切换动画 */
.plugin-tabs .ant-tabs-content {
  transition: all 0.3s ease;
}

.plugin-tabs .ant-tabs-tabpane {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-content {
  min-height: 400px;
  padding: 24px 0;
}

.rich-content {
  line-height: 1.8;
  color: #333;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.author-section,
.recommendations-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  background-color: #f5f5f5;
  padding: 40px 20px;
}

.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.error-actions .ant-btn {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .plugin-detail-content {
    max-width: 1000px;
  }

  .plugin-header-content {
    gap: 24px;
  }

  .plugin-image img {
    width: 280px;
    height: 180px;
  }
}

@media (max-width: 768px) {
  .plugin-detail-content {
    padding: 16px;
    max-width: 100%;
  }

  .navigation-bar {
    padding: 12px 16px;
    margin-bottom: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .nav-left {
    gap: 12px;
  }

  .breadcrumb-path {
    font-size: 12px;
    gap: 6px;
  }

  .path-current {
    max-width: 150px;
    font-size: 14px;
  }

  .nav-right {
    align-self: center;
  }

  .plugin-header {
    padding: 20px;
    margin-bottom: 16px;
  }

  .plugin-header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .plugin-image {
    align-self: center;
  }

  .plugin-image img {
    width: 100%;
    max-width: 280px;
    height: 180px;
  }

  .plugin-title {
    font-size: 24px;
    text-align: center;
  }

  .plugin-description {
    text-align: center;
    font-size: 15px;
  }

  .meta-row {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .meta-item {
    min-width: auto;
    width: auto;
    justify-content: center;
  }

  .price-item {
    justify-content: center;
    text-align: center;
    width: fit-content;
  }

  .plugin-tabs {
    padding: 16px;
    margin-bottom: 16px;
  }

  .plugin-tabs .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 14px;
    margin-right: 4px;
  }

  .tab-content {
    min-height: 300px;
    padding: 16px 0;
  }

  .author-section,
  .recommendations-section {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .plugin-detail-content {
    padding: 12px;
  }

  .navigation-bar {
    padding: 8px 12px;
    border-radius: 8px;
  }

  .back-button {
    width: 36px;
    height: 36px;
  }

  .breadcrumb-path {
    font-size: 11px;
    gap: 4px;
  }

  .path-current {
    max-width: 120px;
    font-size: 12px;
  }

  .nav-right .ant-btn-group .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }

  .plugin-header {
    padding: 16px;
  }

  .plugin-title {
    font-size: 20px;
  }

  .plugin-description {
    font-size: 14px;
  }

  .meta-row {
    gap: 8px;
  }

  .meta-item {
    font-size: 12px;
  }

  .plugin-tabs {
    padding: 12px;
  }

  .plugin-tabs .ant-tabs-tab {
    padding: 6px 8px;
    font-size: 12px;
    margin-right: 2px;
  }

  .tab-content {
    padding: 12px 0;
  }
}
</style>
