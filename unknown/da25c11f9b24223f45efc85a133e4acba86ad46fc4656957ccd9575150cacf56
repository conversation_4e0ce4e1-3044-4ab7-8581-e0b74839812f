<template>
  <div class="related-plugins">
    <div class="related-card">
      <h3 class="section-title">
        <a-icon type="appstore" />
        相关推荐
        <span class="category-hint" v-if="categoryText">- {{ categoryText }}</span>
      </h3>
      
      <div v-if="recommendations && recommendations.length > 0" class="plugins-grid">
        <div 
          v-for="plugin in recommendations" 
          :key="plugin.id"
          class="plugin-card"
          @click="goToPlugin(plugin.id)">
          
          <!-- 插件图片 -->
          <div class="plugin-image">
            <img
              :src="getPluginImage(plugin)"
              :alt="plugin.plubname"
              @error="handleImageError"
            />
            <div class="image-overlay">
              <a-icon type="eye" class="view-icon" />
            </div>
          </div>
          
          <!-- 插件信息 -->
          <div class="plugin-info">
            <h4 class="plugin-name">{{ plugin.plubname }}</h4>
            <p class="plugin-description">{{ plugin.plubinfo || '暂无描述' }}</p>
            
            <div class="plugin-meta">
              <div class="meta-item">
                <a-icon type="dollar" />
                <span>{{ getPluginPriceText(plugin) }}</span>
              </div>
            </div>
            
            <div class="plugin-tags">
              <a-tag :color="getCategoryColor(plugin.plubCategory)" size="small">
                {{ getCategoryText(plugin.plubCategory) }}
              </a-tag>
              <a-tag :color="getStatusColor(plugin.status)" size="small">
                {{ getStatusText(plugin.status) }}
              </a-tag>
            </div>
          </div>
          
          <!-- 悬浮操作 -->
          <div class="plugin-actions">
            <a-button type="primary" size="small" @click.stop="goToPlugin(plugin.id)">
              查看详情
            </a-button>
          </div>
        </div>
      </div>
      
      <!-- 无推荐内容 -->
      <div v-else class="no-recommendations">
        <a-empty description="暂无相关推荐">
          <a-button type="primary" @click="goToMarket">
            <a-icon type="shop" />
            浏览更多插件
          </a-button>
        </a-empty>
      </div>
      
      <!-- 查看更多 -->
      <div v-if="recommendations && recommendations.length > 0" class="more-actions">
        <a-button class="more-plugins-btn" @click="viewMoreByCategory">
          <a-icon type="appstore" />
          <span>查看更多插件</span>
          <a-icon type="right" />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getPluginImageUrl } from '../utils/marketUtils'

export default {
  name: 'RelatedPlugins',
  
  props: {
    recommendations: {
      type: Array,
      default: () => []
    },
    currentCategory: {
      type: String,
      default: ''
    },
    currentPluginId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      // 🔥 使用TOS统一管理的默认插件图片
      defaultPluginImage: '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'
    }
  },

  async mounted() {
    console.log('RelatedPlugins组件挂载，推荐数据:', this.recommendations);
    console.log('推荐数据长度:', this.recommendations ? this.recommendations.length : 0);

    // 🔥 初始化分类数据
    await this.$categoryService.getCategories();
  },

  watch: {
    recommendations: {
      handler(newVal) {
        console.log('推荐数据变化:', newVal);
        console.log('新推荐数据长度:', newVal ? newVal.length : 0);
      },
      immediate: true
    }
  },

  computed: {
    categoryText() {
      // 🔥 使用全局分类字典服务获取分类文本
      if (!this.currentCategory) {
        return '相关';
      }
      return this.$categoryService.getCategoryText(this.currentCategory);
    },

    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）
    defaultPluginImage() {
      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';
    }
  },

  methods: {
    // 🔥 获取插件图片（支持组合插件优先级处理，与market页面保持一致）
    getPluginImage(pluginOrPath) {
      // 如果传入的是字符串路径，保持向后兼容
      if (typeof pluginOrPath === 'string') {
        return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);
      }

      // 如果传入的是插件对象，使用新的优先级逻辑
      return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);
    },

    handleImageError(event) {
      event.target.src = this.defaultPluginImage;
    },

    formatNumber(num) {
      if (!num) return '0';
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万';
      }
      return num.toLocaleString();
    },

    getCategoryColor(category) {
      // 🔥 使用全局分类字典服务获取分类颜色
      return this.$categoryService.getCategoryColor(category);
    },

    getCategoryText(category) {
      // 🔥 使用全局分类字典服务获取分类文本
      return this.$categoryService.getCategoryText(category);
    },

    getStatusColor(status) {
      const colors = {
        0: 'red',     // 下架
        1: 'green',   // 上架
        2: 'orange',  // 审核中
        3: 'red'      // 已拒绝
      };
      return colors[status] || 'default';
    },

    getStatusText(status) {
      const texts = {
        0: '已下架',
        1: '已上架',
        2: '审核中',
        3: '已拒绝'
      };
      return texts[status] || '未知状态';
    },

    goToPlugin(pluginId) {
      if (pluginId === this.currentPluginId) {
        this.$message.info('这就是当前插件');
        return;
      }
      
      // 跳转到插件详情页
      this.$router.push(`/market/plugin/${pluginId}`);
    },

    goToMarket() {
      this.$router.push('/market');
    },

    viewMoreByCategory() {
      this.$router.push(`/market?category=${this.currentCategory}`);
    },

    // 🔥 获取插件价格显示文本
    getPluginPriceText(plugin) {
      const price = plugin.neednum
      const isSvipFree = plugin.isSvipFree === 1 || plugin.isSvipFree === '1'

      if (!price || price <= 0) {
        return '免费'
      }

      if (isSvipFree) {
        return `SVIP免费，低至¥${price}/次`
      } else {
        return `低至¥${price}/次`
      }
    }
  }
}
</script>

<style scoped>
.related-plugins {
  margin-bottom: 24px;
}

.related-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.section-title .anticon {
  color: #1890ff;
  font-size: 20px;
}

.category-hint {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.plugins-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.plugin-card {
  position: relative;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.plugin-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.plugin-card:hover .image-overlay {
  opacity: 1;
}

.plugin-card:hover .plugin-actions {
  opacity: 1;
  transform: translateY(0);
}

.plugin-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.plugin-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.plugin-card:hover .plugin-image img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.view-icon {
  color: white;
  font-size: 24px;
}

.plugin-info {
  padding: 16px;
}

.plugin-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.plugin-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.plugin-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.meta-item .anticon {
  color: #1890ff;
}

.plugin-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.plugin-actions {
  position: absolute;
  bottom: 16px;
  right: 16px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.no-recommendations {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.more-actions {
  text-align: center;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.more-plugins-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  height: 50px;
  padding: 0 2rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.more-plugins-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.more-plugins-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  color: white;
  border: none;
}

.more-plugins-btn:hover::before {
  left: 100%;
}

.more-plugins-btn:active {
  transform: translateY(0);
}

.more-plugins-btn .anticon {
  font-size: 1.1rem;
}

.more-plugins-btn .anticon:first-child {
  margin-right: 0.25rem;
}

.more-plugins-btn .anticon:last-child {
  margin-left: 0.25rem;
  transition: transform 0.3s ease;
}

.more-plugins-btn:hover .anticon:last-child {
  transform: translateX(3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plugins-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .plugin-card {
    margin-bottom: 0;
  }
  
  .plugin-actions {
    position: static;
    opacity: 1;
    transform: none;
    margin-top: 12px;
  }
  
  .image-overlay {
    display: none;
  }
}
</style>
