{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue?vue&type=template&id=dbf24c8c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue", "mtime": 1754042137915}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"agent-market\" },\n    [\n      _c(\"div\", { staticClass: \"sticky-filters\" }, [\n        _c(\"div\", { staticClass: \"market-filters\" }, [\n          _c(\"div\", { staticClass: \"filter-row\" }, [\n            _c(\"div\", { staticClass: \"search-box\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"search-wrapper\" },\n                [\n                  _c(\"a-icon\", {\n                    staticClass: \"search-icon\",\n                    attrs: { type: \"search\" }\n                  }),\n                  _c(\"a-input\", {\n                    staticClass: \"search-input\",\n                    attrs: {\n                      placeholder: \"搜索智能体名称、描述或标签...\",\n                      size: \"large\"\n                    },\n                    on: {\n                      pressEnter: _vm.handleSearch,\n                      input: _vm.handleSearch\n                    },\n                    model: {\n                      value: _vm.searchQuery,\n                      callback: function($$v) {\n                        _vm.searchQuery = $$v\n                      },\n                      expression: \"searchQuery\"\n                    }\n                  }),\n                  _vm.searchQuery\n                    ? _c(\"a-icon\", {\n                        staticClass: \"clear-icon\",\n                        attrs: { type: \"close-circle\" },\n                        on: { click: _vm.clearSearch }\n                      })\n                    : _vm._e()\n                ],\n                1\n              )\n            ]),\n            _c(\"div\", { staticClass: \"filter-controls\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"filter-item-inline\" },\n                [\n                  _c(\"span\", { staticClass: \"filter-label\" }, [\n                    _vm._v(\"作者类型：\")\n                  ]),\n                  _c(\n                    \"a-select\",\n                    {\n                      staticClass: \"filter-select\",\n                      attrs: { placeholder: \"全部类型\", size: \"large\" },\n                      on: { change: _vm.handleFilterChange },\n                      model: {\n                        value: _vm.authorTypeFilter,\n                        callback: function($$v) {\n                          _vm.authorTypeFilter = $$v\n                        },\n                        expression: \"authorTypeFilter\"\n                      }\n                    },\n                    [\n                      _c(\"a-select-option\", { attrs: { value: \"\" } }, [\n                        _vm._v(\"全部类型\")\n                      ]),\n                      _c(\n                        \"a-select-option\",\n                        { attrs: { value: \"1\" } },\n                        [\n                          _c(\"a-icon\", {\n                            staticStyle: {\n                              color: \"#f59e0b\",\n                              \"margin-right\": \"4px\"\n                            },\n                            attrs: { type: \"crown\" }\n                          }),\n                          _vm._v(\"\\n                官方\\n              \")\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-select-option\",\n                        { attrs: { value: \"2\" } },\n                        [\n                          _c(\"a-icon\", {\n                            staticStyle: {\n                              color: \"#3b82f6\",\n                              \"margin-right\": \"4px\"\n                            },\n                            attrs: { type: \"user\" }\n                          }),\n                          _vm._v(\"\\n                创作者\\n              \")\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ])\n          ])\n        ])\n      ]),\n      !_vm.loading\n        ? _c(\"div\", { staticClass: \"agent-list\" }, [\n            _c(\"div\", { staticClass: \"list-header\" }, [\n              _c(\"h3\", { staticClass: \"list-title\" }, [\n                _vm._v(\"\\n        智能体列表\\n        \"),\n                _c(\"span\", { staticClass: \"list-count\" }, [\n                  _vm._v(\"(\" + _vm._s(_vm.totalCount) + \"个)\")\n                ])\n              ])\n            ]),\n            _vm.agentList.length > 0\n              ? _c(\n                  \"div\",\n                  { staticClass: \"agent-grid\" },\n                  _vm._l(_vm.agentList, function(agent) {\n                    return _c(\"AgentCard\", {\n                      key: agent.id,\n                      attrs: { agent: agent },\n                      on: { \"view-detail\": _vm.handleViewDetail }\n                    })\n                  }),\n                  1\n                )\n              : _c(\n                  \"div\",\n                  { staticClass: \"empty-state\" },\n                  [\n                    _c(\n                      \"a-empty\",\n                      { attrs: { description: \"暂无智能体数据\" } },\n                      [\n                        _c(\n                          \"a-button\",\n                          {\n                            attrs: { type: \"primary\" },\n                            on: { click: _vm.handleRefresh }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"reload\" } }),\n                            _vm._v(\"\\n          刷新数据\\n        \")\n                          ],\n                          1\n                        )\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n            _vm.agentList.length > 0\n              ? _c(\"div\", { staticClass: \"load-more-wrapper\" }, [\n                  _vm.loadingMore\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"loading-more\" },\n                        [\n                          _c(\"a-spin\", { attrs: { size: \"small\" } }),\n                          _c(\"span\", [_vm._v(\"正在加载更多...\")])\n                        ],\n                        1\n                      )\n                    : _vm.hasMore\n                    ? _c(\"div\", {\n                        ref: \"loadMoreTrigger\",\n                        staticClass: \"load-more-trigger\"\n                      })\n                    : _c(\n                        \"div\",\n                        { staticClass: \"no-more-data\" },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"check-circle\" } }),\n                          _c(\"span\", [\n                            _vm._v(\n                              \"已加载全部数据 (共\" +\n                                _vm._s(_vm.totalCount) +\n                                \"个)\"\n                            )\n                          ])\n                        ],\n                        1\n                      )\n                ])\n              : _vm._e()\n          ])\n        : _c(\n            \"div\",\n            { staticClass: \"loading-state\" },\n            [\n              _c(\n                \"a-spin\",\n                { attrs: { size: \"large\", tip: \"正在加载智能体数据...\" } },\n                [_c(\"div\", { staticClass: \"loading-placeholder\" })]\n              )\n            ],\n            1\n          ),\n      _c(\"AgentDetailModal\", {\n        attrs: {\n          visible: _vm.detailModalVisible,\n          agentId: _vm.selectedAgentId,\n          isPurchased: _vm.isSelectedAgentPurchased\n        },\n        on: {\n          close: _vm.handleCloseDetailModal,\n          purchase: _vm.handlePurchaseFromModal,\n          \"purchase-success\": _vm.handlePurchaseSuccess\n        }\n      })\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}