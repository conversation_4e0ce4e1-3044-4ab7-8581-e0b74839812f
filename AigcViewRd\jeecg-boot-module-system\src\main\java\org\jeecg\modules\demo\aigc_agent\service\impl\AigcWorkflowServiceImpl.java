package org.jeecg.modules.demo.aigc_agent.service.impl;

import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.mapper.AigcWorkflowMapper;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工作流表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Service
public class AigcWorkflowServiceImpl extends ServiceImpl<AigcWorkflowMapper, AigcWorkflow> implements IAigcWorkflowService {
	
	@Autowired
	private AigcWorkflowMapper aigcWorkflowMapper;
	
	@Override
	public List<AigcWorkflow> selectByMainId(String mainId) {
		return aigcWorkflowMapper.selectByMainId(mainId);
	}
}
