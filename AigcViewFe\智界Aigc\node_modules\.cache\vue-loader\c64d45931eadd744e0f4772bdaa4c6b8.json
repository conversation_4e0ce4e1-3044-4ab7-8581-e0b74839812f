{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue?vue&type=template&id=17ce7a1e&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue", "mtime": 1753944358622}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-modal\n  :visible=\"visible\"\n  :title=\"modalTitle\"\n  width=\"1200px\"\n  :footer=\"null\"\n  @cancel=\"handleCancel\"\n  :z-index=\"2000\"\n  :mask-closable=\"true\"\n  :destroy-on-close=\"false\"\n  class=\"combined-plugin-modal\"\n  :get-container=\"() => document.body\">\n  \n  <!-- 🔥 组合插件头部信息 -->\n  <div class=\"combined-plugin-header\">\n    <div class=\"combined-info\">\n      <div class=\"combined-image\">\n        <img\n          :src=\"getPluginImage(combinedPlugin)\"\n          :alt=\"combinedPlugin && combinedPlugin.combinedName\"\n          @error=\"handleImageError\" />\n      </div>\n      <div class=\"combined-details\">\n        <h2 class=\"combined-title\">{{ combinedPlugin && combinedPlugin.combinedName }}</h2>\n        <p class=\"combined-description\">{{ combinedPlugin && combinedPlugin.combinedDescription }}</p>\n        <div class=\"combined-meta\">\n          <span class=\"meta-item\">\n            <a-icon type=\"user\" />\n            创作者：{{ (combinedPlugin && combinedPlugin.plubwrite_dictText) || '未知' }}\n          </span>\n          <span class=\"meta-item\">\n            <a-icon type=\"tag\" />\n            分类：{{ getCategoryText(combinedPlugin && combinedPlugin.plubCategory) }}\n          </span>\n          <span class=\"meta-item\">\n            <a-icon type=\"link\" />\n            组合插件\n          </span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <a-divider>\n    <span class=\"divider-text\">\n      <a-icon type=\"appstore\" />\n      包含的插件 ({{ subPlugins.length }})\n    </span>\n  </a-divider>\n\n  <!-- 🔥 子插件网格展示 -->\n  <div class=\"sub-plugins-container\" v-loading=\"loading\">\n    <div v-if=\"loading\" class=\"loading-container\">\n      <a-spin size=\"large\">\n        <span slot=\"tip\">正在加载插件列表...</span>\n      </a-spin>\n    </div>\n    \n    <div v-else-if=\"subPlugins.length === 0\" class=\"empty-container\">\n      <a-empty description=\"暂无子插件\" />\n    </div>\n    \n    <div v-else class=\"sub-plugins-grid\">\n      <div \n        v-for=\"subPlugin in subPlugins\" \n        :key=\"subPlugin.id\"\n        class=\"sub-plugin-card\"\n        @click=\"selectSubPlugin(subPlugin)\">\n        \n        <div class=\"sub-plugin-image\">\n          <img\n            :src=\"getPluginImage(subPlugin)\"\n            :alt=\"subPlugin.plubname\"\n            @error=\"handleSubImageError\" />\n          <div class=\"plugin-overlay\">\n            <a-icon type=\"eye\" class=\"view-icon\" />\n          </div>\n        </div>\n        \n        <div class=\"sub-plugin-info\">\n          <h4 class=\"sub-plugin-title\" :title=\"subPlugin.plubname\">\n            {{ subPlugin.plubname }}\n          </h4>\n          <p class=\"sub-plugin-description\" :title=\"subPlugin.plubinfo\">\n            {{ truncateText(subPlugin.plubinfo, 60) }}\n          </p>\n          <div class=\"sub-plugin-meta\">\n            <span class=\"category-tag\">\n              {{ getCategoryText(subPlugin.plubCategory) }}\n            </span>\n            <span class=\"price-tag\">\n              {{ getSubPluginPriceText(subPlugin) }}\n            </span>\n          </div>\n        </div>\n        \n        <div class=\"sub-plugin-actions\">\n          <a-button type=\"primary\" size=\"small\" @click.stop=\"selectSubPlugin(subPlugin)\">\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n        </div>\n      </div>\n    </div>\n  </div>\n</a-modal>\n", null]}