{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue?vue&type=style&index=0&id=496f8674&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue", "mtime": 1753987025534}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.workflow-center {\r\n  min-height: 100vh;\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* 简洁页面标题 */\r\n.simple-header {\r\n  text-align: center;\r\n  padding: 2rem 0 3rem; /* 恢复原来的padding */\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.simple-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.simple-subtitle {\r\n  font-size: 1.1rem;\r\n  color: #64748b;\r\n  margin: 0;\r\n}\r\n\r\n.workflow-tabs {\r\n  background: white;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  position: sticky;\r\n  top: 104px; /* 顶部导航栏实际高度是100px */\r\n  z-index: 200; /* 提高层级，确保在搜索筛选区域之上 */\r\n}\r\n\r\n.tabs-container {\r\n  max-width: 1600px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.tab-nav {\r\n  display: flex;\r\n  gap: 0;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.tab-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 2rem;\r\n  cursor: pointer;\r\n  border-bottom: 3px solid transparent;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  color: #64748b;\r\n  background: transparent;\r\n  position: relative;\r\n  border-radius: 8px 8px 0 0;\r\n}\r\n\r\n.tab-item:hover {\r\n  color: #3b82f6;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\r\n}\r\n\r\n.tab-item.active {\r\n  color: #ffffff;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  border-bottom-color: transparent;\r\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -3px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.tab-item .anticon {\r\n  font-size: 1.2rem;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.tab-item.active .anticon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.workflow-content {\r\n  flex: 1;\r\n}\r\n\r\n.content-container {\r\n  max-width: 1600px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n.tab-content {\r\n  min-height: 600px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .simple-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .simple-subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .tabs-container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .tab-item {\r\n    padding: 0.75rem 1rem;\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .content-container {\r\n    padding: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .simple-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .simple-subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .tab-nav {\r\n    justify-content: center;\r\n  }\r\n\r\n  .tab-item {\r\n    flex: 1;\r\n    justify-content: center;\r\n  }\r\n}\r\n", {"version": 3, "sources": ["WorkflowCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "WorkflowCenter.vue", "sourceRoot": "src/views/website/workflow", "sourcesContent": ["<template>\r\n  <WebsitePage>\r\n    <div class=\"workflow-center\">\r\n      <!-- 简洁页面标题 -->\r\n      <div class=\"simple-header\">\r\n        <h1 class=\"simple-title\">AI工作流中心</h1>\r\n        <p class=\"simple-subtitle\">发现和使用优质AI智能体，提升您的创作效率，让每个想法都能完美实现</p>\r\n      </div>\r\n\r\n    <!-- Tab导航 -->\r\n    <div class=\"workflow-tabs\">\r\n      <div class=\"tabs-container\">\r\n        <div class=\"tab-nav\">\r\n          <div\r\n            class=\"tab-item\"\r\n            :class=\"{ active: activeTab === 'market' }\"\r\n            @click=\"switchTab('market')\"\r\n          >\r\n            <a-icon type=\"shop\" />\r\n            <span>智能体市场</span>\r\n          </div>\r\n          <div\r\n            class=\"tab-item\"\r\n            :class=\"{ active: activeTab === 'creator' }\"\r\n            @click=\"switchTab('creator')\"\r\n          >\r\n            <a-icon type=\"build\" />\r\n            <span>创作者中心</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Tab内容 -->\r\n    <div class=\"workflow-content\">\r\n      <div class=\"content-container\">\r\n        <!-- 智能体市场 -->\r\n        <div v-show=\"activeTab === 'market'\" class=\"tab-content\">\r\n          <AgentMarket />\r\n        </div>\r\n\r\n        <!-- 创作者中心 -->\r\n        <div v-show=\"activeTab === 'creator'\" class=\"tab-content\">\r\n          <CreatorCenter />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </div>\r\n  </WebsitePage>\r\n</template>\r\n\r\n<script>\r\nimport WebsitePage from '@/components/website/WebsitePage.vue'\r\nimport AgentMarket from './components/AgentMarket.vue'\r\nimport CreatorCenter from './components/CreatorCenter.vue'\r\n\r\nexport default {\r\n  name: 'WorkflowCenter',\r\n  components: {\r\n    WebsitePage,\r\n    AgentMarket,\r\n    CreatorCenter\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'market'\r\n    }\r\n  },\r\n  mounted() {\r\n    document.title = 'AI工作流中心 - 智界AIGC'\r\n  },\r\n  methods: {\r\n    switchTab(tab) {\r\n      if (tab === 'creator') {\r\n        this.$message.info('创作者中心功能正在开发中，敬请期待！')\r\n        return\r\n      }\r\n      this.activeTab = tab\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.workflow-center {\r\n  min-height: 100vh;\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* 简洁页面标题 */\r\n.simple-header {\r\n  text-align: center;\r\n  padding: 2rem 0 3rem; /* 恢复原来的padding */\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.simple-title {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.simple-subtitle {\r\n  font-size: 1.1rem;\r\n  color: #64748b;\r\n  margin: 0;\r\n}\r\n\r\n.workflow-tabs {\r\n  background: white;\r\n  border-bottom: 1px solid #e2e8f0;\r\n  position: sticky;\r\n  top: 104px; /* 顶部导航栏实际高度是100px */\r\n  z-index: 200; /* 提高层级，确保在搜索筛选区域之上 */\r\n}\r\n\r\n.tabs-container {\r\n  max-width: 1600px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.tab-nav {\r\n  display: flex;\r\n  gap: 0;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.tab-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 2rem;\r\n  cursor: pointer;\r\n  border-bottom: 3px solid transparent;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  color: #64748b;\r\n  background: transparent;\r\n  position: relative;\r\n  border-radius: 8px 8px 0 0;\r\n}\r\n\r\n.tab-item:hover {\r\n  color: #3b82f6;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\r\n}\r\n\r\n.tab-item.active {\r\n  color: #ffffff;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  border-bottom-color: transparent;\r\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.tab-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -3px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.tab-item .anticon {\r\n  font-size: 1.2rem;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.tab-item.active .anticon {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.workflow-content {\r\n  flex: 1;\r\n}\r\n\r\n.content-container {\r\n  max-width: 1600px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n.tab-content {\r\n  min-height: 600px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .simple-title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .simple-subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .tabs-container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .tab-item {\r\n    padding: 0.75rem 1rem;\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  .content-container {\r\n    padding: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .simple-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .simple-subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .tab-nav {\r\n    justify-content: center;\r\n  }\r\n\r\n  .tab-item {\r\n    flex: 1;\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>"]}]}