{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\components\\website\\WebsiteHeader.vue", "mtime": 1753971674317}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { gsap } from 'gsap';\nimport { ACCESS_TOKEN } from '@/store/mutation-types';\nimport { isAdmin, getUserRole } from '@/utils/roleUtils';\nimport Vue from 'vue';\nimport LogoImage from '@/components/common/LogoImage.vue';\nexport default {\n  name: 'WebsiteHeader',\n  components: {\n    LogoImage: LogoImage\n  },\n  props: {\n    // 是否使用透明背景（首页专用）\n    transparent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [],\n      userInfo: {},\n      isLoggedIn: false,\n      isAdmin: false\n    };\n  },\n  computed: {\n    isTransparent: function isTransparent() {\n      return this.transparent && !this.isScrolled;\n    }\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.loadMenuData();\n\n            case 2:\n              _context.next = 4;\n              return this.checkUserStatus();\n\n            case 4:\n              this.initScrollListener();\n              this.initNavbarAnimations();\n\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n\n    return mounted;\n  }(),\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    loadMenuData: function () {\n      var _loadMenuData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                try {\n                  // TODO: 从API获取菜单数据\n                  // const response = await this.$http.get('/api/website/header/menu')\n                  // this.menuItems = response.data\n                  // 临时数据，后续替换为API调用\n                  this.menuItems = [{\n                    name: '首页',\n                    path: '/home',\n                    icon: 'home'\n                  }, {\n                    name: '插件中心',\n                    path: '/market',\n                    icon: 'shop'\n                  }, {\n                    name: '工作流中心',\n                    path: '/workflow-center',\n                    icon: 'deployment-unit'\n                  }, // { name: '客户案例', path: '/cases', icon: 'trophy' },\n                  // { name: '教程中心', path: '/tutorials', icon: 'book' },\n                  // { name: '签到奖励', path: '/signin', icon: 'gift' },\n                  {\n                    name: '客户案例',\n                    path: '',\n                    icon: 'trophy'\n                  }, {\n                    name: '教程中心',\n                    path: '',\n                    icon: 'book'\n                  }, {\n                    name: '签到奖励',\n                    path: '',\n                    icon: 'gift'\n                  }, {\n                    name: '订阅会员',\n                    path: '/membership',\n                    icon: 'crown'\n                  }, {\n                    name: '邀请奖励',\n                    path: '/affiliate',\n                    icon: 'team'\n                  }, {\n                    name: '个人中心',\n                    path: '/usercenter',\n                    icon: 'user'\n                  }];\n                } catch (error) {\n                  console.error('加载菜单数据失败:', error); // 降级方案\n\n                  this.menuItems = [{\n                    name: '首页',\n                    path: '/',\n                    icon: 'home'\n                  }, {\n                    name: '插件中心',\n                    path: '/market',\n                    icon: 'shop'\n                  }, {\n                    name: '个人中心',\n                    path: '/usercenter',\n                    icon: 'user'\n                  }];\n                }\n\n              case 1:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function loadMenuData() {\n        return _loadMenuData.apply(this, arguments);\n      }\n\n      return loadMenuData;\n    }(),\n    initScrollListener: function initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll);\n    },\n    handleScroll: function handleScroll() {\n      this.isScrolled = window.scrollY > 50;\n    },\n    toggleMobileMenu: function toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen;\n    },\n    closeMobileMenu: function closeMobileMenu() {\n      this.mobileMenuOpen = false;\n    },\n    // 🔥 处理桌面端开发中功能点击\n    handleDevelopingClick: function handleDevelopingClick(featureName) {\n      console.log('🎯 桌面端开发中功能点击:', featureName);\n      this.$message.info(\"\".concat(featureName, \"\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85\\uFF01\"), 3);\n    },\n    // 🔥 处理移动端菜单点击\n    handleMobileMenuClick: function handleMobileMenuClick(item) {\n      console.log('🎯 移动端菜单点击:', item.name, 'path:', item.path);\n\n      if (!item.path || item.path === '') {\n        console.log('🎯 移动端开发中功能点击:', item.name);\n        this.$message.info(\"\".concat(item.name, \"\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D\\uFF0C\\u656C\\u8BF7\\u671F\\u5F85\\uFF01\"), 3);\n      } else {\n        // 有效路径，进行跳转\n        this.$router.push(item.path);\n      }\n\n      this.closeMobileMenu();\n    },\n    goHome: function goHome() {\n      this.$router.push('/');\n    },\n    handleLogin: function handleLogin() {\n      this.$router.push('/login');\n    },\n    checkUserStatus: function () {\n      var _checkUserStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var token, userRole;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                // 检查是否有TOKEN\n                token = Vue.ls.get(ACCESS_TOKEN);\n\n                if (token) {\n                  _context3.next = 6;\n                  break;\n                }\n\n                this.isLoggedIn = false;\n                this.isAdmin = false;\n                return _context3.abrupt(\"return\");\n\n              case 6:\n                this.isLoggedIn = true; // 检查用户角色\n\n                _context3.next = 9;\n                return getUserRole();\n\n              case 9:\n                userRole = _context3.sent;\n                _context3.next = 12;\n                return isAdmin();\n\n              case 12:\n                this.isAdmin = _context3.sent;\n                // 获取用户信息\n                this.userInfo = {\n                  username: this.$store.getters.username || '用户',\n                  role: userRole\n                };\n                console.log('🔍 WebsiteHeader用户状态:', {\n                  isLoggedIn: this.isLoggedIn,\n                  isAdmin: this.isAdmin,\n                  userInfo: this.userInfo\n                });\n                _context3.next = 22;\n                break;\n\n              case 17:\n                _context3.prev = 17;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('检查用户状态失败:', _context3.t0);\n                this.isLoggedIn = false;\n                this.isAdmin = false;\n\n              case 22:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 17]]);\n      }));\n\n      function checkUserStatus() {\n        return _checkUserStatus.apply(this, arguments);\n      }\n\n      return checkUserStatus;\n    }(),\n    goToAdmin: function goToAdmin() {\n      // 跳转到后台管理首页\n      this.$router.push('/dashboard/analysis');\n      this.closeMobileMenu();\n    },\n    initNavbarAnimations: function initNavbarAnimations() {\n      var _this = this;\n\n      // 导航栏入场动画 - 添加refs存在检查\n      this.$nextTick(function () {\n        var elements = [_this.$refs.navBrand, _this.$refs.navMenu, _this.$refs.navActions].filter(function (el) {\n          return el;\n        });\n\n        if (elements.length > 0) {\n          gsap.from(elements, {\n            duration: 0.4,\n            y: -20,\n            opacity: 0,\n            ease: \"power2.out\",\n            stagger: 0.05\n          });\n        }\n      });\n    }\n  }\n};", {"version": 3, "sources": ["WebsiteHeader.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA,SAAA,IAAA,QAAA,MAAA;AACA,SAAA,YAAA,QAAA,wBAAA;AACA,SAAA,OAAA,EAAA,WAAA,QAAA,mBAAA;AACA,OAAA,GAAA,MAAA,KAAA;AACA,OAAA,SAAA,MAAA,mCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA;AADA,GAFA;AAKA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA;AAFA,GALA;AAYA,EAAA,IAZA,kBAYA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,KADA;AAEA,MAAA,cAAA,EAAA,KAFA;AAGA,MAAA,SAAA,EAAA,EAHA;AAIA,MAAA,QAAA,EAAA,EAJA;AAKA,MAAA,UAAA,EAAA,KALA;AAMA,MAAA,OAAA,EAAA;AANA,KAAA;AAQA,GArBA;AAsBA,EAAA,QAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA,aAAA,KAAA,WAAA,IAAA,CAAA,KAAA,UAAA;AACA;AAHA,GAtBA;AA2BA,EAAA,OA3BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBA4BA,KAAA,YAAA,EA5BA;;AAAA;AAAA;AAAA,qBA6BA,KAAA,eAAA,EA7BA;;AAAA;AA8BA,mBAAA,kBAAA;AACA,mBAAA,oBAAA;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiCA,EAAA,aAjCA,2BAiCA;AACA,IAAA,MAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,GAnCA;AAoCA,EAAA,OAAA,EAAA;AACA,IAAA,YADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAAA;AACA;AACA;AACA;AAEA;AACA,uBAAA,SAAA,GAAA,CACA;AAAA,oBAAA,IAAA,EAAA,IAAA;AAAA,oBAAA,IAAA,EAAA,OAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBADA,EAEA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,SAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAFA,EAGA;AAAA,oBAAA,IAAA,EAAA,OAAA;AAAA,oBAAA,IAAA,EAAA,kBAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAHA,EAIA;AACA;AACA;AACA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,EAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAPA,EAQA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,EAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBARA,EASA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,EAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBATA,EAUA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,aAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAVA,EAWA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,YAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAXA,EAYA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,aAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAZA,CAAA;AAcA,iBApBA,CAoBA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA,EADA,CAEA;;AACA,uBAAA,SAAA,GAAA,CACA;AAAA,oBAAA,IAAA,EAAA,IAAA;AAAA,oBAAA,IAAA,EAAA,GAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBADA,EAEA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,SAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAFA,EAGA;AAAA,oBAAA,IAAA,EAAA,MAAA;AAAA,oBAAA,IAAA,EAAA,aAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBAHA,CAAA;AAKA;;AA9BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiCA,IAAA,kBAjCA,gCAiCA;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,KAnCA;AAqCA,IAAA,YArCA,0BAqCA;AACA,WAAA,UAAA,GAAA,MAAA,CAAA,OAAA,GAAA,EAAA;AACA,KAvCA;AAyCA,IAAA,gBAzCA,8BAyCA;AACA,WAAA,cAAA,GAAA,CAAA,KAAA,cAAA;AACA,KA3CA;AA6CA,IAAA,eA7CA,6BA6CA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KA/CA;AAiDA;AACA,IAAA,qBAlDA,iCAkDA,WAlDA,EAkDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,WAAA;AACA,WAAA,QAAA,CAAA,IAAA,WAAA,WAAA,qFAAA,CAAA;AACA,KArDA;AAuDA;AACA,IAAA,qBAxDA,iCAwDA,IAxDA,EAwDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,IAAA,CAAA,IAAA,EAAA,OAAA,EAAA,IAAA,CAAA,IAAA;;AACA,UAAA,CAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,IAAA,KAAA,EAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,IAAA;AACA,aAAA,QAAA,CAAA,IAAA,WAAA,IAAA,CAAA,IAAA,qFAAA,CAAA;AACA,OAHA,MAGA;AACA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;;AACA,WAAA,eAAA;AACA,KAlEA;AAoEA,IAAA,MApEA,oBAoEA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,KAtEA;AAwEA,IAAA,WAxEA,yBAwEA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,QAAA;AACA,KA1EA;AA4EA,IAAA,eA5EA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8EA;AACA,gBAAA,KA/EA,GA+EA,GAAA,CAAA,EAAA,CAAA,GAAA,CAAA,YAAA,CA/EA;;AAAA,oBAgFA,KAhFA;AAAA;AAAA;AAAA;;AAiFA,qBAAA,UAAA,GAAA,KAAA;AACA,qBAAA,OAAA,GAAA,KAAA;AAlFA;;AAAA;AAsFA,qBAAA,UAAA,GAAA,IAAA,CAtFA,CAwFA;;AAxFA;AAAA,uBAyFA,WAAA,EAzFA;;AAAA;AAyFA,gBAAA,QAzFA;AAAA;AAAA,uBA0FA,OAAA,EA1FA;;AAAA;AA0FA,qBAAA,OA1FA;AA4FA;AACA,qBAAA,QAAA,GAAA;AACA,kBAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA,IAAA,IADA;AAEA,kBAAA,IAAA,EAAA;AAFA,iBAAA;AAKA,gBAAA,OAAA,CAAA,GAAA,CAAA,uBAAA,EAAA;AACA,kBAAA,UAAA,EAAA,KAAA,UADA;AAEA,kBAAA,OAAA,EAAA,KAAA,OAFA;AAGA,kBAAA,QAAA,EAAA,KAAA;AAHA,iBAAA;AAlGA;AAAA;;AAAA;AAAA;AAAA;AAyGA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,UAAA,GAAA,KAAA;AACA,qBAAA,OAAA,GAAA,KAAA;;AA3GA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+GA,IAAA,SA/GA,uBA+GA;AACA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,qBAAA;AACA,WAAA,eAAA;AACA,KAnHA;AAqHA,IAAA,oBArHA,kCAqHA;AAAA;;AACA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,QAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,QAAA,EAAA,KAAA,CAAA,KAAA,CAAA,OAAA,EAAA,KAAA,CAAA,KAAA,CAAA,UAAA,EAAA,MAAA,CAAA,UAAA,EAAA;AAAA,iBAAA,EAAA;AAAA,SAAA,CAAA;;AACA,YAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,YAAA,QAAA,EAAA,GADA;AAEA,YAAA,CAAA,EAAA,CAAA,EAFA;AAGA,YAAA,OAAA,EAAA,CAHA;AAIA,YAAA,IAAA,EAAA,YAJA;AAKA,YAAA,OAAA,EAAA;AALA,WAAA;AAOA;AACA,OAXA;AAYA;AAnIA;AApCA,CAAA", "sourcesContent": ["<template>\n  <nav class=\"website-navbar\" :class=\"{ 'scrolled': isScrolled, 'transparent': isTransparent }\" ref=\"navbar\">\n    <div class=\"nav-container\">\n      <div class=\"nav-brand\" ref=\"navBrand\" @click=\"goHome\">\n        <LogoImage\n          size=\"medium\"\n          :hover=\"true\"\n          container-class=\"brand-logo-container\"\n          image-class=\"brand-logo-image\"\n          fallback-class=\"brand-logo-fallback\"\n        />\n        <span class=\"brand-text\">智界AIGC</span>\n      </div>\n\n      <div class=\"nav-menu\" ref=\"navMenu\">\n        <component\n          v-for=\"item in menuItems\"\n          :key=\"item.name\"\n          :is=\"item.path && item.path !== '' ? 'router-link' : 'span'\"\n          :to=\"item.path && item.path !== '' ? item.path : undefined\"\n          class=\"nav-link\"\n          :class=\"{\n            'active': $route.path === item.path,\n            'nav-link-disabled': !item.path || item.path === ''\n          }\"\n          @click=\"(!item.path || item.path === '') ? handleDevelopingClick(item.name) : undefined\"\n        >\n          <a-icon :type=\"item.icon\" class=\"nav-icon\" />\n          <span class=\"nav-text\">{{ item.name }}</span>\n        </component>\n      </div>\n\n      <div class=\"nav-actions\" ref=\"navActions\">\n        <button v-if=\"!isLoggedIn\" class=\"btn-secondary\" @click=\"handleLogin\">登录</button>\n        <button v-else-if=\"isAdmin\" class=\"btn-admin\" @click=\"goToAdmin\">\n          <a-icon type=\"dashboard\" />\n          后台管理\n        </button>\n      </div>\n\n      <button class=\"mobile-menu-btn\" @click=\"toggleMobileMenu\" ref=\"mobileMenuBtn\">\n        <a-icon :type=\"mobileMenuOpen ? 'close' : 'menu'\" />\n      </button>\n    </div>\n\n    <!-- 移动端菜单 -->\n    <div class=\"mobile-menu\" :class=\"{ 'open': mobileMenuOpen }\" ref=\"mobileMenu\">\n      <component\n        v-for=\"item in menuItems\"\n        :key=\"item.name\"\n        :is=\"item.path && item.path !== '' ? 'router-link' : 'span'\"\n        :to=\"item.path && item.path !== '' ? item.path : undefined\"\n        class=\"mobile-nav-link\"\n        :class=\"{ 'mobile-nav-link-disabled': !item.path || item.path === '' }\"\n        @click=\"handleMobileMenuClick(item)\"\n      >\n        <a-icon :type=\"item.icon\" class=\"mobile-nav-icon\" />\n        <span class=\"mobile-nav-text\">{{ item.name }}</span>\n      </component>\n      <div class=\"mobile-actions\">\n        <button v-if=\"!isLoggedIn\" class=\"mobile-btn-login\" @click=\"handleLogin\">登录</button>\n        <button v-else-if=\"isAdmin\" class=\"mobile-btn-admin\" @click=\"goToAdmin\">\n          <a-icon type=\"dashboard\" />\n          后台管理\n        </button>\n      </div>\n    </div>\n  </nav>\n</template>\n\n<script>\nimport { gsap } from 'gsap'\nimport { ACCESS_TOKEN } from '@/store/mutation-types'\nimport { isAdmin, getUserRole } from '@/utils/roleUtils'\nimport Vue from 'vue'\nimport LogoImage from '@/components/common/LogoImage.vue'\n\nexport default {\n  name: 'WebsiteHeader',\n  components: {\n    LogoImage\n  },\n  props: {\n    // 是否使用透明背景（首页专用）\n    transparent: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      isScrolled: false,\n      mobileMenuOpen: false,\n      menuItems: [],\n      userInfo: {},\n      isLoggedIn: false,\n      isAdmin: false\n    }\n  },\n  computed: {\n    isTransparent() {\n      return this.transparent && !this.isScrolled\n    }\n  },\n  async mounted() {\n    await this.loadMenuData()\n    await this.checkUserStatus()\n    this.initScrollListener()\n    this.initNavbarAnimations()\n  },\n  beforeDestroy() {\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  methods: {\n    async loadMenuData() {\n      try {\n        // TODO: 从API获取菜单数据\n        // const response = await this.$http.get('/api/website/header/menu')\n        // this.menuItems = response.data\n        \n        // 临时数据，后续替换为API调用\n        this.menuItems = [\n          { name: '首页', path: '/home', icon: 'home' },\n          { name: '插件中心', path: '/market', icon: 'shop' },\n          { name: '工作流中心', path: '/workflow-center', icon: 'deployment-unit' },\n          // { name: '客户案例', path: '/cases', icon: 'trophy' },\n          // { name: '教程中心', path: '/tutorials', icon: 'book' },\n          // { name: '签到奖励', path: '/signin', icon: 'gift' },\n          { name: '客户案例', path: '', icon: 'trophy' },\n          { name: '教程中心', path: '', icon: 'book' },\n          { name: '签到奖励', path: '', icon: 'gift' },\n          { name: '订阅会员', path: '/membership', icon: 'crown' },\n          { name: '邀请奖励', path: '/affiliate', icon: 'team' },\n          { name: '个人中心', path: '/usercenter', icon: 'user' }\n        ]\n      } catch (error) {\n        console.error('加载菜单数据失败:', error)\n        // 降级方案\n        this.menuItems = [\n          { name: '首页', path: '/', icon: 'home' },\n          { name: '插件中心', path: '/market', icon: 'shop' },\n          { name: '个人中心', path: '/usercenter', icon: 'user' }\n        ]\n      }\n    },\n    \n    initScrollListener() {\n      window.addEventListener('scroll', this.handleScroll)\n    },\n    \n    handleScroll() {\n      this.isScrolled = window.scrollY > 50\n    },\n    \n    toggleMobileMenu() {\n      this.mobileMenuOpen = !this.mobileMenuOpen\n    },\n    \n    closeMobileMenu() {\n      this.mobileMenuOpen = false\n    },\n\n    // 🔥 处理桌面端开发中功能点击\n    handleDevelopingClick(featureName) {\n      console.log('🎯 桌面端开发中功能点击:', featureName)\n      this.$message.info(`${featureName}功能正在开发中，敬请期待！`, 3)\n    },\n\n    // 🔥 处理移动端菜单点击\n    handleMobileMenuClick(item) {\n      console.log('🎯 移动端菜单点击:', item.name, 'path:', item.path)\n      if (!item.path || item.path === '') {\n        console.log('🎯 移动端开发中功能点击:', item.name)\n        this.$message.info(`${item.name}功能正在开发中，敬请期待！`, 3)\n      } else {\n        // 有效路径，进行跳转\n        this.$router.push(item.path)\n      }\n      this.closeMobileMenu()\n    },\n\n    goHome() {\n      this.$router.push('/')\n    },\n    \n    handleLogin() {\n      this.$router.push('/login')\n    },\n\n    async checkUserStatus() {\n      try {\n        // 检查是否有TOKEN\n        const token = Vue.ls.get(ACCESS_TOKEN)\n        if (!token) {\n          this.isLoggedIn = false\n          this.isAdmin = false\n          return\n        }\n\n        this.isLoggedIn = true\n\n        // 检查用户角色\n        const userRole = await getUserRole()\n        this.isAdmin = await isAdmin()\n\n        // 获取用户信息\n        this.userInfo = {\n          username: this.$store.getters.username || '用户',\n          role: userRole\n        }\n\n        console.log('🔍 WebsiteHeader用户状态:', {\n          isLoggedIn: this.isLoggedIn,\n          isAdmin: this.isAdmin,\n          userInfo: this.userInfo\n        })\n\n      } catch (error) {\n        console.error('检查用户状态失败:', error)\n        this.isLoggedIn = false\n        this.isAdmin = false\n      }\n    },\n\n    goToAdmin() {\n      // 跳转到后台管理首页\n      this.$router.push('/dashboard/analysis')\n      this.closeMobileMenu()\n    },\n\n    initNavbarAnimations() {\n      // 导航栏入场动画 - 添加refs存在检查\n      this.$nextTick(() => {\n        const elements = [this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions].filter(el => el)\n        if (elements.length > 0) {\n          gsap.from(elements, {\n            duration: 0.4,\n            y: -20,\n            opacity: 0,\n            ease: \"power2.out\",\n            stagger: 0.05\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 官网导航栏样式 - 支持透明和普通两种模式 */\n.website-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 1000;\n  padding: 1.75rem 0;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(25px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.15);\n  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);\n  /* 添加微妙的渐变边框 */\n  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent) 1;\n}\n\n/* 透明模式（首页专用） */\n.website-navbar.transparent {\n  background: transparent;\n  backdrop-filter: none;\n  border-bottom: none;\n  box-shadow: none;\n}\n\n.website-navbar.scrolled {\n  background: rgba(255, 255, 255, 0.99);\n  backdrop-filter: blur(30px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 12px 50px rgba(59, 130, 246, 0.12), 0 4px 16px rgba(0, 0, 0, 0.06);\n  padding: 1.5rem 0;\n  /* 滚动后增强效果 */\n  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent) 1;\n}\n\n.nav-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 品牌Logo区域 */\n.nav-brand {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1e293b;\n  text-decoration: none;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  margin-right: 1rem;\n  flex-shrink: 0;\n  position: relative;\n}\n\n/* 透明模式下的品牌样式 */\n.website-navbar.transparent .nav-brand {\n  color: #ffffff;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n.nav-brand:hover {\n  transform: translateY(-2px);\n}\n\n/* Logo容器样式 */\n.brand-logo-container {\n  width: 48px;\n  height: 48px;\n  border-radius: 14px;\n  overflow: hidden;\n  position: relative;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25), 0 2px 8px rgba(139, 92, 246, 0.15);\n}\n\n.brand-logo-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.6s ease;\n  z-index: 1;\n}\n\n.brand-logo-image {\n  width: 100% !important;\n  height: 100% !important;\n  object-fit: cover;\n  border-radius: 14px;\n}\n\n/* Fallback样式（当logo图片加载失败时） */\n.brand-logo-fallback {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border-radius: 14px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.4rem;\n}\n\n/* 悬停效果 */\n.nav-brand:hover .brand-logo-container {\n  transform: scale(1.08) rotate(8deg);\n  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35), 0 4px 12px rgba(139, 92, 246, 0.2);\n}\n\n.nav-brand:hover .brand-logo-container::before {\n  left: 100%;\n}\n\n.brand-text {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.nav-brand:hover .brand-text {\n  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  transform: translateY(-1px);\n}\n\n/* 透明模式下的品牌文字 */\n.website-navbar.transparent .brand-text {\n  color: #ffffff;\n  background: none;\n  -webkit-text-fill-color: #ffffff;\n  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n}\n\n/* 导航菜单 */\n.nav-menu {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: nowrap;\n  flex: 1;\n  justify-content: center;\n}\n\n.nav-link {\n  color: rgba(30, 41, 59, 0.8);\n  text-decoration: none;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  padding: 0.875rem 1.125rem;\n  border-radius: 12px;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  overflow: hidden;\n}\n\n/* 透明模式下的导航链接 */\n.website-navbar.transparent .nav-link {\n  color: rgba(255, 255, 255, 0.9);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);\n  transition: left 0.6s ease;\n  z-index: -1;\n}\n\n.nav-link:hover,\n.nav-link.active {\n  color: #3b82f6;\n  background: rgba(59, 130, 246, 0.1);\n  transform: translateY(-3px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.nav-link:hover::before {\n  left: 100%;\n}\n\n/* 透明模式下的导航链接悬停 */\n.website-navbar.transparent .nav-link:hover {\n  color: #ffffff;\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10px);\n}\n\n.nav-link::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  width: 0;\n  height: 3px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateX(-50%);\n  border-radius: 2px;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n\n.nav-link:hover::after,\n.nav-link.active::after {\n  width: 85%;\n}\n\n/* 导航图标和文字 */\n.nav-icon {\n  font-size: 0.9rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-text {\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-link:hover .nav-icon {\n  transform: scale(1.1) rotate(5deg);\n}\n\n.nav-link:hover .nav-text {\n  transform: translateX(2px);\n}\n\n.nav-link:hover .nav-icon,\n.nav-link.active .nav-icon {\n  color: #3b82f6;\n  transform: scale(1.1);\n}\n\n.nav-link:hover .nav-text,\n.nav-link.active .nav-text {\n  color: #3b82f6;\n}\n\n/* 🔥 开发中菜单项样式 */\n.nav-link-disabled {\n  color: rgba(30, 41, 59, 0.6) !important;\n  cursor: pointer;\n}\n\n.nav-link-disabled:hover {\n  color: rgba(30, 41, 59, 0.8) !important;\n  background: rgba(249, 115, 22, 0.1);\n}\n\n.website-navbar.transparent .nav-link-disabled {\n  color: rgba(255, 255, 255, 0.7) !important;\n}\n\n.website-navbar.transparent .nav-link-disabled:hover {\n  color: rgba(255, 255, 255, 0.9) !important;\n  background: rgba(249, 115, 22, 0.2);\n}\n\n/* 透明模式下的图标和文字悬停 */\n.website-navbar.transparent .nav-link:hover .nav-icon,\n.website-navbar.transparent .nav-link:hover .nav-text {\n  color: #ffffff;\n}\n\n/* 右侧操作区 */\n.nav-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n.btn-secondary,\n.btn-admin {\n  padding: 0.875rem 1.75rem;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.btn-secondary {\n  background: transparent;\n  border: 2px solid rgba(59, 130, 246, 0.3);\n  color: #3b82f6;\n}\n\n.btn-secondary::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);\n  transition: left 0.6s ease;\n}\n\n.btn-admin {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border: none;\n  color: white;\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);\n}\n\n/* 透明模式下的按钮 */\n.website-navbar.transparent .btn-secondary {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: #ffffff;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.website-navbar.transparent .btn-admin {\n  background: rgba(16, 185, 129, 0.9);\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n.btn-secondary:hover {\n  background: rgba(59, 130, 246, 0.12);\n  border-color: rgba(59, 130, 246, 0.6);\n  transform: translateY(-3px);\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);\n}\n\n.btn-secondary:hover::before {\n  left: 100%;\n}\n\n.btn-admin:hover {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.35);\n}\n\n/* 透明模式下的按钮悬停 */\n.website-navbar.transparent .btn-secondary:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.website-navbar.transparent .btn-admin:hover {\n  background: rgba(5, 150, 105, 0.95);\n  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);\n}\n\n/* 移动端菜单按钮 */\n.mobile-menu-btn {\n  display: none;\n  background: none;\n  border: none;\n  color: #1e293b;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n/* 透明模式下的移动端按钮 */\n.website-navbar.transparent .mobile-menu-btn {\n  color: #ffffff;\n  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);\n}\n\n.mobile-menu-btn:hover {\n  background: rgba(59, 130, 246, 0.1);\n  color: #3b82f6;\n}\n\n/* 透明模式下的移动端按钮悬停 */\n.website-navbar.transparent .mobile-menu-btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  color: #ffffff;\n}\n\n/* 移动端菜单 */\n.mobile-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(59, 130, 246, 0.1);\n  box-shadow: 0 4px 30px rgba(59, 130, 246, 0.12);\n  transform: translateY(-100%);\n  opacity: 0;\n  visibility: hidden;\n  transition: all 0.3s ease;\n  padding: 1rem 0;\n}\n\n.mobile-menu.open {\n  transform: translateY(0);\n  opacity: 1;\n  visibility: visible;\n}\n\n.mobile-nav-link {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem 2rem;\n  color: rgba(30, 41, 59, 0.8);\n  text-decoration: none;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.mobile-nav-link:hover {\n  background: rgba(59, 130, 246, 0.08);\n  color: #3b82f6;\n}\n\n.mobile-nav-icon {\n  font-size: 1.1rem;\n}\n\n/* 🔥 移动端开发中菜单项样式 */\n.mobile-nav-link-disabled {\n  color: rgba(30, 41, 59, 0.6) !important;\n  cursor: pointer;\n}\n\n.mobile-nav-link-disabled:hover {\n  background: rgba(249, 115, 22, 0.1) !important;\n  color: rgba(30, 41, 59, 0.8) !important;\n}\n\n.mobile-actions {\n  padding: 1rem 2rem;\n  border-top: 1px solid rgba(59, 130, 246, 0.1);\n  margin-top: 1rem;\n}\n\n.mobile-btn-login,\n.mobile-btn-admin {\n  width: 100%;\n  padding: 0.875rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n\n.mobile-btn-login {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n}\n\n.mobile-btn-admin {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  color: white;\n}\n\n.mobile-btn-login:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\n}\n\n.mobile-btn-admin:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n}\n\n\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .nav-container {\n    padding: 0 1.5rem;\n  }\n  \n  .nav-menu {\n    gap: 0.3rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .nav-menu,\n  .nav-actions {\n    display: none;\n  }\n  \n  .mobile-menu-btn {\n    display: flex;\n  }\n  \n  .nav-container {\n    padding: 0 1rem;\n  }\n}\n</style>\n"], "sourceRoot": "src/components/website"}]}