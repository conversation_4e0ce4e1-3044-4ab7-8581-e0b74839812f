{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue", "mtime": 1753959561940}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\nimport AigcAgentForm from './AigcAgentForm'\n\nexport default {\n  name: 'AigcAgentModal',\n  components: {\n    AigcAgentForm\n  },\n  data() {\n    return {\n      title:'',\n      width:800,\n      visible: false,\n      disableSubmit: false\n    }\n  },\n  methods:{\n    add () {\n      this.visible=true\n      this.$nextTick(()=>{\n        this.$refs.realForm.add();\n      })\n    },\n    edit (record) {\n      this.visible=true\n      this.$nextTick(()=>{\n        this.$refs.realForm.edit(record);\n      })\n    },\n    close () {\n      // 🔥 关闭时回滚头像变更\n      if (this.$refs.realForm && this.$refs.realForm.handleClose) {\n        this.$refs.realForm.handleClose();\n      }\n      this.$emit('close');\n      this.visible = false;\n    },\n    handleOk () {\n      this.$refs.realForm.handleOk();\n    },\n    submitCallback(){\n      this.$emit('ok');\n      this.visible = false;\n    },\n    handleCancel () {\n      this.close()\n    }\n  }\n}\n", {"version": 3, "sources": ["AigcAgentModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AigcAgentModal.vue", "sourceRoot": "src/views/aigcview/agent/modules", "sourcesContent": ["<template>\n  <j-modal\n    :title=\"title\"\n    :width=\"1200\"\n    :visible=\"visible\"\n    :maskClosable=\"false\"\n    switchFullscreen\n    @ok=\"handleOk\"\n    :okButtonProps=\"{ class:{'jee-hidden': disableSubmit} }\"\n    @cancel=\"handleCancel\">\n    <aigc-agent-form ref=\"realForm\" @ok=\"submitCallback\" :disabled=\"disableSubmit\"/>\n  </j-modal>\n</template>\n\n<script>\n\n  import AigcAgentForm from './AigcAgentForm'\n\n  export default {\n    name: 'AigcAgentModal',\n    components: {\n      AigcAgentForm\n    },\n    data() {\n      return {\n        title:'',\n        width:800,\n        visible: false,\n        disableSubmit: false\n      }\n    },\n    methods:{\n      add () {\n        this.visible=true\n        this.$nextTick(()=>{\n          this.$refs.realForm.add();\n        })\n      },\n      edit (record) {\n        this.visible=true\n        this.$nextTick(()=>{\n          this.$refs.realForm.edit(record);\n        })\n      },\n      close () {\n        // 🔥 关闭时回滚头像变更\n        if (this.$refs.realForm && this.$refs.realForm.handleClose) {\n          this.$refs.realForm.handleClose();\n        }\n        this.$emit('close');\n        this.visible = false;\n      },\n      handleOk () {\n        this.$refs.realForm.handleOk();\n      },\n      submitCallback(){\n        this.$emit('ok');\n        this.visible = false;\n      },\n      handleCancel () {\n        this.close()\n      }\n    }\n  }\n</script>\n\n<style scoped>\n</style>"]}]}