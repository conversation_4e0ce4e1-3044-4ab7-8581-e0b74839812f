{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\PluginDetail.vue", "mtime": 1753945012309}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./PluginDetail.vue?vue&type=template&id=a4b5014e&scoped=true&\"\nimport script from \"./PluginDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./PluginDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./PluginDetail.vue?vue&type=style&index=0&id=a4b5014e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a4b5014e\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\AigcView_zj\\\\AigcViewFe\\\\智界Aigc\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('a4b5014e')) {\n      api.createRecord('a4b5014e', component.options)\n    } else {\n      api.reload('a4b5014e', component.options)\n    }\n    module.hot.accept(\"./PluginDetail.vue?vue&type=template&id=a4b5014e&scoped=true&\", function () {\n      api.rerender('a4b5014e', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/website/market/PluginDetail.vue\"\nexport default component.exports"]}