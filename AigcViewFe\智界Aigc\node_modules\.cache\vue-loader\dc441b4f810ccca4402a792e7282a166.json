{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue?vue&type=template&id=81cbcc72&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue", "mtime": 1753968167936}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-spin\",\n    { attrs: { spinning: _vm.confirmLoading } },\n    [\n      _c(\n        \"j-form-container\",\n        { attrs: { disabled: _vm.formDisabled } },\n        [\n          _c(\n            \"a-form-model\",\n            {\n              ref: \"form\",\n              attrs: {\n                slot: \"detail\",\n                model: _vm.model,\n                rules: _vm.validatorRules\n              },\n              slot: \"detail\"\n            },\n            [\n              _c(\n                \"a-row\",\n                [\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"作者类型\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"authorType\"\n                          }\n                        },\n                        [\n                          _c(\"j-dict-select-tag\", {\n                            attrs: {\n                              type: \"list\",\n                              dictCode: \"author_type\",\n                              placeholder: \"请选择作者类型\"\n                            },\n                            model: {\n                              value: _vm.model.authorType,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"authorType\", $$v)\n                              },\n                              expression: \"model.authorType\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"智能体ID\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"agentId\"\n                          }\n                        },\n                        [\n                          _c(\"a-input\", {\n                            attrs: { placeholder: \"请输入智能体ID\" },\n                            model: {\n                              value: _vm.model.agentId,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"agentId\", $$v)\n                              },\n                              expression: \"model.agentId\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"智能体名称\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"agentName\"\n                          }\n                        },\n                        [\n                          _c(\"a-input\", {\n                            attrs: { placeholder: \"请输入智能体名称\" },\n                            model: {\n                              value: _vm.model.agentName,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"agentName\", $$v)\n                              },\n                              expression: \"model.agentName\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"智能体描述\",\n                            labelCol: _vm.labelCol2,\n                            wrapperCol: _vm.wrapperCol2,\n                            prop: \"agentDescription\"\n                          }\n                        },\n                        [\n                          _c(\"a-textarea\", {\n                            attrs: {\n                              rows: \"4\",\n                              placeholder: \"请输入智能体描述\"\n                            },\n                            model: {\n                              value: _vm.model.agentDescription,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"agentDescription\", $$v)\n                              },\n                              expression: \"model.agentDescription\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"智能体头像\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"agentAvatar\"\n                          }\n                        },\n                        [\n                          _c(\"j-image-upload-deferred\", {\n                            ref: \"avatarUpload\",\n                            attrs: {\n                              isMultiple: false,\n                              bizPath: \"agent-avatar\",\n                              text: \"上传头像\"\n                            },\n                            model: {\n                              value: _vm.model.agentAvatar,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"agentAvatar\", $$v)\n                              },\n                              expression: \"model.agentAvatar\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"展示视频\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"demoVideo\"\n                          }\n                        },\n                        [\n                          _c(\"j-upload\", {\n                            attrs: {\n                              beforeUpload: _vm.beforeVideoUpload,\n                              text: \"上传视频(最大100MB)\"\n                            },\n                            model: {\n                              value: _vm.model.demoVideo,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"demoVideo\", $$v)\n                              },\n                              expression: \"model.demoVideo\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"体验链接\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"experienceLink\"\n                          }\n                        },\n                        [\n                          _c(\"a-input\", {\n                            attrs: { placeholder: \"请输入体验链接\" },\n                            model: {\n                              value: _vm.model.experienceLink,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"experienceLink\", $$v)\n                              },\n                              expression: \"model.experienceLink\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"价格（元）\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"price\"\n                          }\n                        },\n                        [\n                          _c(\"a-input-number\", {\n                            staticStyle: { width: \"100%\" },\n                            attrs: { placeholder: \"请输入价格（元）\" },\n                            model: {\n                              value: _vm.model.price,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"price\", $$v)\n                              },\n                              expression: \"model.price\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 8 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"审核状态\",\n                            labelCol: _vm.labelCol,\n                            wrapperCol: _vm.wrapperCol,\n                            prop: \"auditStatus\"\n                          }\n                        },\n                        [\n                          _c(\"j-dict-select-tag\", {\n                            attrs: {\n                              type: \"list\",\n                              dictCode: \"audit_status\",\n                              placeholder: \"请选择审核状态\"\n                            },\n                            model: {\n                              value: _vm.model.auditStatus,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"auditStatus\", $$v)\n                              },\n                              expression: \"model.auditStatus\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"a-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"审核备注\",\n                            labelCol: _vm.labelCol2,\n                            wrapperCol: _vm.wrapperCol2,\n                            prop: \"auditRemark\"\n                          }\n                        },\n                        [\n                          _c(\"a-textarea\", {\n                            attrs: { rows: \"4\", placeholder: \"请输入审核备注\" },\n                            model: {\n                              value: _vm.model.auditRemark,\n                              callback: function($$v) {\n                                _vm.$set(_vm.model, \"auditRemark\", $$v)\n                              },\n                              expression: \"model.auditRemark\"\n                            }\n                          })\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\n        \"a-tabs\",\n        {\n          on: { change: _vm.handleChangeTabs },\n          model: {\n            value: _vm.activeKey,\n            callback: function($$v) {\n              _vm.activeKey = $$v\n            },\n            expression: \"activeKey\"\n          }\n        },\n        [\n          _c(\n            \"a-tab-pane\",\n            {\n              key: _vm.refKeys[0],\n              attrs: { tab: \"工作流表\", forceRender: true }\n            },\n            [\n              _c(\"j-editable-table\", {\n                ref: _vm.refKeys[0],\n                attrs: {\n                  loading: _vm.aigcWorkflowTable.loading,\n                  columns: _vm.aigcWorkflowTable.columns,\n                  dataSource: _vm.aigcWorkflowTable.dataSource,\n                  maxHeight: 300,\n                  disabled: _vm.formDisabled,\n                  rowNumber: true,\n                  rowSelection: true,\n                  actionButton: true\n                }\n              })\n            ],\n            1\n          )\n        ],\n        1\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}