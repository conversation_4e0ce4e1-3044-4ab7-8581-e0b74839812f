{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue?vue&type=template&id=3c3fbfba&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentModal.vue", "mtime": 1753959561940}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<j-modal\n  :title=\"title\"\n  :width=\"1200\"\n  :visible=\"visible\"\n  :maskClosable=\"false\"\n  switchFullscreen\n  @ok=\"handleOk\"\n  :okButtonProps=\"{ class:{'jee-hidden': disableSubmit} }\"\n  @cancel=\"handleCancel\">\n  <aigc-agent-form ref=\"realForm\" @ok=\"submitCallback\" :disabled=\"disableSubmit\"/>\n</j-modal>\n", null]}