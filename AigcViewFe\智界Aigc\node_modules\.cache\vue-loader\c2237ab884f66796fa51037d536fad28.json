{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue?vue&type=template&id=3d278fe4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue", "mtime": 1754041473614}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"agent-card\", on: { click: _vm.handleCardClick } },\n    [\n      _vm.agent.showSvipPromo && _vm.agent.authorType === \"1\"\n        ? _c(\"div\", { staticClass: \"svip-promo-tag svip-free\" }, [\n            _vm._v(\"\\n    SVIP免费\\n  \")\n          ])\n        : _vm._e(),\n      _vm.agent.showSvipPromo && _vm.agent.authorType === \"2\"\n        ? _c(\"div\", { staticClass: \"svip-promo-tag svip-discount\" }, [\n            _vm._v(\"\\n    SVIP 5折\\n  \")\n          ])\n        : _vm._e(),\n      _vm.agent.isPurchased\n        ? _c(\n            \"div\",\n            { staticClass: \"purchased-tag\" },\n            [\n              _c(\"a-icon\", { attrs: { type: \"check-circle\" } }),\n              _c(\"span\", [_vm._v(\"已购买\")])\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\"div\", { staticClass: \"agent-cover\" }, [\n        _c(\"div\", { staticClass: \"cover-image\" }, [\n          _vm.agent.demoVideo\n            ? _c(\"video\", {\n                staticClass: \"cover-video\",\n                attrs: {\n                  src: _vm.agent.demoVideo,\n                  muted: \"\",\n                  loop: \"\",\n                  preload: \"metadata\"\n                },\n                domProps: { muted: true },\n                on: {\n                  mouseenter: _vm.handleVideoHover,\n                  mouseleave: _vm.handleVideoLeave,\n                  loadedmetadata: _vm.handleVideoLoaded\n                }\n              })\n            : _vm.agent.agentAvatar\n            ? _c(\"img\", {\n                staticClass: \"cover-image-img\",\n                attrs: { src: _vm.agent.agentAvatar, alt: _vm.agent.agentName },\n                on: { error: _vm.handleImageError }\n              })\n            : _c(\n                \"div\",\n                { staticClass: \"cover-placeholder\" },\n                [_c(\"a-icon\", { attrs: { type: \"robot\" } })],\n                1\n              )\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"author-type-tag\", class: _vm.authorTypeClass },\n          [\n            _c(\"a-icon\", { attrs: { type: _vm.authorTypeIcon } }),\n            _c(\"span\", [_vm._v(_vm._s(_vm.authorTypeText))])\n          ],\n          1\n        ),\n        _vm.agent.showDiscountPrice && _vm.agent.discountRate === 0.7\n          ? _c(\n              \"div\",\n              { staticClass: \"vip-discount-tag\" },\n              [\n                _c(\"a-icon\", { attrs: { type: \"crown\" } }),\n                _c(\"span\", [_vm._v(\"VIP 7折\")])\n              ],\n              1\n            )\n          : _vm._e(),\n        _vm.agent.isFree\n          ? _c(\n              \"div\",\n              { staticClass: \"svip-free-tag\" },\n              [\n                _c(\"a-icon\", { attrs: { type: \"crown\" } }),\n                _c(\"span\", [_vm._v(\"SVIP 免费\")])\n              ],\n              1\n            )\n          : _vm.agent.showDiscountPrice && _vm.agent.discountRate === 0.5\n          ? _c(\n              \"div\",\n              { staticClass: \"svip-discount-tag\" },\n              [\n                _c(\"a-icon\", { attrs: { type: \"crown\" } }),\n                _c(\"span\", [_vm._v(\"SVIP 5折\")])\n              ],\n              1\n            )\n          : _vm._e()\n      ]),\n      _c(\"div\", { staticClass: \"agent-info\" }, [\n        _c(\"div\", { staticClass: \"agent-header\" }, [\n          _c(\n            \"h4\",\n            {\n              staticClass: \"agent-name\",\n              attrs: { title: _vm.agent.agentName }\n            },\n            [_vm._v(\"\\n        \" + _vm._s(_vm.agent.agentName) + \"\\n      \")]\n          ),\n          _c(\"div\", { staticClass: \"agent-price\" }, [\n            _vm.agent.isFree\n              ? _c(\"div\", { staticClass: \"price-container\" }, [\n                  _c(\"span\", { staticClass: \"free-price\" }, [_vm._v(\"免费\")])\n                ])\n              : _vm.agent.showDiscountPrice\n              ? _c(\"div\", { staticClass: \"price-container\" }, [\n                  _c(\"span\", { staticClass: \"discount-price\" }, [\n                    _vm._v(\n                      \"¥\" +\n                        _vm._s(\n                          Math.round(\n                            (_vm.agent.originalPrice || 0) *\n                              _vm.agent.discountRate\n                          )\n                        )\n                    )\n                  ]),\n                  _c(\"span\", { staticClass: \"original-price\" }, [\n                    _vm._v(\"¥\" + _vm._s(_vm.agent.originalPrice || 0))\n                  ])\n                ])\n              : _c(\"div\", { staticClass: \"price-container\" }, [\n                  _c(\"span\", { staticClass: \"current-price\" }, [\n                    _vm._v(\"¥\" + _vm._s(_vm.agent.originalPrice || 0))\n                  ])\n                ])\n          ])\n        ]),\n        _vm.agent.description\n          ? _c(\"div\", { staticClass: \"agent-description\" }, [\n              _vm._v(\"\\n      \" + _vm._s(_vm.agent.description) + \"\\n    \")\n            ])\n          : _vm._e(),\n        _c(\"div\", { staticClass: \"agent-meta\" }, [\n          _c(\"span\", { staticClass: \"creator-info\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"creator-avatar\" },\n              [\n                _vm.creatorAvatar\n                  ? _c(\"img\", {\n                      attrs: { src: _vm.creatorAvatar, alt: _vm.creatorName },\n                      on: { error: _vm.handleCreatorAvatarError }\n                    })\n                  : _c(\"a-icon\", { attrs: { type: \"user\" } })\n              ],\n              1\n            ),\n            _c(\"span\", { staticClass: \"creator-name\" }, [\n              _vm._v(_vm._s(_vm.creatorName))\n            ])\n          ]),\n          _c(\n            \"span\",\n            { staticClass: \"workflow-count\" },\n            [\n              _c(\"a-icon\", { attrs: { type: \"deployment-unit\" } }),\n              _vm._v(\n                \"\\n        \" + _vm._s(_vm.workflowCount) + \"个工作流\\n      \"\n              )\n            ],\n            1\n          )\n        ])\n      ])\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}