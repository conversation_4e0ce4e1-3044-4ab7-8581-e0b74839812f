{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\AigcAgentList.vue", "mtime": 1753951994089}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { JeecgListMixin } from '@/mixins/JeecgListMixin';\nimport AigcAgentModal from './modules/AigcAgentModal';\nimport { filterMultiDictText } from '@/components/dict/JDictSelectUtil';\nimport '@/assets/less/TableExpand.less';\nexport default {\n  name: \"AigcAgentList\",\n  mixins: [JeecgListMixin],\n  components: {\n    AigcAgentModal: AigcAgentModal\n  },\n  data: function data() {\n    return {\n      description: '智能体表管理页面',\n      // 表头\n      columns: [{\n        title: '#',\n        dataIndex: '',\n        key: 'rowIndex',\n        width: 60,\n        align: \"center\",\n        customRender: function customRender(t, r, index) {\n          return parseInt(index) + 1;\n        }\n      }, {\n        title: '作者类型',\n        align: \"center\",\n        dataIndex: 'authorType_dictText'\n      }, {\n        title: '智能体ID',\n        align: \"center\",\n        dataIndex: 'agentId'\n      }, {\n        title: '智能体名称',\n        align: \"center\",\n        dataIndex: 'agentName'\n      }, {\n        title: '智能体描述',\n        align: \"center\",\n        dataIndex: 'agentDescription'\n      }, {\n        title: '智能体头像',\n        align: \"center\",\n        dataIndex: 'agentAvatar',\n        scopedSlots: {\n          customRender: 'imgSlot'\n        }\n      }, {\n        title: '展示视频',\n        align: \"center\",\n        dataIndex: 'demoVideo',\n        scopedSlots: {\n          customRender: 'fileSlot'\n        }\n      }, {\n        title: '体验链接',\n        align: \"center\",\n        dataIndex: 'experienceLink'\n      }, {\n        title: '价格（元）',\n        align: \"center\",\n        dataIndex: 'price'\n      }, {\n        title: '审核状态',\n        align: \"center\",\n        dataIndex: 'auditStatus_dictText'\n      }, {\n        title: '审核备注',\n        align: \"center\",\n        dataIndex: 'auditRemark'\n      }, {\n        title: '操作',\n        dataIndex: 'action',\n        align: \"center\",\n        fixed: \"right\",\n        width: 147,\n        scopedSlots: {\n          customRender: 'action'\n        }\n      }],\n      url: {\n        list: \"/aigc_agent/aigcAgent/list\",\n        delete: \"/aigc_agent/aigcAgent/delete\",\n        deleteBatch: \"/aigc_agent/aigcAgent/deleteBatch\",\n        exportXlsUrl: \"/aigc_agent/aigcAgent/exportXls\",\n        importExcelUrl: \"aigc_agent/aigcAgent/importExcel\"\n      },\n      dictOptions: {},\n      superFieldList: []\n    };\n  },\n  created: function created() {\n    this.getSuperFieldList();\n  },\n  computed: {\n    importExcelUrl: function importExcelUrl() {\n      return \"\".concat(window._CONFIG['domianURL'], \"/\").concat(this.url.importExcelUrl);\n    }\n  },\n  methods: {\n    initDictConfig: function initDictConfig() {},\n    getSuperFieldList: function getSuperFieldList() {\n      var fieldList = [];\n      fieldList.push({\n        type: 'string',\n        value: 'authorType',\n        text: '作者类型',\n        dictCode: 'author_type'\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'agentId',\n        text: '智能体ID',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'agentName',\n        text: '智能体名称',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'agentDescription',\n        text: '智能体描述',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'agentAvatar',\n        text: '智能体头像',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'demoVideo',\n        text: '展示视频',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'experienceLink',\n        text: '体验链接',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'BigDecimal',\n        value: 'price',\n        text: '价格（元）',\n        dictCode: ''\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'auditStatus',\n        text: '审核状态',\n        dictCode: 'audit_status'\n      });\n      fieldList.push({\n        type: 'string',\n        value: 'auditRemark',\n        text: '审核备注',\n        dictCode: ''\n      });\n      this.superFieldList = fieldList;\n    }\n  }\n};", {"version": 3, "sources": ["AigcAgentList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA,SAAA,cAAA,QAAA,yBAAA;AACA,OAAA,cAAA,MAAA,0BAAA;AACA,SAAA,mBAAA,QAAA,mCAAA;AACA,OAAA,gCAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,MAAA,EAAA,CAAA,cAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,cAAA,EAAA;AADA,GAHA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,UADA;AAEA;AACA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,UAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,KAAA,EAAA,QALA;AAMA,QAAA,YAAA,EAAA,sBAAA,CAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AACA,iBAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;AARA,OADA,EAWA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAXA,EAgBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAhBA,EAqBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OArBA,EA0BA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA1BA,EA+BA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA,aAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OA/BA,EAqCA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA,WAHA;AAIA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAJA,OArCA,EA2CA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA3CA,EAgDA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAhDA,EAqDA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OArDA,EA0DA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA1DA,EA+DA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,QAHA;AAIA,QAAA,KAAA,EAAA,OAJA;AAKA,QAAA,KAAA,EAAA,GALA;AAMA,QAAA,WAAA,EAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AANA,OA/DA,CAHA;AA2EA,MAAA,GAAA,EAAA;AACA,QAAA,IAAA,EAAA,4BADA;AAEA,QAAA,MAAA,EAAA,8BAFA;AAGA,QAAA,WAAA,EAAA,mCAHA;AAIA,QAAA,YAAA,EAAA,iCAJA;AAKA,QAAA,cAAA,EAAA;AALA,OA3EA;AAmFA,MAAA,WAAA,EAAA,EAnFA;AAoFA,MAAA,cAAA,EAAA;AApFA,KAAA;AAsFA,GA7FA;AA8FA,EAAA,OA9FA,qBA8FA;AACA,SAAA,iBAAA;AACA,GAhGA;AAiGA,EAAA,QAAA,EAAA;AACA,IAAA,cAAA,EAAA,0BAAA;AACA,uBAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,cAAA,KAAA,GAAA,CAAA,cAAA;AACA;AAHA,GAjGA;AAsGA,EAAA,OAAA,EAAA;AACA,IAAA,cADA,4BACA,CACA,CAFA;AAGA,IAAA,iBAHA,+BAGA;AACA,UAAA,SAAA,GAAA,EAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,YAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,kBAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,aAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,gBAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,YAAA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,aAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,aAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,WAAA,cAAA,GAAA,SAAA;AACA;AAhBA;AAtGA,CAAA", "sourcesContent": ["<template>\n  <a-card :bordered=\"false\">\n    <!-- 查询区域 -->\n    <div class=\"table-page-search-wrapper\">\n      <a-form layout=\"inline\" @keyup.enter.native=\"searchQuery\">\n        <a-row :gutter=\"24\">\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"作者类型\">\n              <j-dict-select-tag placeholder=\"请选择作者类型\" v-model=\"queryParam.authorType\" dictCode=\"author_type\"/>\n            </a-form-item>\n          </a-col>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <a-form-item label=\"智能体名称\">\n              <a-input placeholder=\"请输入智能体名称\" v-model=\"queryParam.agentName\"></a-input>\n            </a-form-item>\n          </a-col>\n          <template v-if=\"toggleSearchStatus\">\n            <a-col :xl=\"10\" :lg=\"11\" :md=\"12\" :sm=\"24\">\n              <a-form-item label=\"价格（元）\">\n                <a-input placeholder=\"请输入最小值\" class=\"query-group-cust\" v-model=\"queryParam.price_begin\"></a-input>\n                <span class=\"query-group-split-cust\"></span>\n                <a-input placeholder=\"请输入最大值\" class=\"query-group-cust\" v-model=\"queryParam.price_end\"></a-input>\n              </a-form-item>\n            </a-col>\n            <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n              <a-form-item label=\"审核状态\">\n                <j-dict-select-tag placeholder=\"请选择审核状态\" v-model=\"queryParam.auditStatus\" dictCode=\"audit_status\"/>\n              </a-form-item>\n            </a-col>\n          </template>\n          <a-col :xl=\"6\" :lg=\"7\" :md=\"8\" :sm=\"24\">\n            <span style=\"float: left;overflow: hidden;\" class=\"table-page-search-submitButtons\">\n              <a-button type=\"primary\" @click=\"searchQuery\" icon=\"search\">查询</a-button>\n              <a-button type=\"primary\" @click=\"searchReset\" icon=\"reload\" style=\"margin-left: 8px\">重置</a-button>\n              <a @click=\"handleToggleSearch\" style=\"margin-left: 8px\">\n                {{ toggleSearchStatus ? '收起' : '展开' }}\n                <a-icon :type=\"toggleSearchStatus ? 'up' : 'down'\"/>\n              </a>\n            </span>\n          </a-col>\n        </a-row>\n      </a-form>\n    </div>\n    <!-- 查询区域-END -->\n    \n    <!-- 操作按钮区域 -->\n    <div class=\"table-operator\">\n      <a-button @click=\"handleAdd\" type=\"primary\" icon=\"plus\">新增</a-button>\n      <a-button type=\"primary\" icon=\"download\" @click=\"handleExportXls('智能体表')\">导出</a-button>\n      <a-upload name=\"file\" :showUploadList=\"false\" :multiple=\"false\" :headers=\"tokenHeader\" :action=\"importExcelUrl\" @change=\"handleImportExcel\">\n        <a-button type=\"primary\" icon=\"import\">导入</a-button>\n      </a-upload>\n      <!-- 高级查询区域 -->\n      <j-super-query :fieldList=\"superFieldList\" ref=\"superQueryModal\" @handleSuperQuery=\"handleSuperQuery\"></j-super-query>\n      <a-dropdown v-if=\"selectedRowKeys.length > 0\">\n        <a-menu slot=\"overlay\">\n          <a-menu-item key=\"1\" @click=\"batchDel\"><a-icon type=\"delete\"/>删除</a-menu-item>\n        </a-menu>\n        <a-button style=\"margin-left: 8px\"> 批量操作 <a-icon type=\"down\" /></a-button>\n      </a-dropdown>\n    </div>\n\n    <!-- table区域-begin -->\n    <div>\n      <div class=\"ant-alert ant-alert-info\" style=\"margin-bottom: 16px;\">\n        <i class=\"anticon anticon-info-circle ant-alert-icon\"></i> 已选择 <a style=\"font-weight: 600\">{{ selectedRowKeys.length }}</a>项\n        <a style=\"margin-left: 24px\" @click=\"onClearSelected\">清空</a>\n      </div>\n\n      <a-table\n        ref=\"table\"\n        size=\"middle\"\n        bordered\n        rowKey=\"id\"\n        class=\"j-table-force-nowrap\"\n        :scroll=\"{x:true}\"\n        :columns=\"columns\"\n        :dataSource=\"dataSource\"\n        :pagination=\"ipagination\"\n        :loading=\"loading\"\n        :rowSelection=\"{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}\"\n        @change=\"handleTableChange\">\n\n        <template slot=\"htmlSlot\" slot-scope=\"text\">\n          <div v-html=\"text\"></div>\n        </template>\n        <template slot=\"imgSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无图片</span>\n          <img v-else :src=\"getImgView(text)\" height=\"25px\" alt=\"\" style=\"max-width:80px;font-size: 12px;font-style: italic;\"/>\n        </template>\n        <template slot=\"fileSlot\" slot-scope=\"text\">\n          <span v-if=\"!text\" style=\"font-size: 12px;font-style: italic;\">无文件</span>\n          <a-button\n            v-else\n            :ghost=\"true\"\n            type=\"primary\"\n            icon=\"download\"\n            size=\"small\"\n            @click=\"downloadFile(text)\">\n            下载\n          </a-button>\n        </template>\n\n        <span slot=\"action\" slot-scope=\"text, record\">\n          <a @click=\"handleEdit(record)\">编辑</a>\n\n          <a-divider type=\"vertical\" />\n          <a-dropdown>\n            <a class=\"ant-dropdown-link\">更多 <a-icon type=\"down\" /></a>\n            <a-menu slot=\"overlay\">\n              <a-menu-item>\n                <a @click=\"handleDetail(record)\">详情</a>\n              </a-menu-item>\n              <a-menu-item>\n                <a-popconfirm title=\"确定删除吗?\" @confirm=\"() => handleDelete(record.id)\">\n                  <a>删除</a>\n                </a-popconfirm>\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </span>\n\n      </a-table>\n    </div>\n\n    <aigc-agent-modal ref=\"modalForm\" @ok=\"modalFormOk\"/>\n  </a-card>\n</template>\n\n<script>\n\n  import { JeecgListMixin } from '@/mixins/JeecgListMixin'\n  import AigcAgentModal from './modules/AigcAgentModal'\n  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'\n  import '@/assets/less/TableExpand.less'\n\n  export default {\n    name: \"AigcAgentList\",\n    mixins:[JeecgListMixin],\n    components: {\n      AigcAgentModal\n    },\n    data () {\n      return {\n        description: '智能体表管理页面',\n        // 表头\n        columns: [\n          {\n            title: '#',\n            dataIndex: '',\n            key:'rowIndex',\n            width:60,\n            align:\"center\",\n            customRender:function (t,r,index) {\n              return parseInt(index)+1;\n            }\n          },\n          {\n            title:'作者类型',\n            align:\"center\",\n            dataIndex: 'authorType_dictText'\n          },\n          {\n            title:'智能体ID',\n            align:\"center\",\n            dataIndex: 'agentId'\n          },\n          {\n            title:'智能体名称',\n            align:\"center\",\n            dataIndex: 'agentName'\n          },\n          {\n            title:'智能体描述',\n            align:\"center\",\n            dataIndex: 'agentDescription'\n          },\n          {\n            title:'智能体头像',\n            align:\"center\",\n            dataIndex: 'agentAvatar',\n            scopedSlots: {customRender: 'imgSlot'}\n          },\n          {\n            title:'展示视频',\n            align:\"center\",\n            dataIndex: 'demoVideo',\n            scopedSlots: {customRender: 'fileSlot'}\n          },\n          {\n            title:'体验链接',\n            align:\"center\",\n            dataIndex: 'experienceLink'\n          },\n          {\n            title:'价格（元）',\n            align:\"center\",\n            dataIndex: 'price'\n          },\n          {\n            title:'审核状态',\n            align:\"center\",\n            dataIndex: 'auditStatus_dictText'\n          },\n          {\n            title:'审核备注',\n            align:\"center\",\n            dataIndex: 'auditRemark'\n          },\n          {\n            title: '操作',\n            dataIndex: 'action',\n            align:\"center\",\n            fixed:\"right\",\n            width:147,\n            scopedSlots: { customRender: 'action' },\n          }\n        ],\n        url: {\n          list: \"/aigc_agent/aigcAgent/list\",\n          delete: \"/aigc_agent/aigcAgent/delete\",\n          deleteBatch: \"/aigc_agent/aigcAgent/deleteBatch\",\n          exportXlsUrl: \"/aigc_agent/aigcAgent/exportXls\",\n          importExcelUrl: \"aigc_agent/aigcAgent/importExcel\",\n          \n        },\n        dictOptions:{},\n        superFieldList:[],\n      }\n    },\n    created() {\n      this.getSuperFieldList();\n    },\n    computed: {\n      importExcelUrl: function(){\n        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;\n      }\n    },\n    methods: {\n      initDictConfig(){\n      },\n      getSuperFieldList(){\n        let fieldList=[];\n         fieldList.push({type:'string',value:'authorType',text:'作者类型',dictCode:'author_type'})\n         fieldList.push({type:'string',value:'agentId',text:'智能体ID',dictCode:''})\n         fieldList.push({type:'string',value:'agentName',text:'智能体名称',dictCode:''})\n         fieldList.push({type:'string',value:'agentDescription',text:'智能体描述',dictCode:''})\n         fieldList.push({type:'string',value:'agentAvatar',text:'智能体头像',dictCode:''})\n         fieldList.push({type:'string',value:'demoVideo',text:'展示视频',dictCode:''})\n         fieldList.push({type:'string',value:'experienceLink',text:'体验链接',dictCode:''})\n         fieldList.push({type:'BigDecimal',value:'price',text:'价格（元）',dictCode:''})\n         fieldList.push({type:'string',value:'auditStatus',text:'审核状态',dictCode:'audit_status'})\n         fieldList.push({type:'string',value:'auditRemark',text:'审核备注',dictCode:''})\n        this.superFieldList = fieldList\n      }\n    }\n  }\n</script>\n<style scoped>\n  @import '~@assets/less/common.less';\n</style>"], "sourceRoot": "src/views/aigcview/agent"}]}