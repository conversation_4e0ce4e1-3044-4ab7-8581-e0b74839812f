<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/db/tables_nacos.sql" beforeDir="false" afterPath="$PROJECT_DIR$/db/tables_nacos.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/db/增量SQL/会员订阅订单支持.sql" beforeDir="false" afterPath="$PROJECT_DIR$/db/增量SQL/会员订阅订单支持.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/bpm/api/IBpmBaseExtAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/bpm/api/IBpmBaseExtAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/online/api/IOnlineBaseExtAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/online/api/IOnlineBaseExtAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/system/api/ISysBaseAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-api/jeecg-system-local-api/target/classes/org/jeecg/common/system/api/ISysBaseAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/exception/JeecgBootExceptionHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/exception/JeecgBootExceptionHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/system/query/QueryGenerator.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/system/query/QueryGenerator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/util/CommonUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/common/util/CommonUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/config/shiro/ShiroConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/src/main/java/org/jeecg/config/shiro/ShiroConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/CommonAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/CommonAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/IWpsBaseAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/IWpsBaseAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/desform/ISysTranslateAPI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/desform/ISysTranslateAPI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/FileDownDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/FileDownDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/FileUploadDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/FileUploadDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/LogDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/LogDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/OnlineAuthDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/OnlineAuthDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/BusMessageDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/BusMessageDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/BusTemplateMessageDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/BusTemplateMessageDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/MessageDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/MessageDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/TemplateDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/TemplateDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/TemplateMessageDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/dto/message/TemplateMessageDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/vo/OaWpsModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/vo/OaWpsModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/vo/Result.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/api/vo/Result.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/AutoLogAspect$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/AutoLogAspect$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/AutoLogAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/AutoLogAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/DictAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/DictAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/PermissionDataAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/PermissionDataAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/UrlMatchEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/UrlMatchEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/AutoLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/AutoLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/Dict.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/Dict.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/OnlineAuth.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/OnlineAuth.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/PermissionData.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/aspect/annotation/PermissionData.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/CommonConstant.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/CommonConstant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/CommonSendStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/CommonSendStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/DataBaseConstant.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/DataBaseConstant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/FillRuleConstant.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/FillRuleConstant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ProvinceCityArea$Area.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ProvinceCityArea$Area.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ProvinceCityArea.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ProvinceCityArea.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ServiceNameConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/ServiceNameConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/VXESocketConst.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/VXESocketConst.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/WebsocketConst.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/WebsocketConst.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/CgformEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/CgformEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/ModuleType.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/ModuleType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/RoleIndexConfigEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/constant/enums/RoleIndexConfigEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/es/JeecgElasticsearchTemplate.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/es/JeecgElasticsearchTemplate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/es/QueryStringBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/es/QueryStringBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/JeecgBootException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/JeecgBootException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/JeecgBootExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/JeecgBootExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/TokenKickedException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/exception/TokenKickedException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/handler/IFillRuleHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/handler/IFillRuleHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/controller/JeecgController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/controller/JeecgController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/entity/JeecgEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/entity/JeecgEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/service/JeecgService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/service/JeecgService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/service/impl/JeecgServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/base/service/impl/JeecgServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/MatchTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/MatchTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryCondition.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryCondition.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryGenerator$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryRuleEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/query/QueryRuleEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/util/JeecgDataAutorUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/util/JeecgDataAutorUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/util/JwtUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/util/JwtUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/ComboModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/ComboModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictModelMany.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictModelMany.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictQuery.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DictQuery.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DynamicDataSourceModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/DynamicDataSourceModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/LoginUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/LoginUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysCategoryModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysCategoryModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysDepartModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysDepartModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysPermissionDataRuleModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysPermissionDataRuleModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysUserCacheInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/system/vo/SysUserCacheInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/BrowserType.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/BrowserType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/BrowserUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/BrowserUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/CommonUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/CommonUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DateUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DySmsEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DySmsEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DySmsHelper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/DySmsHelper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/FillRuleUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/FillRuleUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/HTMLUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/HTMLUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/IPUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/IPUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/ImportExcelUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/ImportExcelUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MD5Util.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MD5Util.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MinioUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MinioUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MyClassLoader.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/MyClassLoader.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PasswordUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PasswordUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil$Matcher.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil$Matcher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil$SpringAntMatcher.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil$SpringAntMatcher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PathMatcherUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PmsUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/PmsUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/ReflectHelper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/ReflectHelper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/RestDesformUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/RestDesformUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/RestUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/RestUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SpringContextUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SpringContextUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SqlInjectionUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SqlInjectionUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SysAnnmentTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/SysAnnmentTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/TokenUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/TokenUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/UUIDGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/UUIDGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/YouBianCodeUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/YouBianCodeUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DataSourceCachePool.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DataSourceCachePool.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DbTypeUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DbTypeUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DynamicDBUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/DynamicDBUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/FreemarkerParseFactory.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/dynamic/db/FreemarkerParseFactory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/encryption/AesEncryptUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/encryption/AesEncryptUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/encryption/EncryptedString.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/encryption/EncryptedString.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/filter/FileTypeFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/filter/FileTypeFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/filter/StrAttackFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/filter/StrAttackFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/oConvertUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/oConvertUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/oss/OssBootUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/oss/OssBootUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/SecurityTools.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/SecurityTools.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/MyKeyPair.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/MyKeyPair.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecurityReq.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecurityReq.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecurityResp.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecurityResp.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecuritySignReq.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecuritySignReq.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecuritySignResp.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/security/entity/SecuritySignResp.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/ObjectParseUtil$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/ObjectParseUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/ObjectParseUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/QueryRuleEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/QueryRuleEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/QueryRuleVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/common/util/superSearch/QueryRuleVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/AutoPoiConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/AutoPoiConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/AutoPoiDictConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/AutoPoiDictConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/CorsFilterCondition.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/CorsFilterCondition.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/DruidConfig$RemoveAdFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/DruidConfig$RemoveAdFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/DruidConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/DruidConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/JeecgCloudCondition.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/JeecgCloudCondition.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/RestTemplateConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/RestTemplateConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/StaticConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/StaticConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/Swagger2Config.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/Swagger2Config.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/WebMvcConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/WebMvcConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/WebSocketConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/WebSocketConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisPlusSaasConfig$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisPlusSaasConfig$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisPlusSaasConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/MybatisPlusSaasConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/TenantContext.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/mybatis/TenantContext.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/oss/MinioConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/oss/MinioConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/oss/OssConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/oss/OssConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/JwtToken.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/JwtToken.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/ShiroConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/ShiroConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/ShiroRealm.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/ShiroRealm.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/CustomShiroFilterFactoryBean$MySpringShiroFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/CustomShiroFilterFactoryBean$MySpringShiroFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/CustomShiroFilterFactoryBean.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/CustomShiroFilterFactoryBean.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/JwtFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/JwtFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/ResourceCheckFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/shiro/filters/ResourceCheckFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/interceptor/SignAuthConfiguration.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/interceptor/SignAuthConfiguration.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/interceptor/SignAuthInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/interceptor/SignAuthInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/BodyReaderHttpServletRequestWrapper$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/BodyReaderHttpServletRequestWrapper$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/BodyReaderHttpServletRequestWrapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/BodyReaderHttpServletRequestWrapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/HttpUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/HttpUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/SignUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/sign/util/SignUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppTypeConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppTypeConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppTypeItemVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/config/thirdapp/ThirdAppTypeItemVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/mapper/BaseCommonMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/mapper/BaseCommonMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/service/BaseCommonService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/service/BaseCommonService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/service/impl/BaseCommonServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-core/target/classes/org/jeecg/modules/base/service/impl/BaseCommonServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/annotation/RabbitComponent.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/annotation/RabbitComponent.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/base/BaseMap.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/base/BaseMap.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/CommonConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/CommonConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/mqtoken/TransmitUserTokenFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/mqtoken/TransmitUserTokenFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/mqtoken/UserTokenContext.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/config/mqtoken/UserTokenContext.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/constant/CacheConstant.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/constant/CacheConstant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/constant/GlobalConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/constant/GlobalConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/client/JeecgRedisClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/client/JeecgRedisClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/config/RedisConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/config/RedisConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/listener/JeecgRedisListerer.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/listener/JeecgRedisListerer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/receiver/RedisReceiver.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/receiver/RedisReceiver.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/writer/JeecgRedisCacheWriter.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/modules/redis/writer/JeecgRedisCacheWriter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/util/RedisUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/util/RedisUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/util/SpringContextHolder.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-base/jeecg-boot-base-tools/target/classes/org/jeecg/common/util/SpringContextHolder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/api/controller/AigcApiController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/api/controller/AigcApiController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/api/mapper/AicgOnlineUsersMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/api/mapper/AicgOnlineUsersMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/plubshop/controller/AigcPlubShopController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/plubshop/controller/AigcPlubShopController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/plubshop/entity/AigcPlubShop.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/plubshop/entity/AigcPlubShop.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/userprofile/entity/AicgUserProfile.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/userprofile/entity/AicgUserProfile.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/userprofile/mapper/xml/AicgUserProfileMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/demo/userprofile/mapper/xml/AicgUserProfileMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/TosService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/TosService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/message/websocket/WebSocket.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/message/websocket/WebSocket.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/controller/LoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/controller/LoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/controller/SysUserOnlineController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/controller/SysUserOnlineController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-test.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application-test.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/templates/xiaohongshu-share-template.html" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/templates/xiaohongshu-share-template.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-test.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application-test.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneGUI.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneGUI.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneToMainUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgOneToMainUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgSystemApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/JeecgSystemApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/CodeGenerateDbConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/CodeGenerateDbConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/SystemInitListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/SystemInitListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/init/TomcatFactoryConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/jimureport/JimuReportTokenService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/config/jimureport/JimuReportTokenService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$FileStorage.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$FileStorage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$HtmlSecurity.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig$HtmlSecurity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/config/AigcApiConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController$ImageTransferResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController$ImageTransferResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/AigcApiController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/CozeVideoController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/CozeVideoController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/PriceCalculationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/PriceCalculationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/SystemAPIController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/controller/SystemAPIController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/dto/PluginVerifyResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/dto/PluginVerifyResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgApiLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgApiLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgOnlineUsers.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/entity/AicgOnlineUsers.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/CozeVideoExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/CozeVideoExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException$ErrorCodes.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException$ErrorCodes.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/exception/VideoGenerationException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgApiLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgApiLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgOnlineUsersMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/mapper/AicgOnlineUsersMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/DouBaoVideoApiService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/DouBaoVideoApiService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/IAigcApiService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/IAigcApiService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/VideoGenerationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/VideoGenerationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/impl/AigcApiServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/service/impl/AigcApiServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/QRCodeUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/QRCodeUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/SecurityUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/SecurityUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/VideoParameterValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/api/util/VideoParameterValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/controller/CasClientController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/controller/CasClientController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/CASServiceUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$CustomAttributeHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils$CustomAttributeHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/cas/util/XmlUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/controller/AicgUserApiUsageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/controller/AicgUserApiUsageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/entity/AicgUserApiUsage.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/entity/AicgUserApiUsage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/mapper/AicgUserApiUsageMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/mapper/AicgUserApiUsageMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/IAicgUserApiUsageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/IAicgUserApiUsageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/impl/AicgUserApiUsageServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/apiusage/service/impl/AicgUserApiUsageServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/controller/AicgExchangeCodeController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/controller/AicgExchangeCodeController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/entity/AicgExchangeCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/entity/AicgExchangeCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/mapper/AicgExchangeCodeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/mapper/AicgExchangeCodeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/IAicgExchangeCodeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/IAicgExchangeCodeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/impl/AicgExchangeCodeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/exchangecode/service/impl/AicgExchangeCodeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/controller/AigcHomeCarouselController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/controller/AigcHomeCarouselController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/entity/AigcHomeCarousel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/entity/AigcHomeCarousel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/mapper/AigcHomeCarouselMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/mapper/AigcHomeCarouselMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/IAigcHomeCarouselService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/IAigcHomeCarouselService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/impl/AigcHomeCarouselServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/homecarousel/service/impl/AigcHomeCarouselServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/controller/AicgUserMembershipHistoryController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/controller/AicgUserMembershipHistoryController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/entity/AicgUserMembershipHistory.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/entity/AicgUserMembershipHistory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/mapper/AicgUserMembershipHistoryMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/mapper/AicgUserMembershipHistoryMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/IAicgUserMembershipHistoryService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/IAicgUserMembershipHistoryService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/impl/AicgUserMembershipHistoryServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/membershiphistory/service/impl/AicgUserMembershipHistoryServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/controller/AicgUserNotificationController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/controller/AicgUserNotificationController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/entity/AicgUserNotification.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/entity/AicgUserNotification.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/mapper/AicgUserNotificationMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/mapper/AicgUserNotificationMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/IAicgUserNotificationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/IAicgUserNotificationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/impl/AicgUserNotificationServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/notification/service/impl/AicgUserNotificationServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/controller/AigcPlubAuthorController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/controller/AigcPlubAuthorController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/entity/AigcPlubAuthor.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/entity/AigcPlubAuthor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/mapper/AigcPlubAuthorMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/mapper/AigcPlubAuthorMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/IAigcPlubAuthorService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/IAigcPlubAuthorService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/impl/AigcPlubAuthorServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubauthor/service/impl/AigcPlubAuthorServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/controller/AigcPlubShopController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/controller/AigcPlubShopController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/entity/AigcPlubShop.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/entity/AigcPlubShop.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/mapper/AigcPlubShopMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/mapper/AigcPlubShopMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/IAigcPlubShopService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/IAigcPlubShopService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/impl/AigcPlubShopServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/plubshop/service/impl/AigcPlubShopServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/controller/AicgUserReferralController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/controller/AicgUserReferralController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/entity/AicgUserReferral.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/entity/AicgUserReferral.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/mapper/AicgUserReferralMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/mapper/AicgUserReferralMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/IAicgUserReferralService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/IAicgUserReferralService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/impl/AicgUserReferralServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referral/service/impl/AicgUserReferralServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/controller/AicgUserReferralRewardController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/controller/AicgUserReferralRewardController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/entity/AicgUserReferralReward.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/entity/AicgUserReferralReward.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/mapper/AicgUserReferralRewardMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/mapper/AicgUserReferralRewardMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/IAicgUserReferralRewardService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/IAicgUserReferralRewardService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/impl/AicgUserReferralRewardServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/referralreward/service/impl/AicgUserReferralRewardServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/usercenter/controller/UserCenterDataController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/controller/AicgUserProfileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/controller/AicgUserProfileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/entity/AicgUserProfile.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/entity/AicgUserProfile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/mapper/AicgUserProfileMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/mapper/AicgUserProfileMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/mapper/xml/AicgUserProfileMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/mapper/xml/AicgUserProfileMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/IAicgUserProfileService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/IAicgUserProfileService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/impl/AicgUserProfileServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userprofile/service/impl/AicgUserProfileServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/controller/AicgUserRecordController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/controller/AicgUserRecordController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/entity/AicgUserRecord.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/entity/AicgUserRecord.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/mapper/AicgUserRecordMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/mapper/AicgUserRecordMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/IAicgUserRecordService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/IAicgUserRecordService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/impl/AicgUserRecordServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/userrecord/service/impl/AicgUserRecordServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/controller/AigcVersionControlController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/controller/AigcVersionControlController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/dto/PublicVersionInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/dto/PublicVersionInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/entity/AigcVersionControl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/entity/AigcVersionControl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/mapper/AigcVersionControlMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/mapper/AigcVersionControlMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/IAigcVersionControlService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/IAigcVersionControlService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/impl/AigcVersionControlServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/versioncontrol/service/impl/AigcVersionControlServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/controller/AigcVideoTeacherController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/controller/AigcVideoTeacherController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/entity/AigcVideoTeacher.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/entity/AigcVideoTeacher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/mapper/AigcVideoTeacherMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/mapper/AigcVideoTeacherMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/IAigcVideoTeacherService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/IAigcVideoTeacherService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/impl/AigcVideoTeacherServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videoteacher/service/impl/AigcVideoTeacherServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/controller/AigcVideoTutorialController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/controller/AigcVideoTutorialController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/entity/AigcVideoTutorial.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/entity/AigcVideoTutorial.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/mapper/AigcVideoTutorialMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/mapper/AigcVideoTutorialMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/IAigcVideoTutorialService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/IAigcVideoTutorialService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/impl/AigcVideoTutorialServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/videotutorial/service/impl/AigcVideoTutorialServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/controller/AigcWebsiteFeaturesController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/controller/AigcWebsiteFeaturesController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/entity/AigcWebsiteFeatures.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/entity/AigcWebsiteFeatures.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/mapper/AigcWebsiteFeaturesMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/mapper/AigcWebsiteFeaturesMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/IAigcWebsiteFeaturesService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/IAigcWebsiteFeaturesService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/impl/AigcWebsiteFeaturesServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitefeatures/service/impl/AigcWebsiteFeaturesServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/controller/AigcWebsiteStatsController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/controller/AigcWebsiteStatsController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/entity/AigcWebsiteStats.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/entity/AigcWebsiteStats.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/mapper/AigcWebsiteStatsMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/mapper/AigcWebsiteStatsMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/IAigcWebsiteStatsService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/IAigcWebsiteStatsService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/impl/AigcWebsiteStatsServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/demo/websitestats/service/impl/AigcWebsiteStatsServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessKeyAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/aspect/JianyingAccessKeyAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/JianyingWebConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/JianyingWebConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig$Internal.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig$Internal.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/config/TosConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingDataboxController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingDataboxController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingToolboxController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/controller/JianyingToolboxController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddAudiosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddAudiosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddCaptionsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddCaptionsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddEffectsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddEffectsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddImagesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddImagesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddKeyframesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddKeyframesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddMasksRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddMasksRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddStickerRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddStickerRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddTextStyleRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddTextStyleRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddVideosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AddVideosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AsrTimelinesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AsrTimelinesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioDownloadResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioDownloadResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingSummary.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioProcessingSummary.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioTimelinesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/AudioTimelinesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BaseJianyingRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BaseJianyingRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BgmSearchRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/BgmSearchRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CaptionInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CaptionInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CreateDraftRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/CreateDraftRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EasyCreateMaterialRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EasyCreateMaterialRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo$FileUrlInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo$FileUrlInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/EffectInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoStatusRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GenVideoStatusRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetAudioDurationRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetAudioDurationRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetImageAnimationsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetImageAnimationsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetTextAnimationsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetTextAnimationsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetUrlRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/GetUrlRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ImgsInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ImgsInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/KeyframesInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/KeyframesInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ObjsToStrListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/ObjsToStrListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SaveDraftRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SaveDraftRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SearchStickerRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SearchStickerRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SoundEffectsSearchRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/SoundEffectsSearchRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrListToObjsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrListToObjsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrToListRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/StrToListRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/TimelinesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/TimelinesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/VideoInfosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/dto/VideoInfosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingParameterException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/exception/JianyingParameterException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/interceptor/JianyingApiInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/interceptor/JianyingApiInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/ApiKeyVerificationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftConfigGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftConfigGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftContentGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftContentGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftPackageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/DraftPackageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$23.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$23.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$24.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$25.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$25.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$26.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$26.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$27.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$27.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$28.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$28.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$29.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$29.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$30.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$30.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$31.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$32.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$32.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$33.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$33.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$34.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$34.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$35.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$35.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$36.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$36.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$37.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$37.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$MaskInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$MaskInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$VideoMaterialResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService$VideoMaterialResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingAssistantService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService$CacheEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService$CacheEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingEffectSearchService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$AnimationInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$AnimationInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionCacheEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionCacheEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService$TransitionInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingIdResolverService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService$MaskInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService$MaskInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingMaskSearchService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/TosService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/TosService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/task/TosCleanupTask.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/task/TosCleanupTask.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/validator/JianyingAccessValidator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/validator/JianyingAccessValidator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/config/JianyingProConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/config/JianyingProConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/controller/JianyingProController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/controller/JianyingProController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/BaseJianyingProRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/BaseJianyingProRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddAudiosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddAudiosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddCaptionsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddCaptionsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddEffectsRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddEffectsRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddImagesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddImagesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddKeyframesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddKeyframesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddVideosRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProAddVideosRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProDataConversionRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProTimelinesRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/request/JianyingProTimelinesRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/BaseJianyingProResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/BaseJianyingProResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddAudiosResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddAudiosResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddVideosResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/dto/response/JianyingProAddVideosResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/enums/JianyingProErrorCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/enums/JianyingProErrorCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProBusinessException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProBusinessException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProGlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/exception/JianyingProGlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/JianyingProService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/JianyingProService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$23.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$23.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$25.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$25.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$26.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$26.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$27.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$27.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$28.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$28.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$29.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$29.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$30.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$30.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$32.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$32.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$33.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$33.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$35.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$35.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$36.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$36.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$37.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$37.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$38.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$38.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$39.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$39.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$40.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$40.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$41.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$41.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$KeywordRange.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$KeywordRange.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$MaskInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$MaskInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$23.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$23.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$24.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$24.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$25.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$25.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$27.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$27.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$28.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$28.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$29.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$29.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$30.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$30.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$31.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$31.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$32.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$32.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$33.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$33.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$34.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$34.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$35.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$35.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$36.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$36.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$37.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$37.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$MaskInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$MaskInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$VideoMaterialResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$VideoMaterialResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProCozeApiService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProCozeApiService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDraftContentGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDraftContentGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService$CacheEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService$CacheEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProEffectSearchService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$AnimationInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$AnimationInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionCacheEntry.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionCacheEntry.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService$TransitionInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProIdResolverService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProTosService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProTosService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/util/JianyingProResponseUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/util/JianyingProResponseUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageTemplateController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/controller/SysMessageTemplateController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/MsgParams.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/MsgParams.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessage.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessageTemplate.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/entity/SysMessageTemplate.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/ISendMsgHandle.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/ISendMsgHandle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgStatusEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgStatusEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgTypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/enums/SendMsgTypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/EmailSendMsgHandle.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/EmailSendMsgHandle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/SmsSendMsgHandle.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/SmsSendMsgHandle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/WxSendMsgHandle.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/handle/impl/WxSendMsgHandle.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/job/SendMsgJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/job/SendMsgJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageTemplateMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/mapper/SysMessageTemplateMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageTemplateService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/ISysMessageTemplateService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageTemplateServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/service/impl/SysMessageTemplateServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/util/PushMsgUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/util/PushMsgUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/SocketHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/SocketHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/TestSocketController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/TestSocketController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/WebSocket.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/message/websocket/WebSocket.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/controller/ActuatorRedisController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/controller/ActuatorRedisController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/domain/RedisInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/domain/RedisInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/exception/RedisConnectException.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/exception/RedisConnectException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/RedisService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/RedisService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/MailHealthIndicator.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/MailHealthIndicator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/RedisServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/monitor/service/impl/RedisServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/aop/LogRecordAspect.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/aop/LogRecordAspect.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/controller/NgAlainController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/controller/NgAlainController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/NgAlainService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/NgAlainService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/impl/NgAlainServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/ngalain/service/impl/NgAlainServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/controller/OSSFileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/controller/OSSFileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/entity/OSSFile.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/entity/OSSFile.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/mapper/OSSFileMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/mapper/OSSFileMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/IOSSFileService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/IOSSFileService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/impl/OSSFileServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/oss/service/impl/OSSFileServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/controller/QuartzJobController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/controller/QuartzJobController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/entity/QuartzJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/entity/QuartzJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/AsyncJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/AsyncJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleParamJob.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/job/SampleParamJob.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/mapper/QuartzJobMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/mapper/QuartzJobMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/IQuartzJobService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/IQuartzJobService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/impl/QuartzJobServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/quartz/service/impl/QuartzJobServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$InviteCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$InviteCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Password.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Password.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Security.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$Security.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Captcha.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Captcha.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Email.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Email.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Sms.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode$Sms.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig$VerifyCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/RegisterConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/SmsConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/config/SmsConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CacheManagementController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CacheManagementController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CommonController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/CommonController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/DuplicateCheckController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/DuplicateCheckController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/LoginController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/LoginController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementSendController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysAnnouncementSendController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCategoryController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCategoryController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCheckRuleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysCheckRuleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataSourceController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDataSourceController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartPermissionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartPermissionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartRoleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDepartRoleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictItemController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysDictItemController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysFillRuleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysFillRuleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysGatewayRouteController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysGatewayRouteController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysLogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysLogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPermissionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPermissionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPositionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysPositionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysRoleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysRoleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysSensitiveWordController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysSensitiveWordController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysTenantController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysTenantController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUploadController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUploadController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserAgentController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserAgentController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserOnlineController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/SysUserOnlineController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdAppController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdAppController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdLoginController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/ThirdLoginController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserCenterController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserCenterController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserRegisterController.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/controller/UserRegisterController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO$WechatInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO$WechatInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/dto/RegisterDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgVerifyCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgVerifyCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgWechatTemp.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AicgWechatTemp.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AigcDesktopDownloadLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/AigcDesktopDownloadLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncement.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncement.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncementSend.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysAnnouncementSend.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCategory.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCategory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCheckRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysCheckRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataSource.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDataSource.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepart.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepart.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartPermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartPermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRole.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRole.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRolePermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRolePermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRoleUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDepartRoleUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDict.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDict.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDictItem.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysDictItem.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysFillRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysFillRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysGatewayRoute.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysGatewayRoute.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermissionDataRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPermissionDataRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPosition.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysPosition.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRole.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRole.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRolePermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysRolePermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWord.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWord.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWordHitLog.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysSensitiveWordHitLog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysTenant.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysTenant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysThirdAccount.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysThirdAccount.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserAgent.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserAgent.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserDepart.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserDepart.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserRole.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/SysUserRole.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeErrorType.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeErrorType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/entity/VerifyCodeResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgVerifyCodeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgVerifyCodeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgWechatTempMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AicgWechatTempMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AigcDesktopDownloadLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/AigcDesktopDownloadLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementSendMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysAnnouncementSendMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCategoryMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCategoryMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCheckRuleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysCheckRuleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataSourceMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDataSourceMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartPermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartPermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRolePermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRolePermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleUserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDepartRoleUserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictItemMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictItemMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysDictMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysFillRuleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysFillRuleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysGatewayRouteMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysGatewayRouteMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionDataRuleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionDataRuleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPositionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysPositionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRolePermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysRolePermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordHitLogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordHitLogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysSensitiveWordMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysTenantMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysTenantMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysThirdAccountMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysThirdAccountMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserAgentMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserAgentMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserDepartMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserDepartMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserRoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/mapper/SysUserRoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/AnnouncementSendModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/AnnouncementSendModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DepartIdModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DepartIdModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DuplicateCheckVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/DuplicateCheckVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDepartTreeModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDepartTreeModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDictTree.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysDictTree.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysLoginModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysLoginModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysPermissionTree.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysPermissionTree.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysUserSysDepartModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/SysUserSysDepartModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/ThirdLoginModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/ThirdLoginModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeSelectModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/model/TreeSelectModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/CategoryCodeRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/CategoryCodeRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrderNumberRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrderNumberRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrgCodeRule.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/rule/OrgCodeRule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAicgVerifyCodeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAicgVerifyCodeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAigcDesktopDownloadLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IAigcDesktopDownloadLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService$NicknameValidationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService$NicknameValidationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISensitiveWordService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementSendService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementSendService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysAnnouncementService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCategoryService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCategoryService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCheckRuleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysCheckRuleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataSourceService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDataSourceService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartPermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartPermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRolePermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRolePermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleUserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartRoleUserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDepartService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictItemService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictItemService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysDictService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysFillRuleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysFillRuleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysGatewayRouteService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysGatewayRouteService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionDataRuleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionDataRuleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPositionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysPositionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRolePermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRolePermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysRoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordHitLogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordHitLogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$ImportResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$ImportResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$SensitiveWordCheckResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService$SensitiveWordCheckResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysSensitiveWordService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysTenantService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysTenantService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysThirdAccountService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysThirdAccountService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserAgentService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserAgentService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserDepartService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserDepartService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserRoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserRoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/ISysUserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IThirdAppService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IThirdAppService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IUserRegisterService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/IUserRegisterService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/UserCacheCleanupService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/UserCacheCleanupService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AicgVerifyCodeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AicgVerifyCodeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AigcDesktopDownloadLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/AigcDesktopDownloadLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ImportFileServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ImportFileServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SensitiveWordServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SensitiveWordServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementSendServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementSendServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysAnnouncementServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysBaseApiImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysBaseApiImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCategoryServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCategoryServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCheckRuleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysCheckRuleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataSourceServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDataSourceServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartPermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartPermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRolePermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRolePermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleUserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartRoleUserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDepartServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictItemServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictItemServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysDictServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysFillRuleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysFillRuleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysGatewayRouteServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysGatewayRouteServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionDataRuleImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionDataRuleImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPositionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysPositionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRolePermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRolePermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysRoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordHitLogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordHitLogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysSensitiveWordServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysTenantServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysTenantServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysThirdAccountServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysThirdAccountServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserAgentServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserAgentServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserDepartServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserDepartServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserRoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserRoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/SysUserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppDingtalkServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppDingtalkServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppWechatEnterpriseServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/ThirdAppWechatEnterpriseServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/UserRegisterServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/desform/SysTranslateAPIImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/service/impl/desform/SysTranslateAPIImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/AnnouncementManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/AnnouncementManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/FindsDepartsChildrenUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/FindsDepartsChildrenUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LoginConflictChecker.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LoginConflictChecker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier$CacheVerificationResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier$CacheVerificationResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/LogoutCacheVerifier.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/PermissionDataUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/PermissionDataUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RandImageUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RandImageUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RoleChecker.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/RoleChecker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SecurityUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SecurityUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SingleLoginManager.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/SingleLoginManager.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/XSSUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/util/XSSUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/LoginConflictResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/LoginConflictResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDepartUsersVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDepartUsersVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDictPage.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysDictPage.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysOnlineVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysOnlineVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserDepVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserDepVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserOnlineVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserOnlineVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserRoleVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/SysUserRoleVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JdtDepartmentTreeVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JdtDepartmentTreeVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JwDepartmentTreeVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/JwDepartmentTreeVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/SyncInfoVo.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/system/vo/thirdapp/SyncInfoVo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/templates/xiaohongshu-share-template.html" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/templates/xiaohongshu-share-template.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-starter/jeecg-boot-starter-cloud/nacos/jeecg.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-starter/jeecg-boot-starter-cloud/nacos/jeecg.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/error-log.html" beforeDir="false" afterPath="$PROJECT_DIR$/logs/error-log.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2ySsI0hrVVD7Z6hSzII5gP8jPBf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.jeecg-boot-module-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-module-system [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JeecgSystemApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/AigcView/AigcViewRd&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="JeecgSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jeecg-boot-module-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.jeecg.JeecgSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="" />
      <created>1749839225426</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749839225426</updated>
      <workItem from="1749839226610" duration="2126000" />
      <workItem from="1749846796291" duration="37818000" />
      <workItem from="1750127497474" duration="664000" />
      <workItem from="1750147622680" duration="8219000" />
      <workItem from="1750182992775" duration="828000" />
      <workItem from="1750219207386" duration="34463000" />
      <workItem from="1750395165498" duration="16469000" />
      <workItem from="1750481146931" duration="18858000" />
      <workItem from="1750576054427" duration="13926000" />
      <workItem from="1750662956240" duration="24712000" />
      <workItem from="1750844199724" duration="10000" />
      <workItem from="1750924195138" duration="28172000" />
      <workItem from="1750964802742" duration="9819000" />
      <workItem from="1750999295568" duration="9206000" />
      <workItem from="1751010645542" duration="31817000" />
      <workItem from="1751184251064" duration="3114000" />
      <workItem from="1751308217715" duration="5256000" />
      <workItem from="1751366071895" duration="82680000" />
      <workItem from="1751786686767" duration="103917000" />
      <workItem from="1752333468590" duration="3861000" />
      <workItem from="1752422504660" duration="9302000" />
      <workItem from="1752506359093" duration="3126000" />
      <workItem from="1752633991731" duration="11995000" />
      <workItem from="1752716420676" duration="1695000" />
      <workItem from="1752752996018" duration="4905000" />
      <workItem from="1752833179798" duration="17286000" />
      <workItem from="1752909924462" duration="2263000" />
      <workItem from="1753102236494" duration="60366000" />
      <workItem from="1753502491755" duration="4485000" />
      <workItem from="1753638631113" duration="23609000" />
      <workItem from="1753752194234" duration="28883000" />
      <workItem from="1753845114170" duration="23417000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>