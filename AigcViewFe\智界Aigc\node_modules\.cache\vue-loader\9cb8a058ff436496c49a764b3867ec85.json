{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue?vue&type=template&id=3d278fe4&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue", "mtime": 1754041473614}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"agent-card\" @click=\"handleCardClick\">\n  <!-- SVIP推广标签 -->\n  <div v-if=\"agent.showSvipPromo && agent.authorType === '1'\" class=\"svip-promo-tag svip-free\">\n    SVIP免费\n  </div>\n  <div v-if=\"agent.showSvipPromo && agent.authorType === '2'\" class=\"svip-promo-tag svip-discount\">\n    SVIP 5折\n  </div>\n\n  <!-- 已购买标签 -->\n  <div v-if=\"agent.isPurchased\" class=\"purchased-tag\">\n    <a-icon type=\"check-circle\" />\n    <span>已购买</span>\n  </div>\n\n  <!-- 智能体封面 -->\n  <div class=\"agent-cover\">\n    <div class=\"cover-image\">\n      <!-- 如果有视频，显示视频 -->\n      <video\n        v-if=\"agent.demoVideo\"\n        :src=\"agent.demoVideo\"\n        muted\n        loop\n        preload=\"metadata\"\n        @mouseenter=\"handleVideoHover\"\n        @mouseleave=\"handleVideoLeave\"\n        @loadedmetadata=\"handleVideoLoaded\"\n        class=\"cover-video\"\n      ></video>\n      <!-- 如果没有视频，显示图片 -->\n      <img\n        v-else-if=\"agent.agentAvatar\"\n        :src=\"agent.agentAvatar\"\n        :alt=\"agent.agentName\"\n        @error=\"handleImageError\"\n        class=\"cover-image-img\"\n      />\n      <!-- 默认占位符 -->\n      <div v-else class=\"cover-placeholder\">\n        <a-icon type=\"robot\" />\n      </div>\n    </div>\n\n    <!-- 作者类型标签 -->\n    <div class=\"author-type-tag\" :class=\"authorTypeClass\">\n      <a-icon :type=\"authorTypeIcon\" />\n      <span>{{ authorTypeText }}</span>\n    </div>\n\n    <!-- VIP折扣标签 -->\n    <div v-if=\"agent.showDiscountPrice && agent.discountRate === 0.7\" class=\"vip-discount-tag\">\n      <a-icon type=\"crown\" />\n      <span>VIP 7折</span>\n    </div>\n\n    <!-- SVIP免费标签 -->\n    <div v-if=\"agent.isFree\" class=\"svip-free-tag\">\n      <a-icon type=\"crown\" />\n      <span>SVIP 免费</span>\n    </div>\n\n    <!-- SVIP折扣标签 -->\n    <div v-else-if=\"agent.showDiscountPrice && agent.discountRate === 0.5\" class=\"svip-discount-tag\">\n      <a-icon type=\"crown\" />\n      <span>SVIP 5折</span>\n    </div>\n\n  </div>\n\n  <!-- 智能体信息 -->\n  <div class=\"agent-info\">\n    <div class=\"agent-header\">\n      <h4 class=\"agent-name\" :title=\"agent.agentName\">\n        {{ agent.agentName }}\n      </h4>\n      <div class=\"agent-price\">\n        <!-- 显示免费 -->\n        <div v-if=\"agent.isFree\" class=\"price-container\">\n          <span class=\"free-price\">免费</span>\n        </div>\n        <!-- 显示折扣价格 -->\n        <div v-else-if=\"agent.showDiscountPrice\" class=\"price-container\">\n          <span class=\"discount-price\">¥{{ Math.round((agent.originalPrice || 0) * agent.discountRate) }}</span>\n          <span class=\"original-price\">¥{{ agent.originalPrice || 0 }}</span>\n        </div>\n        <!-- 显示原价 -->\n        <div v-else class=\"price-container\">\n          <span class=\"current-price\">¥{{ agent.originalPrice || 0 }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"agent-description\" v-if=\"agent.description\">\n      {{ agent.description }}\n    </div>\n\n    <div class=\"agent-meta\">\n      <span class=\"creator-info\">\n        <div class=\"creator-avatar\">\n          <img\n            v-if=\"creatorAvatar\"\n            :src=\"creatorAvatar\"\n            :alt=\"creatorName\"\n            @error=\"handleCreatorAvatarError\"\n          />\n          <a-icon v-else type=\"user\" />\n        </div>\n        <span class=\"creator-name\">{{ creatorName }}</span>\n      </span>\n      <span class=\"workflow-count\">\n        <a-icon type=\"deployment-unit\" />\n        {{ workflowCount }}个工作流\n      </span>\n    </div>\n  </div>\n</div>\n", null]}