{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentCard.vue", "mtime": 1754041473614}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'AgentCard',\n  props: {\n    agent: {\n      type: Object,\n      required: true\n    }\n  },\n  computed: {\n\n    // 作者类型样式类\n    authorTypeClass() {\n      return {\n        'official': this.agent.authorType === '1',\n        'creator': this.agent.authorType === '2'\n      }\n    },\n\n    // 作者类型图标\n    authorTypeIcon() {\n      return this.agent.authorType === '1' ? 'crown' : 'user'\n    },\n\n    // 作者类型文本\n    authorTypeText() {\n      return this.agent.authorType === '1' ? '官方' : '创作者'\n    },\n\n    // 价格标签样式类\n    priceTagClass() {\n      return {\n        'free': this.agent.isFree,\n        'discount': this.agent.hasDiscount && !this.agent.isFree,\n        'normal': !this.agent.hasDiscount && !this.agent.isFree\n      }\n    },\n\n    // 创作者名称\n    creatorName() {\n      return this.agent.creatorName || this.agent.createBy || '未知创作者'\n    },\n\n    // 创作者头像\n    creatorAvatar() {\n      return this.agent.creatorAvatar || ''\n    },\n\n    // 工作流数量\n    workflowCount() {\n      return this.agent.workflowCount || 0\n    }\n  },\n  methods: {\n    // 处理卡片点击\n    handleCardClick() {\n      this.$emit('view-detail', this.agent)\n    },\n\n    // 处理查看详情\n    handleViewDetail() {\n      this.$emit('view-detail', this.agent)\n    },\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      console.warn('智能体封面加载失败:', this.agent.agentName)\n      // 隐藏错误的图片，显示占位符\n      event.target.style.display = 'none'\n    },\n\n    // 处理创作者头像加载错误\n    handleCreatorAvatarError(event) {\n      console.warn('创作者头像加载失败:', this.creatorName)\n      // 隐藏错误的头像，显示默认图标\n      event.target.style.display = 'none'\n    },\n\n    // 视频悬停播放\n    handleVideoHover(event) {\n      event.target.play().catch(err => {\n        console.warn('视频播放失败:', err)\n      })\n    },\n\n    // 视频离开暂停\n    handleVideoLeave(event) {\n      event.target.pause()\n      event.target.currentTime = 0\n    },\n\n    // 视频元数据加载完成，设置为第一帧\n    handleVideoLoaded(event) {\n      // 设置视频到第一帧作为封面\n      event.target.currentTime = 0\n    }\n  }\n}\n", {"version": 3, "sources": ["AgentCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AgentCard.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <div class=\"agent-card\" @click=\"handleCardClick\">\n    <!-- SVIP推广标签 -->\n    <div v-if=\"agent.showSvipPromo && agent.authorType === '1'\" class=\"svip-promo-tag svip-free\">\n      SVIP免费\n    </div>\n    <div v-if=\"agent.showSvipPromo && agent.authorType === '2'\" class=\"svip-promo-tag svip-discount\">\n      SVIP 5折\n    </div>\n\n    <!-- 已购买标签 -->\n    <div v-if=\"agent.isPurchased\" class=\"purchased-tag\">\n      <a-icon type=\"check-circle\" />\n      <span>已购买</span>\n    </div>\n\n    <!-- 智能体封面 -->\n    <div class=\"agent-cover\">\n      <div class=\"cover-image\">\n        <!-- 如果有视频，显示视频 -->\n        <video\n          v-if=\"agent.demoVideo\"\n          :src=\"agent.demoVideo\"\n          muted\n          loop\n          preload=\"metadata\"\n          @mouseenter=\"handleVideoHover\"\n          @mouseleave=\"handleVideoLeave\"\n          @loadedmetadata=\"handleVideoLoaded\"\n          class=\"cover-video\"\n        ></video>\n        <!-- 如果没有视频，显示图片 -->\n        <img\n          v-else-if=\"agent.agentAvatar\"\n          :src=\"agent.agentAvatar\"\n          :alt=\"agent.agentName\"\n          @error=\"handleImageError\"\n          class=\"cover-image-img\"\n        />\n        <!-- 默认占位符 -->\n        <div v-else class=\"cover-placeholder\">\n          <a-icon type=\"robot\" />\n        </div>\n      </div>\n\n      <!-- 作者类型标签 -->\n      <div class=\"author-type-tag\" :class=\"authorTypeClass\">\n        <a-icon :type=\"authorTypeIcon\" />\n        <span>{{ authorTypeText }}</span>\n      </div>\n\n      <!-- VIP折扣标签 -->\n      <div v-if=\"agent.showDiscountPrice && agent.discountRate === 0.7\" class=\"vip-discount-tag\">\n        <a-icon type=\"crown\" />\n        <span>VIP 7折</span>\n      </div>\n\n      <!-- SVIP免费标签 -->\n      <div v-if=\"agent.isFree\" class=\"svip-free-tag\">\n        <a-icon type=\"crown\" />\n        <span>SVIP 免费</span>\n      </div>\n\n      <!-- SVIP折扣标签 -->\n      <div v-else-if=\"agent.showDiscountPrice && agent.discountRate === 0.5\" class=\"svip-discount-tag\">\n        <a-icon type=\"crown\" />\n        <span>SVIP 5折</span>\n      </div>\n\n    </div>\n\n    <!-- 智能体信息 -->\n    <div class=\"agent-info\">\n      <div class=\"agent-header\">\n        <h4 class=\"agent-name\" :title=\"agent.agentName\">\n          {{ agent.agentName }}\n        </h4>\n        <div class=\"agent-price\">\n          <!-- 显示免费 -->\n          <div v-if=\"agent.isFree\" class=\"price-container\">\n            <span class=\"free-price\">免费</span>\n          </div>\n          <!-- 显示折扣价格 -->\n          <div v-else-if=\"agent.showDiscountPrice\" class=\"price-container\">\n            <span class=\"discount-price\">¥{{ Math.round((agent.originalPrice || 0) * agent.discountRate) }}</span>\n            <span class=\"original-price\">¥{{ agent.originalPrice || 0 }}</span>\n          </div>\n          <!-- 显示原价 -->\n          <div v-else class=\"price-container\">\n            <span class=\"current-price\">¥{{ agent.originalPrice || 0 }}</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"agent-description\" v-if=\"agent.description\">\n        {{ agent.description }}\n      </div>\n\n      <div class=\"agent-meta\">\n        <span class=\"creator-info\">\n          <div class=\"creator-avatar\">\n            <img\n              v-if=\"creatorAvatar\"\n              :src=\"creatorAvatar\"\n              :alt=\"creatorName\"\n              @error=\"handleCreatorAvatarError\"\n            />\n            <a-icon v-else type=\"user\" />\n          </div>\n          <span class=\"creator-name\">{{ creatorName }}</span>\n        </span>\n        <span class=\"workflow-count\">\n          <a-icon type=\"deployment-unit\" />\n          {{ workflowCount }}个工作流\n        </span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AgentCard',\n  props: {\n    agent: {\n      type: Object,\n      required: true\n    }\n  },\n  computed: {\n\n    // 作者类型样式类\n    authorTypeClass() {\n      return {\n        'official': this.agent.authorType === '1',\n        'creator': this.agent.authorType === '2'\n      }\n    },\n\n    // 作者类型图标\n    authorTypeIcon() {\n      return this.agent.authorType === '1' ? 'crown' : 'user'\n    },\n\n    // 作者类型文本\n    authorTypeText() {\n      return this.agent.authorType === '1' ? '官方' : '创作者'\n    },\n\n    // 价格标签样式类\n    priceTagClass() {\n      return {\n        'free': this.agent.isFree,\n        'discount': this.agent.hasDiscount && !this.agent.isFree,\n        'normal': !this.agent.hasDiscount && !this.agent.isFree\n      }\n    },\n\n    // 创作者名称\n    creatorName() {\n      return this.agent.creatorName || this.agent.createBy || '未知创作者'\n    },\n\n    // 创作者头像\n    creatorAvatar() {\n      return this.agent.creatorAvatar || ''\n    },\n\n    // 工作流数量\n    workflowCount() {\n      return this.agent.workflowCount || 0\n    }\n  },\n  methods: {\n    // 处理卡片点击\n    handleCardClick() {\n      this.$emit('view-detail', this.agent)\n    },\n\n    // 处理查看详情\n    handleViewDetail() {\n      this.$emit('view-detail', this.agent)\n    },\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      console.warn('智能体封面加载失败:', this.agent.agentName)\n      // 隐藏错误的图片，显示占位符\n      event.target.style.display = 'none'\n    },\n\n    // 处理创作者头像加载错误\n    handleCreatorAvatarError(event) {\n      console.warn('创作者头像加载失败:', this.creatorName)\n      // 隐藏错误的头像，显示默认图标\n      event.target.style.display = 'none'\n    },\n\n    // 视频悬停播放\n    handleVideoHover(event) {\n      event.target.play().catch(err => {\n        console.warn('视频播放失败:', err)\n      })\n    },\n\n    // 视频离开暂停\n    handleVideoLeave(event) {\n      event.target.pause()\n      event.target.currentTime = 0\n    },\n\n    // 视频元数据加载完成，设置为第一帧\n    handleVideoLoaded(event) {\n      // 设置视频到第一帧作为封面\n      event.target.currentTime = 0\n    }\n  }\n}\n</script>\n\n<style scoped>\n.agent-card {\n  background: white;\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  border: 1px solid #f1f5f9;\n  position: relative; /* 为SVIP标签提供定位基准 */\n}\n\n.agent-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);\n  border-color: rgba(59, 130, 246, 0.2);\n}\n\n/* 智能体封面 */\n.agent-cover {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n.cover-image {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.cover-image-img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.cover-video {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n  cursor: pointer;\n}\n\n.agent-card:hover .cover-image-img,\n.agent-card:hover .cover-video {\n  transform: scale(1.05);\n}\n\n.cover-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #94a3b8;\n  font-size: 3rem;\n  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\n}\n\n/* 作者类型标签 */\n.author-type-tag {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.author-type-tag.official {\n  background: rgba(59, 130, 246, 0.9);\n  color: white;\n}\n\n.author-type-tag.creator {\n  background: rgba(16, 185, 129, 0.9);\n  color: white;\n}\n\n/* VIP折扣标签 */\n.vip-discount-tag {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: linear-gradient(135deg, #7c3aed 0%, #6366f1 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.4);\n}\n\n/* SVIP免费标签 */\n.svip-free-tag {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);\n}\n\n/* SVIP折扣标签 */\n.svip-discount-tag {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);\n}\n\n\n\n.free-price {\n  font-size: 0.875rem;\n  color: #10b981;\n  font-weight: 600;\n}\n\n.promo-discount .discount-price {\n  font-size: 0.875rem;\n  color: #f59e0b;\n  font-weight: 600;\n}\n\n/* 智能体信息 */\n.agent-info {\n  padding: 1.5rem;\n}\n\n.agent-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.agent-name {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  flex: 1;\n}\n\n.agent-price {\n  margin-left: 1rem;\n  flex-shrink: 0;\n}\n\n.price-container {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.25rem;\n  min-height: 2.5rem; /* 固定高度确保对齐 */\n  justify-content: center; /* 垂直居中 */\n}\n\n.discount-price {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #dc2626;\n}\n\n.original-price {\n  font-size: 0.875rem;\n  font-weight: 400;\n  color: #9ca3af;\n  text-decoration: line-through;\n}\n\n.current-price {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #059669;\n}\n\n.free-price {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #8b5cf6;\n}\n\n.agent-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.875rem;\n  color: #64748b;\n  margin-top: 1rem;\n}\n\n.creator-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.creator-avatar {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: #f1f5f9;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.creator-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.creator-avatar .anticon {\n  font-size: 12px;\n  color: #94a3b8;\n}\n\n.creator-name {\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.workflow-count {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.agent-description {\n  font-size: 0.875rem;\n  color: #64748b;\n  line-height: 1.5;\n  margin-bottom: 1rem;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.agent-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.agent-stats {\n  display: flex;\n  gap: 1rem;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  font-size: 0.75rem;\n  color: #94a3b8;\n}\n\n.agent-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .agent-cover {\n    height: 160px;\n  }\n\n  .agent-info {\n    padding: 1rem;\n  }\n\n  .agent-name {\n    font-size: 1rem;\n  }\n\n  .agent-meta {\n    font-size: 0.8rem;\n  }\n\n  .agent-footer {\n    flex-direction: column;\n    gap: 0.75rem;\n    align-items: stretch;\n  }\n\n  .agent-stats {\n    justify-content: center;\n  }\n}\n\n/* SVIP推广标签 */\n.svip-promo-tag {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  z-index: 10;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: white;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: transform 0.2s ease;\n}\n\n.svip-promo-tag.svip-free {\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);\n}\n\n.svip-promo-tag.svip-discount {\n  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\n  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\n}\n\n.svip-promo-tag:hover {\n  transform: scale(1.05);\n}\n\n/* 已购买标签 */\n.purchased-tag {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  z-index: 10;\n  padding: 0.4rem 0.6rem;\n  border-radius: 6px;\n  font-size: 0.7rem;\n  font-weight: 600;\n  color: white;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.purchased-tag:hover {\n  transform: scale(1.05);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.5);\n}\n\n.purchased-tag .anticon {\n  font-size: 0.7rem;\n}\n</style>\n"]}]}