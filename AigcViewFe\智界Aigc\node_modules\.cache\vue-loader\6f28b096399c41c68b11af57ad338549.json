{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue?vue&type=style&index=0&id=409c6ff1&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\Market.vue", "mtime": 1753973678761}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.market-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 🔥 剪映小助手推广横幅样式 */\n.jianying-banner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n  position: relative;\n  overflow: hidden;\n}\n\n.jianying-banner::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"20\" cy=\"80\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.banner-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  z-index: 1;\n}\n\n.banner-left {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.banner-icon {\n  width: 60px;\n  height: 60px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  color: white;\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n\n.banner-text {\n  color: white;\n}\n\n.banner-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.25rem 0;\n  color: white;\n}\n\n.banner-subtitle {\n  font-size: 1rem;\n  margin: 0;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n.banner-right {\n  flex-shrink: 0;\n}\n\n.jianying-btn.ant-btn {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 48px !important;\n  padding: 0 2rem !important;\n  border-radius: 24px !important;\n  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4) !important;\n  transition: all 0.3s ease !important;\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.jianying-btn.ant-btn:hover:not(:disabled),\n.jianying-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #ee5a24, #d63031) !important;\n  box-shadow: 0 6px 25px rgba(255, 107, 107, 0.5) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.jianying-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #d63031, #c0392b) !important;\n}\n\n.jianying-btn.ant-btn .anticon {\n  font-size: 1.1rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .banner-content {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .banner-left {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .banner-title {\n    font-size: 1.3rem;\n  }\n\n  .banner-subtitle {\n    font-size: 0.9rem;\n  }\n\n  .jianying-btn.ant-btn {\n    width: 100%;\n    max-width: 200px;\n  }\n}\n\n/* 主内容区域 */\n.main-content {\n  background: #f8fafc;\n  min-height: calc(100vh - 200px);\n  padding: 2rem 0;\n}\n\n.content-layout {\n  display: grid;\n  grid-template-columns: 320px 1fr;\n  gap: 2.5rem;\n  align-items: start;\n}\n\n/* 左侧筛选栏 */\n.sidebar {\n  position: sticky;\n  top: 2rem;\n}\n\n.filter-panel {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  border: 1px solid #e2e8f0;\n}\n\n.filter-section {\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.filter-section:last-child {\n  border-bottom: none;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem 1.5rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background: white;\n}\n\n.filter-header:hover {\n  background: #f8fafc;\n}\n\n.filter-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-icon {\n  color: #3b82f6;\n  font-size: 1.1rem;\n}\n\n.filter-badge {\n  background: #3b82f6;\n  color: white;\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  margin-left: 0.5rem;\n  font-weight: 700;\n}\n\n.collapse-icon {\n  color: #64748b;\n  transition: transform 0.2s ease;\n}\n\n.filter-content {\n  padding: 0 1.5rem 1.5rem;\n}\n\n/* 🔥 搜索区域样式 */\n.search-section {\n  background: transparent;\n  padding: 0 0 2rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.search-layout {\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n}\n\n.search-input-group {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n}\n\n/* 🔥 自定义搜索输入框样式 */\n.custom-search-input {\n  flex: 1;\n  position: relative;\n  height: 56px;\n  border-radius: 28px;\n  background: #ffffff;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n}\n\n.custom-search-input::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\n  transform: translateX(-100%);\n  transition: transform 0.6s ease;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.custom-search-input:hover::before {\n  transform: translateX(100%);\n}\n\n.custom-search-input:hover,\n.custom-search-input:focus-within {\n  background: #ffffff;\n  border-color: #3b82f6;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 1.5rem;\n  color: #3b82f6;\n  font-size: 1.2rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n}\n\n.search-input-native {\n  width: 100%;\n  height: 100%;\n  border: none;\n  outline: none;\n  background: transparent;\n  font-size: 1.1rem;\n  font-weight: 500;\n  padding: 0 3rem 0 3.5rem; /* 🔥 右侧增加padding为清空图标留空间 */\n  color: #1e293b;\n  position: relative;\n  z-index: 2;\n}\n\n.search-input-native::placeholder {\n  color: #64748b;\n  font-weight: 400;\n}\n\n/* 🔥 清空搜索图标样式 */\n.clear-search-icon {\n  position: absolute;\n  right: 1.5rem;\n  color: #94a3b8;\n  font-size: 1rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  padding: 0 0.25rem;\n  border-radius: 50%;\n}\n\n.clear-search-icon:hover {\n  color: #ef4444;\n  background: rgba(239, 68, 68, 0.1);\n  transform: scale(1.1);\n}\n\n.clear-search-icon:active {\n  transform: scale(0.95);\n}\n\n/* 🔥 清空筛选按钮新样式 */\n.clear-filters-btn.ant-btn {\n  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 56px !important;\n  padding: 0 2rem !important;\n  border-radius: 28px !important;\n  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3) !important;\n  transition: all 0.3s ease !important;\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n  min-width: 140px;\n  line-height: 56px !important;\n}\n\n.clear-filters-btn.ant-btn:hover:not(:disabled),\n.clear-filters-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #4f46e5, #4338ca) !important;\n  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.clear-filters-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #4338ca, #3730a3) !important;\n}\n\n.clear-filters-btn.ant-btn:disabled,\n.clear-filters-btn.ant-btn[disabled] {\n  background: #e2e8f0 !important;\n  color: #94a3b8 !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n  border: 1px solid #e2e8f0 !important;\n}\n\n.clear-filters-btn.ant-btn .anticon {\n  margin-right: 0.5rem;\n}\n\n\n\n/* 清空筛选按钮 */\n/* 删除重复的红色样式 */\n\n/* 搜索框 */\n.search-input {\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* 分类网格 */\n.category-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.category-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f8fafc;\n  text-align: center;\n}\n\n.category-tag:hover {\n  background: #e2e8f0;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n}\n\n.category-tag.active {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.tag-icon {\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.tag-text {\n  font-weight: 500;\n  font-size: 0.85rem;\n}\n\n.tag-count {\n  font-size: 0.7rem;\n  background: rgba(0, 0, 0, 0.1);\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-weight: 600;\n  min-width: 20px;\n}\n\n.category-tag.active .tag-count {\n  background: rgba(255, 255, 255, 0.25);\n}\n\n/* 价格网格 */\n.price-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.price-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f0fdf4;\n  text-align: center;\n}\n\n.price-tag:hover {\n  background: #dcfce7;\n  border-color: #10b981;\n  transform: translateY(-2px);\n}\n\n.price-tag.active {\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  border-color: #10b981;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n}\n\n/* 排序网格 */\n.sort-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.sort-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #faf5ff;\n  text-align: center;\n}\n\n.sort-tag:hover {\n  background: #f3e8ff;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n}\n\n.sort-tag.active {\n  background: linear-gradient(135deg, #a855f7, #7c3aed);\n  color: white;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);\n}\n\n/* 自定义价格范围 */\n.custom-price-range {\n  margin-top: 1rem;\n  padding: 1.25rem;\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.custom-price-header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.custom-price-icon {\n  font-size: 1.1rem;\n}\n\n.custom-price-label {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-inputs {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.price-input-row {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.input-label {\n  font-size: 0.85rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-input {\n  width: 100%;\n  border-radius: 10px;\n  border: 2px solid #d1d5db;\n  height: 40px;\n  transition: all 0.3s ease;\n}\n\n.price-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n  transform: translateY(-1px);\n}\n\n.apply-custom-btn {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  height: 44px;\n  font-size: 0.9rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  margin-top: 0.5rem;\n}\n\n.apply-custom-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #1d4ed8, #1e40af);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n.apply-custom-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  box-shadow: none;\n  transform: none;\n}\n\n/* 右侧主内容 */\n.main-area {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n/* 结果头部 */\n.results-header {\n  padding: 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.results-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);\n}\n\n.results-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n/* 删除重复的样式定义 */\n\n/* 插件网格区域 */\n.plugins-grid-wrapper {\n  padding: 2rem;\n  min-height: 400px;\n}\n\n/* 分页区域 */\n.pagination-wrapper {\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f5f9;\n  background: #fafbfc;\n  text-align: center;\n}\n\n/* 插件展示区域 */\n.plugins-showcase {\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n  padding: 2rem 0;\n  min-height: 600px;\n}\n\n/* 结果头部 */\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 1.5rem 2rem;\n  background: white;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n  margin-bottom: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.results-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.75rem 0;\n}\n\n.active-filters {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.filter-tag {\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.header-right {\n  flex-shrink: 0;\n  text-align: right;\n}\n\n.results-count {\n  font-size: 0.9rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.count-number {\n  font-size: 1.1rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugins-grid-wrapper {\n  margin-bottom: 2rem;\n}\n\n/* 现代化分页 */\n.modern-pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 2rem;\n  background: white;\n  border-radius: 16px;\n  border: 1px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.modern-pagination::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #667eea, #764ba2);\n}\n\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.custom-pagination .ant-pagination-item {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  margin: 0 3px;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-item:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n.custom-pagination .ant-pagination-item-active {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  border-color: transparent;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.custom-pagination .ant-pagination-item-active a {\n  color: white;\n  font-weight: 600;\n}\n\n.custom-pagination .ant-pagination-prev,\n.custom-pagination .ant-pagination-next {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-prev:hover,\n.custom-pagination .ant-pagination-next:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 1400px) {\n  .content-layout {\n    grid-template-columns: 300px 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .content-layout {\n    grid-template-columns: 280px 1fr;\n    gap: 1.5rem;\n  }\n\n  .filter-section {\n    padding: 1rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .main-content {\n    padding: 1rem 0;\n  }\n\n  .content-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .sidebar {\n    position: static;\n    order: 2;\n  }\n\n  .main-area {\n    order: 1;\n  }\n\n  .results-header {\n    flex-direction: column;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .header-right {\n    text-align: left;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1rem;\n  }\n\n  .pagination-wrapper {\n    padding: 1rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .filter-section {\n    padding: 0.75rem;\n  }\n\n  .results-header {\n    padding: 0.75rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 0.75rem;\n  }\n}\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.05);\n}\n\n.plugin-badge {\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plugin-info {\n  padding: 1.5rem;\n}\n\n.plugin-name {\n  font-size: 1.3rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.plugin-description {\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  line-height: 1.6;\n}\n\n.plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.plugin-price {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugin-rating {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  color: #fbbf24;\n  font-weight: 600;\n}\n\n.btn-plugin-buy {\n  width: 100%;\n  padding: 0.875rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  color: white;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-plugin-buy:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .header-stats {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .plugins-section {\n    padding: 1rem 0;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  /* 🔥 搜索区域响应式 */\n  .search-section {\n    padding: 2rem 0;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n    gap: 1rem;\n    max-width: 100%;\n    padding: 0 1rem;\n  }\n\n  .custom-search-input {\n    height: 50px;\n  }\n\n  .search-input-native {\n    font-size: 1rem;\n    padding: 0 2.5rem 0 3rem; /* 🔥 移动端调整padding */\n  }\n\n  .clear-search-icon {\n    right: 1rem; /* 🔥 移动端调整位置 */\n    font-size: 0.9rem;\n  }\n\n  .clear-filters-btn.ant-btn {\n    height: 50px !important;\n    width: 100%;\n    min-width: auto;\n    line-height: 50px !important;\n  }\n}\n\n/* 🔥 自定义组合插件弹窗样式 */\n.combined-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.combined-modal-content {\n  background: white;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 1200px;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.combined-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32px;\n  border-bottom: none;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.combined-modal-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);\n  backdrop-filter: blur(10px);\n}\n\n.header-content1 {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 2;\n}\n\n.header-icon {\n  font-size: 2.5rem;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 12px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.header-text h2 {\n  margin: 0 0 4px 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: white !important;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n}\n\n.header-subtitle {\n  margin: 0;\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.9) !important;\n  font-weight: 500;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-close {\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white !important;\n  font-size: 1.8rem;\n  cursor: pointer;\n  padding: 0;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 2;\n  backdrop-filter: blur(10px);\n  font-weight: 300;\n}\n\n.combined-modal-close:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: rotate(90deg) scale(1.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-body {\n  padding: 24px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.loading-state,\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #64748b;\n}\n\n/* 🔥 子插件网格布局 */\n.sub-plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 24px;\n  padding: 8px;\n}\n\n/* 🔥 子插件卡片样式 - 与一级插件保持一致 */\n.sub-plugin-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.sub-plugin-card:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  border-color: #3b82f6;\n}\n\n/* 🔥 子插件图片区域 */\n.sub-plugin-image {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n}\n\n.sub-plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.4s ease;\n}\n\n.sub-plugin-card:hover .sub-plugin-image img {\n  transform: scale(1.1);\n}\n\n/* 🔥 子插件分类标签 */\n.sub-plugin-category {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(8px);\n  color: #1e293b;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  z-index: 2;\n}\n\n/* 🔥 子插件状态标签 */\n.sub-plugin-status {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n  z-index: 2;\n}\n\n/* 🔥 子插件内容区域 */\n.sub-plugin-content {\n  padding: 20px;\n}\n\n.sub-plugin-header {\n  margin-bottom: 12px;\n}\n\n.sub-plugin-title {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 8px 0;\n  line-height: 1.3;\n}\n\n.sub-plugin-author {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.author-icon {\n  font-size: 1rem;\n}\n\n.sub-plugin-description {\n  color: #64748b;\n  font-size: 0.9rem;\n  line-height: 1.6;\n  margin: 0 0 20px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* 🔥 子插件底部区域 */\n.sub-plugin-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n\n.sub-plugin-price {\n  display: flex;\n  align-items: baseline;\n  gap: 0.25rem;\n}\n\n.price-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.price-unit {\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n/* 🔥 子插件按钮 */\n.sub-plugin-btn {\n  background: linear-gradient(135deg, #3b82f6, #2563eb);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.sub-plugin-btn:hover {\n  background: linear-gradient(135deg, #2563eb, #1d4ed8);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n}\n\n.btn-icon {\n  font-size: 1rem;\n}\n\n/* 🔥 响应式设计 */\n@media (max-width: 768px) {\n  .combined-modal-content {\n    width: 95%;\n    margin: 20px;\n  }\n\n  .sub-plugins-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .combined-modal-header {\n    padding: 16px;\n  }\n\n  .combined-modal-header h2 {\n    font-size: 1.2rem;\n  }\n\n  .combined-modal-body {\n    padding: 16px;\n  }\n\n  .sub-plugin-card {\n    border-radius: 16px;\n  }\n\n  .sub-plugin-image {\n    height: 160px;\n  }\n\n  .sub-plugin-content {\n    padding: 16px;\n  }\n\n  .sub-plugin-title {\n    font-size: 1.1rem;\n  }\n\n  .sub-plugin-footer {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .sub-plugin-btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n", {"version": 3, "sources": ["Market.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk1CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Market.vue", "sourceRoot": "src/views/website/market", "sourcesContent": ["<template>\n  <WebsitePage>\n    <div class=\"market-container\">\n      <!-- 简洁页面标题 -->\n      <div class=\"simple-header\">\n        <h1 class=\"simple-title\">AI插件中心</h1>\n        <p class=\"simple-subtitle\">发现优质AI插件，提升创作效率，让每个想法都能完美实现</p>\n      </div>\n\n      <!-- 🔥 搜索区域 -->\n      <section class=\"search-section\">\n        <div class=\"container\">\n          <div class=\"search-layout\">\n            <div class=\"search-input-group\">\n              <!-- 🔥 使用原生输入框，完全自定义样式 -->\n              <div class=\"custom-search-input\">\n                <div class=\"search-icon\">\n                  <a-icon type=\"search\" />\n                </div>\n                <input\n                  v-model=\"searchKeyword\"\n                  @input=\"handleSearchInput\"\n                  @keyup.enter=\"handleSearch\"\n                  placeholder=\"搜索插件名称、描述...\"\n                  class=\"search-input-native\"\n                />\n                <!-- 🔥 清空搜索框图标 -->\n                <div\n                  v-if=\"searchKeyword\"\n                  class=\"clear-search-icon\"\n                  @click=\"clearSearchInput\"\n                  title=\"清空搜索\"\n                >\n                  <a-icon type=\"close-circle\" />\n                </div>\n              </div>\n\n              <!-- 🔥 清空筛选按钮移到搜索框右边 -->\n              <a-button\n                @click=\"clearAllFilters\"\n                size=\"large\"\n                class=\"clear-filters-btn\"\n                :disabled=\"!hasActiveFilters\">\n                <a-icon type=\"clear\" />\n                清空所有筛选\n              </a-button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 主内容区域 -->\n      <section class=\"main-content\">\n        <div class=\"container\">\n          <div class=\"content-layout\">\n            <!-- 左侧筛选栏 -->\n            <aside class=\"sidebar\">\n              <div class=\"filter-panel\">\n\n                <!-- 分类筛选 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('category')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"appstore\" class=\"filter-icon\" />\n                      插件分类\n                      <span class=\"filter-badge\" v-if=\"currentFilters.category\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.category ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.category\" class=\"filter-content\">\n                      <div class=\"category-grid\">\n                        <div\n                          class=\"category-tag\"\n                          :class=\"{ active: currentFilters.category === '' }\"\n                          @click=\"selectCategory('')\"\n                        >\n                          <span class=\"tag-icon\">🌟</span>\n                          <span class=\"tag-text\">全部</span>\n                          <span class=\"tag-count\">{{ totalPlugins }}</span>\n                        </div>\n                        <div\n                          v-for=\"category in categories\"\n                          :key=\"category.value\"\n                          class=\"category-tag\"\n                          :class=\"{ active: currentFilters.category === category.value }\"\n                          @click=\"selectCategory(category.value)\"\n                        >\n                          <span class=\"tag-icon\">{{ getCategoryIcon(category.value) }}</span>\n                          <span class=\"tag-text\">{{ category.text }}</span>\n                          <span class=\"tag-count\">{{ categoryCounts[category.value] || 0 }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n                <!-- 价格筛选 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('price')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"dollar\" class=\"filter-icon\" />\n                      价格范围\n                      <span class=\"filter-badge\" v-if=\"currentFilters.priceRange\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.price ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.price\" class=\"filter-content\">\n                      <div class=\"price-grid\">\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '' && !showCustomPrice }\"\n                          @click=\"selectPriceRange('')\"\n                        >\n                          <span class=\"tag-icon\">💰</span>\n                          <span class=\"tag-text\">全部</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '0-1' }\"\n                          @click=\"selectPriceRange('0-1')\"\n                        >\n                          <span class=\"tag-icon\">🪙</span>\n                          <span class=\"tag-text\">¥0-1</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '1-5' }\"\n                          @click=\"selectPriceRange('1-5')\"\n                        >\n                          <span class=\"tag-icon\">💵</span>\n                          <span class=\"tag-text\">¥1-5</span>\n                        </div>\n                        <div\n                          class=\"price-tag\"\n                          :class=\"{ active: currentFilters.priceRange === '5+' }\"\n                          @click=\"selectPriceRange('5+')\"\n                        >\n                          <span class=\"tag-icon\">💎</span>\n                          <span class=\"tag-text\">¥5+</span>\n                        </div>\n                      </div>\n                      <!-- 自定义价格范围 -->\n                      <div class=\"custom-price-range\">\n                        <div class=\"custom-price-header\">\n                          <span class=\"custom-price-icon\">⚙️</span>\n                          <span class=\"custom-price-label\">自定义价格</span>\n                        </div>\n                        <div class=\"price-inputs\">\n                          <div class=\"price-input-row\">\n                            <label class=\"input-label\">最低价格</label>\n                            <a-input-number\n                              v-model=\"customPriceMin\"\n                              :min=\"0\"\n                              :max=\"999\"\n                              placeholder=\"请输入最低价格\"\n                              size=\"default\"\n                              class=\"price-input\"\n                              @pressEnter=\"applyCustomPrice\"\n                            />\n                          </div>\n                          <div class=\"price-input-row\">\n                            <label class=\"input-label\">最高价格</label>\n                            <a-input-number\n                              v-model=\"customPriceMax\"\n                              :min=\"customPriceMin || 0\"\n                              :max=\"999\"\n                              placeholder=\"请输入最高价格\"\n                              size=\"default\"\n                              class=\"price-input\"\n                              @pressEnter=\"applyCustomPrice\"\n                            />\n                          </div>\n                          <a-button\n                            type=\"primary\"\n                            @click=\"applyCustomPrice\"\n                            :disabled=\"!customPriceMin && !customPriceMax\"\n                            class=\"apply-custom-btn\"\n                            block\n                          >\n                            确定筛选\n                          </a-button>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n                <!-- 排序方式 -->\n                <div class=\"filter-section\">\n                  <div class=\"filter-header\" @click=\"toggleSection('sort')\">\n                    <h3 class=\"filter-title\">\n                      <a-icon type=\"sort-ascending\" class=\"filter-icon\" />\n                      排序方式\n                      <span class=\"filter-badge\" v-if=\"currentFilters.sortType !== 'default'\">1</span>\n                    </h3>\n                    <a-icon\n                      :type=\"collapsedSections.sort ? 'down' : 'up'\"\n                      class=\"collapse-icon\"\n                    />\n                  </div>\n                  <a-collapse-transition>\n                    <div v-show=\"!collapsedSections.sort\" class=\"filter-content\">\n                      <div class=\"sort-grid\">\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'default' }\"\n                          @click=\"selectSort('default')\"\n                        >\n                          <span class=\"tag-icon\">🌟</span>\n                          <span class=\"tag-text\">默认</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'newest' }\"\n                          @click=\"selectSort('newest')\"\n                        >\n                          <span class=\"tag-icon\">⏰</span>\n                          <span class=\"tag-text\">最新</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'price-asc' }\"\n                          @click=\"selectSort('price-asc')\"\n                        >\n                          <span class=\"tag-icon\">📈</span>\n                          <span class=\"tag-text\">价格↑</span>\n                        </div>\n                        <div\n                          class=\"sort-tag\"\n                          :class=\"{ active: currentFilters.sortType === 'price-desc' }\"\n                          @click=\"selectSort('price-desc')\"\n                        >\n                          <span class=\"tag-icon\">📉</span>\n                          <span class=\"tag-text\">价格↓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </a-collapse-transition>\n                </div>\n\n\n              </div>\n            </aside>\n\n            <!-- 右侧主内容 -->\n            <main class=\"main-area\">\n              <!-- 结果头部 -->\n              <div class=\"results-header\">\n                <div class=\"header-left\">\n                  <h2 class=\"results-title\">\n                    <span v-if=\"currentFilters.category\">{{ getCategoryText(currentFilters.category) }}插件</span>\n                    <span v-else-if=\"currentFilters.keyword\">搜索结果</span>\n                    <span v-else>全部插件</span>\n                  </h2>\n                  <div class=\"active-filters\" v-if=\"currentFilters.keyword || currentFilters.priceRange || currentFilters.sortType !== 'default'\">\n                    <a-tag\n                      v-if=\"currentFilters.keyword\"\n                      closable\n                      color=\"green\"\n                      @close=\"clearKeywordFilter\"\n                      class=\"filter-tag\"\n                    >\n                      \"{{ currentFilters.keyword }}\"\n                    </a-tag>\n                    <a-tag\n                      v-if=\"currentFilters.priceRange\"\n                      closable\n                      color=\"orange\"\n                      @close=\"clearPriceFilter\"\n                      class=\"filter-tag\"\n                    >\n                      {{ getPriceRangeText(currentFilters.priceRange) }}\n                    </a-tag>\n                    <a-tag\n                      v-if=\"currentFilters.sortType !== 'default'\"\n                      closable\n                      color=\"purple\"\n                      @close=\"clearSortFilter\"\n                      class=\"filter-tag\"\n                    >\n                      {{ getSortTypeText(currentFilters.sortType) }}\n                    </a-tag>\n                  </div>\n                </div>\n                <div class=\"header-right\">\n                  <div class=\"results-count\">\n                    <span class=\"count-number\">{{ filteredPlugins.length }}</span> 个一级插件，共 <span class=\"count-number\">{{ filteredTotalPlugins }}</span> 个插件\n                  </div>\n                </div>\n              </div>\n\n              <!-- 插件网格 -->\n              <div class=\"plugins-grid-wrapper\">\n                <PluginGrid\n                  :plugins=\"currentPagePlugins\"\n                  :loading=\"loading\"\n                  :error=\"error\"\n                  @plugin-use=\"handlePluginUse\"\n                  @plugin-detail=\"handlePluginDetail\"\n                  @combined-plugin-detail=\"viewCombinedPluginDetails\"\n                  @retry=\"handleRetry\"\n                  @clear-filters=\"clearAllFilters\"\n                />\n              </div>\n\n              <!-- 分页 -->\n              <div class=\"pagination-wrapper\" v-if=\"filteredPlugins.length > pageSize\">\n                <a-pagination\n                  :current=\"currentPage\"\n                  :total=\"filteredPlugins.length\"\n                  :page-size=\"pageSize\"\n                  :page-size-options=\"['8', '12', '16', '24']\"\n                  :show-size-changer=\"true\"\n                  :show-quick-jumper=\"true\"\n                  :show-total=\"(total, range) => `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n                  @change=\"handlePageChange\"\n                  @showSizeChange=\"handlePageSizeChange\"\n                />\n              </div>\n            </main>\n          </div>\n        </div>\n      </section>\n    </div>\n\n    <!-- 🔥 组合插件子插件选择弹窗 -->\n    <div v-if=\"showCombinedModal\" class=\"combined-modal-overlay\" @click=\"closeCombinedModal\">\n      <div class=\"combined-modal-content\" @click.stop>\n        <div class=\"combined-modal-header\">\n          <div class=\"header-content1\">\n            <div class=\"header-icon\">🔗</div>\n            <div class=\"header-text\">\n              <h2>选择插件</h2>\n              <p class=\"header-subtitle\">{{ selectedCombinedPlugin && selectedCombinedPlugin.combinedName }}</p>\n            </div>\n          </div>\n          <button class=\"combined-modal-close\" @click=\"closeCombinedModal\">×</button>\n        </div>\n        <div class=\"combined-modal-body\">\n          <div v-if=\"combinedModalLoading\" class=\"loading-state\">\n            <a-spin size=\"large\" />\n            <p>正在加载子插件...</p>\n          </div>\n          <div v-else-if=\"combinedSubPlugins.length === 0\" class=\"empty-state\">\n            <p>暂无子插件</p>\n          </div>\n          <div v-else class=\"sub-plugins-grid\">\n            <div\n              v-for=\"subPlugin in combinedSubPlugins\"\n              :key=\"subPlugin.id\"\n              class=\"sub-plugin-card\"\n              @click=\"selectSubPlugin(subPlugin)\">\n              <!-- 🔥 插件图片区域 -->\n              <div class=\"sub-plugin-image\">\n                <img :src=\"getPluginImage(subPlugin)\" :alt=\"subPlugin.plubname\" />\n                <!-- 分类标签 -->\n                <div class=\"sub-plugin-category\">\n                  <span class=\"category-icon\">🔧</span>\n                  <span>{{ subPlugin.plubCategory_dictText || '其他' }}</span>\n                </div>\n                <!-- 状态标签 -->\n                <div class=\"sub-plugin-status\">上架</div>\n              </div>\n\n              <!-- 🔥 插件内容区域 -->\n              <div class=\"sub-plugin-content\">\n                <div class=\"sub-plugin-header\">\n                  <h3 class=\"sub-plugin-title\">{{ subPlugin.plubname }}</h3>\n                  <div class=\"sub-plugin-author\">\n                    <span class=\"author-icon\">👤</span>\n                    <span>创作者 {{ subPlugin.plubwrite_dictText || '未知' }}</span>\n                  </div>\n                </div>\n\n                <p class=\"sub-plugin-description\">{{ subPlugin.plubinfo || '暂无描述' }}</p>\n\n                <div class=\"sub-plugin-footer\">\n                  <div class=\"sub-plugin-price\">\n                    <span class=\"price-amount\">{{ getSubPluginPriceText(subPlugin) }}</span>\n                  </div>\n                  <button class=\"sub-plugin-btn\">\n                    <span class=\"btn-icon\">👁</span>\n                    <span>查看详情</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 🔥 悬浮式剪映小助手推广组件 -->\n    <div v-if=\"showJianYingFloat\" class=\"jianying-float-container\">\n      <button class=\"jianying-float-btn\" @click=\"goToJianYingDraft\">\n        <!-- 关闭按钮 -->\n        <div class=\"float-close-btn\" @click.stop=\"hideJianYingFloat\">\n          <a-icon type=\"close\" />\n        </div>\n\n        <!-- 按钮图标 -->\n        <div class=\"btn-icon\">\n          <a-icon type=\"download\" />\n        </div>\n\n        <!-- 按钮文字 -->\n        <div class=\"btn-text\">剪映小助手下载</div>\n\n        <!-- 发光效果 -->\n        <div class=\"btn-glow\"></div>\n\n        <!-- 粒子效果容器 -->\n        <div class=\"btn-particles\" ref=\"particles\"></div>\n\n        <!-- 波纹效果 -->\n        <div class=\"jianying-waves\"></div>\n      </button>\n    </div>\n  </WebsitePage>\n</template>\n\n<script>\nimport WebsitePage from '@/components/website/WebsitePage.vue'\nimport CategoryFilter from './components/CategoryFilter.vue'\nimport SearchFilter from './components/SearchFilter.vue'\nimport PluginGrid from './components/PluginGrid.vue'\nimport marketApi from '@/api/market'\nimport { formatCategories, validatePluginData, getPluginImageUrl, processPluginsWithCombined } from './utils/marketUtils'\nimport { HeartbeatMixin } from '@/mixins/HeartbeatMixin'\nimport { getCurrentPageConfig } from '@/utils/heartbeatConfig'\n\nexport default {\n  name: 'Market', // 确保组件名称与路由配置中的componentName一致\n  mixins: [HeartbeatMixin],\n  components: {\n    WebsitePage,\n    CategoryFilter,\n    SearchFilter,\n    PluginGrid\n  },\n\n  data() {\n    return {\n      // 心跳配置 - 商城页面使用中频心跳\n      heartbeatConfig: getCurrentPageConfig('market', {\n        apiKey: 'market-page-heartbeat-key', // 商城页面专用API密钥\n        enableDebugLog: process.env.NODE_ENV === 'development',\n      }),\n\n      // 插件数据\n      allPlugins: [],\n      filteredPlugins: [],\n      currentPagePlugins: [],\n      originalPluginsData: [], // 🔥 存储原始插件数据（用于搜索子插件）\n\n      // 分类数据\n      categories: [],\n      categoryCounts: {},\n\n      // 分页状态\n      currentPage: 1,\n      pageSize: 12,\n\n      // 筛选条件\n      currentFilters: {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      },\n\n      // 加载状态\n      loading: false,\n      error: null,\n\n      // 统计数据\n      totalPlugins: 0,\n\n      // 搜索相关\n      searchKeyword: '',\n      showSuggestions: false,\n      suggestions: ['文案生成', '图片处理', '视频剪辑', '数据分析', '代码助手'],\n\n      // 筛选面板折叠状态\n      collapsedSections: {\n        search: false,\n        category: false,\n        price: true,\n        sort: true\n      },\n\n      // 自定义价格范围\n      showCustomPrice: false,\n      customPriceMin: null,\n      customPriceMax: null,\n\n      // 🔥 组合插件弹窗\n      showCombinedModal: false,\n      selectedCombinedPlugin: null,\n      combinedModalLoading: false,\n      combinedSubPlugins: [],\n\n      // 🔥 悬浮式剪映小助手推广组件\n      showJianYingFloat: true // 是否显示悬浮组件\n    }\n  },\n\n  computed: {\n    hasActiveFilters() {\n      return this.currentFilters.category ||\n             this.currentFilters.keyword ||\n             this.currentFilters.priceRange ||\n             this.currentFilters.sortType !== 'default'\n    },\n\n    // 🔥 计算原始插件总数（包括组合插件的所有子插件）\n    totalOriginalPlugins() {\n      return this.originalPluginsData ? this.originalPluginsData.length : 0\n    },\n\n    // 🔥 计算筛选后的总插件数量（包括组合插件的子插件）\n    filteredTotalPlugins() {\n      let totalCount = 0\n\n      this.filteredPlugins.forEach(plugin => {\n        if (plugin.isCombined === 1) {\n          // 组合插件：计算其子插件数量\n          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n          totalCount += subPlugins.length\n        } else {\n          // 普通插件：计数为1\n          totalCount += 1\n        }\n      })\n\n      return totalCount\n    },\n\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    }\n  },\n\n  async created() {\n    // 悬浮组件默认显示，不需要检查localStorage\n    this.showJianYingFloat = true\n    console.log('🔥 悬浮组件默认显示')\n\n    // 只有在没有数据时才初始化，避免重复加载\n    if (this.allPlugins.length === 0) {\n      await this.initializeMarket()\n    } else {\n      // 如果已有数据，只恢复筛选状态\n      this.restoreMarketState()\n      this.applyFilters()\n      console.log('商城数据已存在，跳过重新加载')\n    }\n  },\n\n  // 组件销毁前恢复滚动\n  beforeDestroy() {\n    document.body.style.overflow = ''\n  },\n\n  // 监听筛选条件变化，保存到localStorage\n  watch: {\n    currentFilters: {\n      handler(newFilters) {\n        this.saveMarketState(newFilters);\n      },\n      deep: true\n    },\n\n    searchKeyword(newKeyword) {\n      this.saveMarketState({ ...this.currentFilters, keyword: newKeyword });\n    }\n  },\n\n  methods: {\n    // 初始化商城\n    async initializeMarket() {\n      this.loading = true\n\n      try {\n        // 恢复筛选状态\n        this.restoreMarketState();\n\n        // 并行加载分类和插件数据\n        await Promise.all([\n          this.loadCategories(),\n          this.loadPlugins()\n        ])\n\n        console.log('商城初始化完成')\n\n      } catch (error) {\n        console.error('商城初始化失败:', error)\n        this.error = '商城初始化失败，请刷新页面重试'\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载分类数据\n    async loadCategories() {\n      try {\n        const response = await marketApi.getPluginCategories()\n        if (response.success) {\n          this.categories = formatCategories(response.result || [])\n          console.log('分类数据加载成功:', this.categories)\n        }\n      } catch (error) {\n        console.error('加载分类数据失败:', error)\n      }\n    },\n\n    // 加载插件数据\n    async loadPlugins() {\n      try {\n        const params = {\n          pageNo: 1,\n          pageSize: 1000, // 获取所有数据，前端分页\n          status: 1 // 只获取已上架的插件\n        }\n\n        const response = await marketApi.getPluginList(params)\n\n        if (response.success) {\n          const result = response.result || response.data\n          const originalData = result.records || result || []\n\n          // 🔥 保存原始数据（用于搜索子插件）\n          this.originalPluginsData = [...originalData]\n\n          // 🔥 前端处理组合插件去重逻辑（使用统一工具函数）\n          const processedPlugins = processPluginsWithCombined(originalData)\n\n          this.allPlugins = processedPlugins\n          this.totalPlugins = processedPlugins.length\n\n          // 计算分类统计\n          this.calculateCategoryCounts()\n\n          // 应用筛选\n          this.applyFilters()\n\n          console.log('插件数据加载成功:', this.allPlugins.length, '个插件（包含组合插件）')\n        } else {\n          throw new Error(response.message || '获取插件数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载插件数据失败:', error)\n        this.error = error.message || '加载插件数据失败'\n      }\n    },\n\n\n\n    // 🔥 检查组合插件是否包含指定分类\n    combinedPluginHasCategory(combinedPlugin, targetCategory) {\n      // 简化版本：直接检查组合插件本身的分类\n      // 在实际应用中，这里应该查询该组合插件的所有子插件\n      // 但为了避免复杂的异步查询，我们先用简化逻辑\n      return combinedPlugin.plubCategory === targetCategory\n    },\n\n    // 🔥 计算分类统计（支持组合插件）\n    calculateCategoryCounts() {\n      this.categoryCounts = {}\n\n      this.categories.forEach(category => {\n        this.categoryCounts[category.value] = 0\n      })\n\n      // 统计组合插件数量\n      let combinedPluginCount = 0\n\n      this.allPlugins.forEach(plugin => {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件计数\n          combinedPluginCount++\n\n          // 组合插件也按其分类计数（用于其他分类的统计）\n          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            this.categoryCounts[plugin.plubCategory]++\n          }\n        } else {\n          // 普通插件计数\n          if (plugin.plubCategory && this.categoryCounts.hasOwnProperty(plugin.plubCategory)) {\n            this.categoryCounts[plugin.plubCategory]++\n          }\n        }\n      })\n\n      // 设置组合插件分类的数量\n      this.categoryCounts['combine'] = combinedPluginCount\n\n      console.log('🔥 分类统计完成:', this.categoryCounts)\n    },\n\n    // 🔥 应用筛选条件（支持组合插件）\n    applyFilters() {\n      let filtered = [...this.allPlugins]\n\n      // 🔥 分类筛选（支持组合插件）\n      if (this.currentFilters.category) {\n        if (this.currentFilters.category === 'combine') {\n          // 只显示组合插件\n          filtered = filtered.filter(plugin => plugin.isCombined === 1 || plugin.isCombined === '1')\n        } else {\n          // 显示指定分类的普通插件 + 包含该分类的组合插件\n          filtered = filtered.filter(plugin => {\n            // 普通插件：直接匹配分类\n            if (plugin.isCombined !== 1 && plugin.isCombined !== '1') {\n              return plugin.plubCategory === this.currentFilters.category\n            }\n            // 组合插件：需要检查是否包含该分类的子插件\n            return this.combinedPluginHasCategory(plugin, this.currentFilters.category)\n          })\n        }\n      }\n\n      // 🔥 关键词筛选（支持组合插件及其子插件）\n      if (this.currentFilters.keyword) {\n        const keyword = this.currentFilters.keyword.toLowerCase()\n        filtered = this.filterByKeyword(filtered, keyword)\n      }\n\n      // 价格范围筛选（只对普通插件有效）\n      if (this.currentFilters.priceRange) {\n        filtered = this.filterByPriceRange(filtered, this.currentFilters.priceRange)\n      }\n\n      // 排序（包括默认排序）\n      filtered = this.sortPlugins(filtered, this.currentFilters.sortType)\n\n      this.filteredPlugins = filtered\n      this.updateCurrentPagePlugins()\n    },\n\n    // 🔥 关键词筛选（支持搜索组合插件的子插件）\n    filterByKeyword(plugins, keyword) {\n      return plugins.filter(plugin => {\n        if (plugin.isCombined === 1 || plugin.isCombined === '1') {\n          // 组合插件：搜索组合插件名、描述和子插件名\n\n          // 1. 搜索组合插件本身的名称和描述\n          if ((plugin.combinedName && plugin.combinedName.toLowerCase().includes(keyword)) ||\n              (plugin.combinedDescription && plugin.combinedDescription.toLowerCase().includes(keyword))) {\n            return true\n          }\n\n          // 2. 搜索组合插件的子插件名称\n          // 从原始数据中查找同名的组合插件的所有子插件\n          const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n          return subPlugins.some(subPlugin =>\n            subPlugin.plubname && subPlugin.plubname.toLowerCase().includes(keyword)\n          )\n        } else {\n          // 普通插件：搜索插件名和描述\n          return (plugin.plubname && plugin.plubname.toLowerCase().includes(keyword)) ||\n                 (plugin.plubinfo && plugin.plubinfo.toLowerCase().includes(keyword))\n        }\n      })\n    },\n\n    // 🔥 从原始数据中获取组合插件的子插件\n    getSubPluginsFromOriginalData(combinedName) {\n      // 从loadPlugins时获取的原始数据中查找\n      // 这里需要访问处理前的原始插件数据\n      if (!this.originalPluginsData) {\n        return []\n      }\n\n      return this.originalPluginsData.filter(plugin =>\n        plugin.isCombined === 1 &&\n        plugin.combinedName === combinedName\n      )\n    },\n\n    // 价格范围筛选\n    filterByPriceRange(plugins, priceRange) {\n      if (!priceRange) return plugins\n\n      return plugins.filter(plugin => {\n        const price = parseFloat(plugin.neednum) || 0\n\n        switch (priceRange) {\n          case '0-1':\n            return price >= 0 && price <= 1\n          case '1-5':\n            return price > 1 && price <= 5\n          case '5+':\n            return price > 5\n          default:\n            // 自定义范围 格式: \"min-max\"\n            if (priceRange.includes('-')) {\n              const [min, max] = priceRange.split('-').map(p => parseFloat(p) || 0)\n              if (max === 999) {\n                return price >= min\n              } else {\n                return price >= min && price <= max\n              }\n            }\n            return true\n        }\n      })\n    },\n\n    // 插件排序\n    sortPlugins(plugins, sortType) {\n      const sorted = [...plugins]\n\n      switch (sortType) {\n        case 'default':\n          // 默认排序：按照aigc_plub_shop表的sort_order字段排序（权重越小越靠前）\n          return sorted.sort((a, b) => {\n            const weightA = parseFloat(a.sortOrder || a.sort_order || 999999) // 如果没有权重，设置为很大的数\n            const weightB = parseFloat(b.sortOrder || b.sort_order || 999999)\n            return weightA - weightB // 权重小的在前\n          })\n        case 'newest':\n          // 按创建时间排序（假设有createTime字段，如果没有可以用id或其他字段）\n          return sorted.sort((a, b) => {\n            const timeA = new Date(a.createTime || a.createBy || 0).getTime()\n            const timeB = new Date(b.createTime || b.createBy || 0).getTime()\n            return timeB - timeA // 最新的在前\n          })\n        case 'price-asc':\n          // 价格从低到高\n          return sorted.sort((a, b) => {\n            const priceA = parseFloat(a.neednum) || 0\n            const priceB = parseFloat(b.neednum) || 0\n            return priceA - priceB\n          })\n        case 'price-desc':\n          // 价格从高到低\n          return sorted.sort((a, b) => {\n            const priceA = parseFloat(a.neednum) || 0\n            const priceB = parseFloat(b.neednum) || 0\n            return priceB - priceA\n          })\n        case 'name-asc':\n          // 名称A-Z\n          return sorted.sort((a, b) => {\n            const nameA = (a.plubname || '').toLowerCase()\n            const nameB = (b.plubname || '').toLowerCase()\n            return nameA.localeCompare(nameB)\n          })\n        default:\n          // 如果是未知的排序类型，也使用默认排序\n          return sorted.sort((a, b) => {\n            const weightA = parseFloat(a.sortWeight || a.sort_weight || 999999)\n            const weightB = parseFloat(b.sortWeight || b.sort_weight || 999999)\n            return weightA - weightB\n          })\n      }\n    },\n\n    // 更新当前页插件\n    updateCurrentPagePlugins() {\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      this.currentPagePlugins = this.filteredPlugins.slice(start, end)\n    },\n\n    // 处理分类变更\n    handleCategoryChange(data) {\n      this.currentFilters.category = data.category\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('分类筛选变更:', data)\n    },\n\n    // 处理搜索变更\n    handleSearchChange(data) {\n      this.currentFilters.keyword = data.keyword\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('搜索变更:', data)\n    },\n\n    // 处理筛选变更\n    handleFilterChange(filters) {\n      this.currentFilters = { ...this.currentFilters, ...filters }\n      this.currentPage = 1\n      this.applyFilters()\n      console.log('筛选条件变更:', filters)\n    },\n\n    // 处理分页变更\n    handlePageChange(page, pageSize) {\n      this.currentPage = page\n      if (pageSize) {\n        this.pageSize = pageSize\n      }\n      this.updateCurrentPagePlugins()\n\n      // 滚动到顶部\n      window.scrollTo({ top: 0, behavior: 'smooth' })\n    },\n\n    // 处理页面大小变更\n    handlePageSizeChange(page, pageSize) {\n      this.currentPage = page\n      this.pageSize = pageSize\n      this.updateCurrentPagePlugins()\n    },\n\n    // 处理插件使用 - 跳转到详情页\n    handlePluginUse(plugin) {\n      console.log('使用插件:', plugin)\n\n      // 检查插件数据有效性\n      if (!validatePluginData(plugin)) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 跳转到插件详情页\n      this.$router.push(`/market/plugin/${plugin.id}`)\n    },\n\n    // 处理插件详情\n    handlePluginDetail(plugin) {\n      console.log('查看插件详情:', plugin)\n\n      // 检查插件数据有效性\n      if (!plugin || !plugin.id) {\n        this.$notification.error({\n          message: '插件数据异常',\n          description: '无法查看详情',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      // 跳转到插件详情页\n      this.$router.push(`/market/plugin/${plugin.id}`)\n    },\n\n    // 处理重试\n    handleRetry() {\n      this.error = null\n      this.loadPlugins()\n    },\n\n    // 🔥 获取子插件价格显示文本\n    getSubPluginPriceText(subPlugin) {\n      const price = subPlugin.neednum\n      const isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1'\n\n      if (!price || price <= 0) {\n        return '免费'\n      }\n\n      if (isSvipFree) {\n        return `SVIP免费，低至¥${price}/次`\n      } else {\n        return `低至¥${price}/次`\n      }\n    },\n\n    // 清空所有筛选\n    clearAllFilters() {\n      this.currentFilters = {\n        category: '',\n        keyword: '',\n        priceRange: '',\n        sortType: 'default',\n        author: ''\n      }\n      this.searchKeyword = ''\n      // 清空自定义价格输入框\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.currentPage = 1\n\n      // 清空localStorage中的筛选状态\n      this.clearMarketState()\n\n      // 重置子组件\n      if (this.$refs.categoryFilter) {\n        this.$refs.categoryFilter.resetCategory()\n      }\n      if (this.$refs.searchFilter) {\n        this.$refs.searchFilter.resetFilters()\n      }\n\n      this.applyFilters()\n      this.$notification.info({\n        message: '筛选已清空',\n        description: '已清空所有筛选条件',\n        placement: 'topRight'\n      })\n    },\n\n    // 实时搜索输入处理\n    handleSearchInput() {\n      // 实时更新搜索关键词\n      this.currentFilters.keyword = this.searchKeyword\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    // 🔥 搜索按钮点击或回车搜索\n    handleSearch() {\n      this.currentFilters.keyword = this.searchKeyword\n      this.currentPage = 1\n      this.applyFilters()\n\n      if (this.searchKeyword) {\n        this.$notification.success({\n          message: '搜索执行中',\n          description: `正在搜索\"${this.searchKeyword}\"相关插件`,\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 🔥 清空搜索框\n    clearSearchInput() {\n      this.searchKeyword = ''\n      this.currentFilters.keyword = ''\n      this.currentPage = 1\n      this.applyFilters()\n\n      this.$notification.info({\n        message: '搜索已清空',\n        description: '已清空搜索关键词',\n        placement: 'topRight'\n      })\n    },\n\n    selectCategory(category) {\n      this.currentFilters.category = category\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    selectPriceRange(range) {\n      this.currentFilters.priceRange = range\n      this.currentPage = 1\n      // 选择预设价格范围时清空自定义输入\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.applyFilters()\n    },\n\n    selectSort(sortType) {\n      this.currentFilters.sortType = sortType\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    getCategoryIcon(category) {\n      const icons = {\n        // 按分类值匹配\n        'ai-chat': '💬',\n        'ai-image': '🎨',\n        'ai-video': '🎬',\n        'ai-audio': '🎵',\n        'social-share': '📱',\n        'tools': '⚙️',\n        'entertainment': '🎮',\n        'combine': '🔗', // 🔥 组合插件图标\n        'other': '🔧',\n\n        // 按分类文本匹配（兼容旧数据）\n        '内容生成': '✍️',\n        '图片生成': '🎨',\n        '视频处理': '🎬',\n        '数据分析': '📊',\n        '开发工具': '⚙️',\n        '设计创意': '🎭',\n        '营销工具': '📈',\n        'AI对话': '💬',\n        'AI绘画': '🎨',\n        'AI视频': '🎬',\n        'AI音频': '🎵',\n        '社交分享': '📱',\n        '工具类': '⚙️',\n        '娱乐': '🎮',\n        '组合插件': '🔗', // 🔥 组合插件图标\n        '其他': '🔧'\n      }\n      return icons[category] || '🔧'\n    },\n\n    getCategoryText(categoryValue) {\n      const category = this.categories.find(cat => cat.value === categoryValue)\n      return category ? category.text : categoryValue\n    },\n\n    // 获取价格范围显示文字\n    getPriceRangeText(priceRange) {\n      const priceTexts = {\n        '0-1': '¥0 - ¥1',\n        '1-5': '¥1 - ¥5',\n        '5+': '¥5以上'\n      }\n\n      // 如果是预设范围，返回对应文字\n      if (priceTexts[priceRange]) {\n        return priceTexts[priceRange]\n      }\n\n      // 如果是自定义范围，格式化显示\n      if (priceRange && priceRange.includes('-')) {\n        const [min, max] = priceRange.split('-')\n        if (max === '999') {\n          return `¥${min}以上`\n        } else {\n          return `¥${min} - ¥${max}`\n        }\n      }\n\n      return priceRange\n    },\n\n    // 获取排序方式显示文字\n    getSortTypeText(sortType) {\n      const sortTexts = {\n        'newest': '最新发布',\n        'price-asc': '价格从低到高',\n        'price-desc': '价格从高到低',\n        'name-asc': '名称A-Z'\n      }\n      return sortTexts[sortType] || sortType\n    },\n\n    clearCategoryFilter() {\n      this.currentFilters.category = ''\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearKeywordFilter() {\n      this.currentFilters.keyword = ''\n      this.searchKeyword = ''\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearPriceFilter() {\n      this.currentFilters.priceRange = ''\n      this.showCustomPrice = false\n      this.customPriceMin = null\n      this.customPriceMax = null\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    clearSortFilter() {\n      this.currentFilters.sortType = 'default'\n      this.currentPage = 1\n      this.applyFilters()\n    },\n\n    // 切换筛选面板折叠状态\n    toggleSection(section) {\n      this.collapsedSections[section] = !this.collapsedSections[section]\n    },\n\n    // 应用自定义价格\n    applyCustomPrice() {\n      if (this.customPriceMin !== null || this.customPriceMax !== null) {\n        const min = this.customPriceMin || 0\n        const max = this.customPriceMax || 999\n        this.currentFilters.priceRange = `${min}-${max}`\n        this.currentPage = 1\n        this.applyFilters()\n        this.$notification.success({\n          message: '价格筛选已应用',\n          description: `已应用价格范围：¥${min} - ${max === 999 ? '以上' : '¥' + max}`,\n          placement: 'topRight'\n        })\n      }\n    },\n\n    // 切换自定义价格\n    toggleCustomPrice() {\n      this.showCustomPrice = !this.showCustomPrice\n      if (this.showCustomPrice) {\n        // 打开自定义价格时，设置一个特殊标识，不清除筛选\n        // 这样\"全部\"就不会亮起，但也不会有实际的价格筛选\n      } else {\n        // 如果关闭自定义价格，清除自定义筛选\n        if (this.currentFilters.priceRange && this.currentFilters.priceRange.includes('-') && !['0-1', '1-5', '5+'].includes(this.currentFilters.priceRange)) {\n          this.currentFilters.priceRange = ''\n          this.applyFilters()\n        }\n      }\n    },\n\n    // 保存商城筛选状态到localStorage\n    saveMarketState(filters) {\n      try {\n        const state = {\n          category: filters.category || '',\n          search: filters.keyword || this.searchKeyword || '',\n          priceRange: filters.priceRange || '',\n          sortBy: filters.sortType || 'default',\n          timestamp: Date.now()\n        };\n\n        localStorage.setItem('market_filter_state', JSON.stringify(state));\n      } catch (error) {\n        console.warn('保存商城筛选状态失败:', error);\n      }\n    },\n\n    // 恢复商城筛选状态\n    restoreMarketState() {\n      try {\n        const stateStr = localStorage.getItem('market_filter_state');\n        if (!stateStr) return;\n\n        const state = JSON.parse(stateStr);\n\n        // 检查状态是否过期（24小时）\n        const now = Date.now();\n        const stateAge = now - (state.timestamp || 0);\n        const maxAge = 24 * 60 * 60 * 1000; // 24小时\n\n        if (stateAge > maxAge) {\n          localStorage.removeItem('market_filter_state');\n          return;\n        }\n\n        // 恢复筛选状态\n        if (state.category) {\n          this.currentFilters.category = state.category;\n        }\n        if (state.search) {\n          this.currentFilters.keyword = state.search;\n          this.searchKeyword = state.search;\n        }\n        if (state.priceRange) {\n          this.currentFilters.priceRange = state.priceRange;\n        }\n        if (state.sortBy && state.sortBy !== 'default') {\n          this.currentFilters.sortType = state.sortBy;\n        }\n\n        console.log('已恢复商城筛选状态:', state);\n\n      } catch (error) {\n        console.warn('恢复商城筛选状态失败:', error);\n        localStorage.removeItem('market_filter_state');\n      }\n    },\n\n    // 清空筛选状态\n    clearMarketState() {\n      try {\n        localStorage.removeItem('market_filter_state');\n      } catch (error) {\n        console.warn('清空商城筛选状态失败:', error);\n      }\n    },\n\n    // 🔥 查看组合插件详情（显示子插件选择弹窗）\n    async viewCombinedPluginDetails(plugin) {\n      console.log('🔗 查看组合插件详情:', plugin.combinedName)\n\n      this.selectedCombinedPlugin = plugin\n      this.showCombinedModal = true\n      this.combinedModalLoading = true\n      this.combinedSubPlugins = []\n\n      // 禁止背景滚动\n      document.body.style.overflow = 'hidden'\n\n      // 加载子插件\n      try {\n        const subPlugins = this.getSubPluginsFromOriginalData(plugin.combinedName)\n        this.combinedSubPlugins = subPlugins\n        console.log('🔗 加载子插件成功:', subPlugins.length)\n      } catch (error) {\n        console.error('🔗 加载子插件失败:', error)\n        this.$notification.error({\n          message: '加载失败',\n          description: '获取子插件列表失败',\n          placement: 'topRight'\n        })\n      } finally {\n        this.combinedModalLoading = false\n      }\n    },\n\n    // 🔥 选择子插件（跳转到具体插件详情页）\n    handleSelectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname)\n      this.showCombinedModal = false\n\n      // 跳转到插件详情页\n      this.$router.push({\n        name: 'PluginDetail',\n        params: { id: subPlugin.id }\n      })\n    },\n\n    // 🔥 关闭组合插件弹窗\n    closeCombinedModal() {\n      this.showCombinedModal = false\n      this.selectedCombinedPlugin = null\n      this.combinedSubPlugins = []\n\n      // 恢复背景滚动\n      document.body.style.overflow = ''\n    },\n\n    // 🔥 选择子插件\n    selectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname)\n      this.closeCombinedModal()\n\n      // 跳转到插件详情页\n      this.$router.push({\n        name: 'PluginDetail',\n        params: { id: subPlugin.id }\n      })\n    },\n\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage(plugin) {\n      return getPluginImageUrl(plugin, this.defaultPluginImage)\n    },\n\n    // 🔥 跳转到剪映小助手页面\n    goToJianYingDraft() {\n      this.$router.push('/JianYingDraft')\n      console.log('跳转到剪映小助手页面')\n    },\n\n    // 🔥 悬浮组件交互方法\n    // 隐藏悬浮组件（仅在当前会话中隐藏，刷新页面后重新显示）\n    hideJianYingFloat() {\n      this.showJianYingFloat = false\n      console.log('🔥 隐藏悬浮组件（仅当前会话）')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.market-container {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);\n  min-height: 100vh;\n  padding: 2rem 0;\n}\n\n/* 简洁页面标题 */\n.simple-header {\n  text-align: center;\n  padding: 2rem 0 3rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.simple-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.simple-subtitle {\n  font-size: 1.1rem;\n  color: #64748b;\n  margin: 0;\n}\n\n/* 🔥 剪映小助手推广横幅样式 */\n.jianying-banner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1.5rem 0;\n  border-bottom: 1px solid #e2e8f0;\n  position: relative;\n  overflow: hidden;\n}\n\n.jianying-banner::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"20\" cy=\"80\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.banner-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  z-index: 1;\n}\n\n.banner-left {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.banner-icon {\n  width: 60px;\n  height: 60px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  color: white;\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n\n.banner-text {\n  color: white;\n}\n\n.banner-title {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.25rem 0;\n  color: white;\n}\n\n.banner-subtitle {\n  font-size: 1rem;\n  margin: 0;\n  color: rgba(255, 255, 255, 0.9);\n  font-weight: 400;\n}\n\n.banner-right {\n  flex-shrink: 0;\n}\n\n.jianying-btn.ant-btn {\n  background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 48px !important;\n  padding: 0 2rem !important;\n  border-radius: 24px !important;\n  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4) !important;\n  transition: all 0.3s ease !important;\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.jianying-btn.ant-btn:hover:not(:disabled),\n.jianying-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #ee5a24, #d63031) !important;\n  box-shadow: 0 6px 25px rgba(255, 107, 107, 0.5) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.jianying-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #d63031, #c0392b) !important;\n}\n\n.jianying-btn.ant-btn .anticon {\n  font-size: 1.1rem;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .banner-content {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n\n  .banner-left {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n\n  .banner-title {\n    font-size: 1.3rem;\n  }\n\n  .banner-subtitle {\n    font-size: 0.9rem;\n  }\n\n  .jianying-btn.ant-btn {\n    width: 100%;\n    max-width: 200px;\n  }\n}\n\n/* 主内容区域 */\n.main-content {\n  background: #f8fafc;\n  min-height: calc(100vh - 200px);\n  padding: 2rem 0;\n}\n\n.content-layout {\n  display: grid;\n  grid-template-columns: 320px 1fr;\n  gap: 2.5rem;\n  align-items: start;\n}\n\n/* 左侧筛选栏 */\n.sidebar {\n  position: sticky;\n  top: 2rem;\n}\n\n.filter-panel {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  border: 1px solid #e2e8f0;\n}\n\n.filter-section {\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.filter-section:last-child {\n  border-bottom: none;\n}\n\n.filter-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.25rem 1.5rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background: white;\n}\n\n.filter-header:hover {\n  background: #f8fafc;\n}\n\n.filter-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.filter-icon {\n  color: #3b82f6;\n  font-size: 1.1rem;\n}\n\n.filter-badge {\n  background: #3b82f6;\n  color: white;\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  margin-left: 0.5rem;\n  font-weight: 700;\n}\n\n.collapse-icon {\n  color: #64748b;\n  transition: transform 0.2s ease;\n}\n\n.filter-content {\n  padding: 0 1.5rem 1.5rem;\n}\n\n/* 🔥 搜索区域样式 */\n.search-section {\n  background: transparent;\n  padding: 0 0 2rem 0;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.search-layout {\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n}\n\n.search-input-group {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n}\n\n/* 🔥 自定义搜索输入框样式 */\n.custom-search-input {\n  flex: 1;\n  position: relative;\n  height: 56px;\n  border-radius: 28px;\n  background: #ffffff;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n}\n\n.custom-search-input::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\n  transform: translateX(-100%);\n  transition: transform 0.6s ease;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.custom-search-input:hover::before {\n  transform: translateX(100%);\n}\n\n.custom-search-input:hover,\n.custom-search-input:focus-within {\n  background: #ffffff;\n  border-color: #3b82f6;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 1.5rem;\n  color: #3b82f6;\n  font-size: 1.2rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n}\n\n.search-input-native {\n  width: 100%;\n  height: 100%;\n  border: none;\n  outline: none;\n  background: transparent;\n  font-size: 1.1rem;\n  font-weight: 500;\n  padding: 0 3rem 0 3.5rem; /* 🔥 右侧增加padding为清空图标留空间 */\n  color: #1e293b;\n  position: relative;\n  z-index: 2;\n}\n\n.search-input-native::placeholder {\n  color: #64748b;\n  font-weight: 400;\n}\n\n/* 🔥 清空搜索图标样式 */\n.clear-search-icon {\n  position: absolute;\n  right: 1.5rem;\n  color: #94a3b8;\n  font-size: 1rem;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  height: 100%;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  padding: 0 0.25rem;\n  border-radius: 50%;\n}\n\n.clear-search-icon:hover {\n  color: #ef4444;\n  background: rgba(239, 68, 68, 0.1);\n  transform: scale(1.1);\n}\n\n.clear-search-icon:active {\n  transform: scale(0.95);\n}\n\n/* 🔥 清空筛选按钮新样式 */\n.clear-filters-btn.ant-btn {\n  background: linear-gradient(135deg, #6366f1, #4f46e5) !important;\n  border: none !important;\n  color: white !important;\n  font-weight: 600;\n  height: 56px !important;\n  padding: 0 2rem !important;\n  border-radius: 28px !important;\n  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3) !important;\n  transition: all 0.3s ease !important;\n  position: relative;\n  overflow: hidden;\n  white-space: nowrap;\n  min-width: 140px;\n  line-height: 56px !important;\n}\n\n.clear-filters-btn.ant-btn:hover:not(:disabled),\n.clear-filters-btn.ant-btn:focus:not(:disabled) {\n  background: linear-gradient(135deg, #4f46e5, #4338ca) !important;\n  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4) !important;\n  transform: translateY(-2px);\n  border: none !important;\n  color: white !important;\n}\n\n.clear-filters-btn.ant-btn:active:not(:disabled) {\n  transform: translateY(0);\n  background: linear-gradient(135deg, #4338ca, #3730a3) !important;\n}\n\n.clear-filters-btn.ant-btn:disabled,\n.clear-filters-btn.ant-btn[disabled] {\n  background: #e2e8f0 !important;\n  color: #94a3b8 !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;\n  cursor: not-allowed !important;\n  transform: none !important;\n  border: 1px solid #e2e8f0 !important;\n}\n\n.clear-filters-btn.ant-btn .anticon {\n  margin-right: 0.5rem;\n}\n\n\n\n/* 清空筛选按钮 */\n/* 删除重复的红色样式 */\n\n/* 搜索框 */\n.search-input {\n  border-radius: 12px;\n  border: 2px solid #e2e8f0;\n  transition: all 0.3s ease;\n}\n\n.search-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* 分类网格 */\n.category-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.category-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f8fafc;\n  text-align: center;\n}\n\n.category-tag:hover {\n  background: #e2e8f0;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n}\n\n.category-tag.active {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n  border-color: #3b82f6;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.tag-icon {\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n.tag-text {\n  font-weight: 500;\n  font-size: 0.85rem;\n}\n\n.tag-count {\n  font-size: 0.7rem;\n  background: rgba(0, 0, 0, 0.1);\n  padding: 0.2rem 0.5rem;\n  border-radius: 10px;\n  font-weight: 600;\n  min-width: 20px;\n}\n\n.category-tag.active .tag-count {\n  background: rgba(255, 255, 255, 0.25);\n}\n\n/* 价格网格 */\n.price-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.price-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #f0fdf4;\n  text-align: center;\n}\n\n.price-tag:hover {\n  background: #dcfce7;\n  border-color: #10b981;\n  transform: translateY(-2px);\n}\n\n.price-tag.active {\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  border-color: #10b981;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n}\n\n/* 排序网格 */\n.sort-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.5rem;\n}\n\n.sort-tag {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.75rem 0.5rem;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: #faf5ff;\n  text-align: center;\n}\n\n.sort-tag:hover {\n  background: #f3e8ff;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n}\n\n.sort-tag.active {\n  background: linear-gradient(135deg, #a855f7, #7c3aed);\n  color: white;\n  border-color: #a855f7;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);\n}\n\n/* 自定义价格范围 */\n.custom-price-range {\n  margin-top: 1rem;\n  padding: 1.25rem;\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n  border-radius: 16px;\n  border: 2px solid #e2e8f0;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.custom-price-header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.custom-price-icon {\n  font-size: 1.1rem;\n}\n\n.custom-price-label {\n  font-size: 0.9rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-inputs {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.price-input-row {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.input-label {\n  font-size: 0.85rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.price-input {\n  width: 100%;\n  border-radius: 10px;\n  border: 2px solid #d1d5db;\n  height: 40px;\n  transition: all 0.3s ease;\n}\n\n.price-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n  transform: translateY(-1px);\n}\n\n.apply-custom-btn {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  border: none;\n  border-radius: 10px;\n  font-weight: 600;\n  height: 44px;\n  font-size: 0.9rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n  transition: all 0.3s ease;\n  margin-top: 0.5rem;\n}\n\n.apply-custom-btn:hover:not(:disabled) {\n  background: linear-gradient(135deg, #1d4ed8, #1e40af);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n}\n\n.apply-custom-btn:disabled {\n  background: #e5e7eb;\n  color: #9ca3af;\n  box-shadow: none;\n  transform: none;\n}\n\n/* 右侧主内容 */\n.main-area {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n/* 结果头部 */\n.results-header {\n  padding: 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.results-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);\n}\n\n.results-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n/* 删除重复的样式定义 */\n\n/* 插件网格区域 */\n.plugins-grid-wrapper {\n  padding: 2rem;\n  min-height: 400px;\n}\n\n/* 分页区域 */\n.pagination-wrapper {\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f5f9;\n  background: #fafbfc;\n  text-align: center;\n}\n\n/* 插件展示区域 */\n.plugins-showcase {\n  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\n  padding: 2rem 0;\n  min-height: 600px;\n}\n\n/* 结果头部 */\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 1.5rem 2rem;\n  background: white;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n  margin-bottom: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n.header-left {\n  flex: 1;\n}\n\n.results-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.75rem 0;\n}\n\n.active-filters {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.filter-tag {\n  border-radius: 16px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.header-right {\n  flex-shrink: 0;\n  text-align: right;\n}\n\n.results-count {\n  font-size: 0.9rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n.count-number {\n  font-size: 1.1rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugins-grid-wrapper {\n  margin-bottom: 2rem;\n}\n\n/* 现代化分页 */\n.modern-pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 2rem;\n  background: white;\n  border-radius: 16px;\n  border: 1px solid #e2e8f0;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n  position: relative;\n  overflow: hidden;\n}\n\n.modern-pagination::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #667eea, #764ba2);\n}\n\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.custom-pagination .ant-pagination-item {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  margin: 0 3px;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-item:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n.custom-pagination .ant-pagination-item-active {\n  background: linear-gradient(135deg, #667eea, #764ba2);\n  border-color: transparent;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.custom-pagination .ant-pagination-item-active a {\n  color: white;\n  font-weight: 600;\n}\n\n.custom-pagination .ant-pagination-prev,\n.custom-pagination .ant-pagination-next {\n  border-radius: 10px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.custom-pagination .ant-pagination-prev:hover,\n.custom-pagination .ant-pagination-next:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n}\n\n/* 响应式设计 */\n@media (max-width: 1400px) {\n  .content-layout {\n    grid-template-columns: 300px 1fr;\n    gap: 2rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .content-layout {\n    grid-template-columns: 280px 1fr;\n    gap: 1.5rem;\n  }\n\n  .filter-section {\n    padding: 1rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1.5rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .main-content {\n    padding: 1rem 0;\n  }\n\n  .content-layout {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .sidebar {\n    position: static;\n    order: 2;\n  }\n\n  .main-area {\n    order: 1;\n  }\n\n  .results-header {\n    flex-direction: column;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .header-right {\n    text-align: left;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 1rem;\n  }\n\n  .pagination-wrapper {\n    padding: 1rem;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .simple-title {\n    font-size: 1.8rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .filter-section {\n    padding: 0.75rem;\n  }\n\n  .results-header {\n    padding: 0.75rem;\n  }\n\n  .plugins-grid-wrapper {\n    padding: 0.75rem;\n  }\n}\n\n.container {\n  max-width: 1600px;\n  margin: 0 auto;\n  padding: 0 2rem;\n}\n\n.plugin-card:hover .plugin-image img {\n  transform: scale(1.05);\n}\n\n.plugin-badge {\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n}\n\n.plugin-info {\n  padding: 1.5rem;\n}\n\n.plugin-name {\n  font-size: 1.3rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.plugin-description {\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  line-height: 1.6;\n}\n\n.plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n\n.plugin-price {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.plugin-rating {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  color: #fbbf24;\n  font-weight: 600;\n}\n\n.btn-plugin-buy {\n  width: 100%;\n  padding: 0.875rem;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  border: none;\n  color: white;\n  border-radius: 10px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn-plugin-buy:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .simple-title {\n    font-size: 2rem;\n  }\n\n  .simple-subtitle {\n    font-size: 1rem;\n  }\n\n  .header-stats {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .stat-number {\n    font-size: 1.5rem;\n  }\n\n  .plugins-section {\n    padding: 1rem 0;\n  }\n\n  .container {\n    padding: 0 1rem;\n  }\n\n  /* 🔥 搜索区域响应式 */\n  .search-section {\n    padding: 2rem 0;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n    gap: 1rem;\n    max-width: 100%;\n    padding: 0 1rem;\n  }\n\n  .custom-search-input {\n    height: 50px;\n  }\n\n  .search-input-native {\n    font-size: 1rem;\n    padding: 0 2.5rem 0 3rem; /* 🔥 移动端调整padding */\n  }\n\n  .clear-search-icon {\n    right: 1rem; /* 🔥 移动端调整位置 */\n    font-size: 0.9rem;\n  }\n\n  .clear-filters-btn.ant-btn {\n    height: 50px !important;\n    width: 100%;\n    min-width: auto;\n    line-height: 50px !important;\n  }\n}\n\n/* 🔥 自定义组合插件弹窗样式 */\n.combined-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.combined-modal-content {\n  background: white;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 1200px;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-50px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n.combined-modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 32px;\n  border-bottom: none;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n\n.combined-modal-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);\n  backdrop-filter: blur(10px);\n}\n\n.header-content1 {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  position: relative;\n  z-index: 2;\n}\n\n.header-icon {\n  font-size: 2.5rem;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 12px;\n  padding: 12px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n.header-text h2 {\n  margin: 0 0 4px 0;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: white !important;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n}\n\n.header-subtitle {\n  margin: 0;\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.9) !important;\n  font-weight: 500;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-close {\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white !important;\n  font-size: 1.8rem;\n  cursor: pointer;\n  padding: 0;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  position: relative;\n  z-index: 2;\n  backdrop-filter: blur(10px);\n  font-weight: 300;\n}\n\n.combined-modal-close:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: rotate(90deg) scale(1.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n\n.combined-modal-body {\n  padding: 24px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.loading-state,\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #64748b;\n}\n\n/* 🔥 子插件网格布局 */\n.sub-plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 24px;\n  padding: 8px;\n}\n\n/* 🔥 子插件卡片样式 - 与一级插件保持一致 */\n.sub-plugin-card {\n  background: white;\n  border-radius: 20px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  border: 2px solid transparent;\n}\n\n.sub-plugin-card:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\n  border-color: #3b82f6;\n}\n\n/* 🔥 子插件图片区域 */\n.sub-plugin-image {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n}\n\n.sub-plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.4s ease;\n}\n\n.sub-plugin-card:hover .sub-plugin-image img {\n  transform: scale(1.1);\n}\n\n/* 🔥 子插件分类标签 */\n.sub-plugin-category {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(8px);\n  color: #1e293b;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.3rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  z-index: 2;\n}\n\n/* 🔥 子插件状态标签 */\n.sub-plugin-status {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: linear-gradient(135deg, #10b981, #059669);\n  color: white;\n  padding: 0.4rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n  z-index: 2;\n}\n\n/* 🔥 子插件内容区域 */\n.sub-plugin-content {\n  padding: 20px;\n}\n\n.sub-plugin-header {\n  margin-bottom: 12px;\n}\n\n.sub-plugin-title {\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 8px 0;\n  line-height: 1.3;\n}\n\n.sub-plugin-author {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.author-icon {\n  font-size: 1rem;\n}\n\n.sub-plugin-description {\n  color: #64748b;\n  font-size: 0.9rem;\n  line-height: 1.6;\n  margin: 0 0 20px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* 🔥 子插件底部区域 */\n.sub-plugin-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n\n.sub-plugin-price {\n  display: flex;\n  align-items: baseline;\n  gap: 0.25rem;\n}\n\n.price-amount {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #3b82f6;\n}\n\n.price-unit {\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 500;\n}\n\n/* 🔥 子插件按钮 */\n.sub-plugin-btn {\n  background: linear-gradient(135deg, #3b82f6, #2563eb);\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n\n.sub-plugin-btn:hover {\n  background: linear-gradient(135deg, #2563eb, #1d4ed8);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n}\n\n.btn-icon {\n  font-size: 1rem;\n}\n\n/* 🔥 响应式设计 */\n@media (max-width: 768px) {\n  .combined-modal-content {\n    width: 95%;\n    margin: 20px;\n  }\n\n  .sub-plugins-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .combined-modal-header {\n    padding: 16px;\n  }\n\n  .combined-modal-header h2 {\n    font-size: 1.2rem;\n  }\n\n  .combined-modal-body {\n    padding: 16px;\n  }\n\n  .sub-plugin-card {\n    border-radius: 16px;\n  }\n\n  .sub-plugin-image {\n    height: 160px;\n  }\n\n  .sub-plugin-content {\n    padding: 16px;\n  }\n\n  .sub-plugin-title {\n    font-size: 1.1rem;\n  }\n\n  .sub-plugin-footer {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .sub-plugin-btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style>\n"]}]}