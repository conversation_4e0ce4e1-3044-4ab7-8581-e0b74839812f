server:
  port: 8080
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /jeecg-boot
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: zrkvgrwjdwdejfae
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: false
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: ***********************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: ***************************************************************************************************************************************************************************************************************************
          #username: root
          #password: root
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: '123456'
    port: 6379
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/system/**/xml/*Mapper.xml,classpath*:org/jeecg/modules/aigc/**/xml/*Mapper.xml,classpath*:org/jeecg/modules/demo/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao :
  base-package: org.jeecg.modules.jmreport.*
  #DB类型（mysql | postgresql | oracle | sqlserver| other）
  db-type: mysql
jeecg :
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss\火山引擎：tos
  uploadType: tos
  path :
    #文件上传根目录 设置
    upload: C:/aigcview/upload
    #webapp文件路径
    webapp: C:/aigcview/webapp
  shiro:
     excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/category/**,/visual/**,/map/**,/jmreport/bigscreen2/**,/api/getUserInfo,/aigc/aigcHomeCarousel/list,/aigc/websiteFeatures/list,/aigc/websiteStats/list,/api/auth/**,/api/aigc/verify-apikey,/api/aigc/xiaohongshu/**,/api/aigc/coze/**,/api/coze/video/**,/api/aigc/image/**,/api/aigc/html/**,/api/aigc/qrcode/**,/api/jianying/**,/api/jianyingpro/**,/plubshop/aigcPlubShop/getPluginDetail,/plubshop/aigcPlubShop/list,/sys/dict/getDictItems/**,/api/alipay/notify,/api/alipay/return,/api/alipay/auth/callback,/api/agent/market/list
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: LTAI5tSWPfBoqXs1yKT6hMqh
    secretKey: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: jeecgdev
    staticDomain: https://static.jeecg.com  # 已启用HTTPS
  # 阿里云短信服务配置
  sms:
    accessKeyId: LTAI5tSWPfBoqXs1yKT6hMqh
    accessKeySecret: ******************************
    signName: 岳阳卓软信息技术
    templateCode: SMS_490740286
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: ************:9200
    check-enabled: true
  # 智界Aigc生产环境配置
  aigc:
    base:
      url: https://www.aigcview.com  # 临时使用IP访问
    html:
      storage:
        path: C:/aigcview/html/
    qrcode:
      storage:
        path: C:/aigcview/qrcode/
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
    # 文件、图片上传方式，可选项：qiniu（七牛云）、system（跟随系统配置）
    upload-type: system
    map:
      # 配置百度地图的AK，申请地址：https://lbs.baidu.com/apiconsole/key?application=key#/home
      baidu: ??
  # 在线预览文件服务器地址配置
  file-view-domain: https://fileview.jeecg.com
  # minio文件上传
  minio:
    minio_url: https://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: otatest
  #大屏报表参数设置
  jmreport:
    mode: prod
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: true
    #必须校验方法
    verify_methods: remove,delete,save,add,update
  #Wps在线文档
  wps:
    domain: https://wwo.wps.cn/office/  # 已启用HTTPS
    appid: ??
    appsecret: ??
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: https://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  route:
    config:
      data-id: jeecg-gateway-router
      group: DEFAULT_GROUP
      #自定义路由配置 yml nacos database
      data-type: database
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#cas单点登录
cas:
  prefixUrl: https://cas.example.org:8443/cas
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper : info
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: true
  basic:
    enable: true
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: https://www.aigcview.com/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: false
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: false
      # appKey
      client-id: ??
      # appSecret
      client-secret: ??
      agent-id: ??

# 智界Aigc生产环境配置
aigc:
  base:
    url: https://www.aigcview.com  # 临时使用IP访问
  html:
    storage:
      path: C:/aigcview/html/
  qrcode:
    storage:
      path: C:/aigcview/qrcode/
  # API配置
  api:
    # HTML安全配置
    html-security:
      enabled: true
      max-content-size: 2097152         # 2MB
      allow-external-resources: false
    # 文件存储配置
    file-storage:
      html-path: C:/aigcview/html/
      qrcode-path: C:/aigcview/qrcode/
      retention-days: 30
      enable-cleanup: true
      max-file-count: 100000
  # 用户注册配置
  register:
    # 验证码配置
    verify-code:
      sms:
        expire-minutes: 5           # 短信验证码有效期（分钟）
        send-interval-seconds: 60   # 发送间隔（秒）
      email:
        expire-minutes: 5           # 邮箱验证码有效期（分钟）
        send-interval-seconds: 60   # 发送间隔（秒）
      captcha:
        expire-minutes: 2           # 图形验证码有效期（分钟）
    # 密码复杂度要求
    password:
      min-length: 8               # 最小长度
      require-letter: true        # 需要字母
      require-number: true        # 需要数字
      require-special: false      # 需要特殊字符
    # 邀请码配置
    invite-code:
      length: 8                   # 邀请码长度
      type: "alphanumeric"        # 类型：numeric-纯数字，alpha-纯字母，alphanumeric-字母数字混合
      expire-days: 0              # 有效期（天），0表示永久有效
    # 安全策略
    security:
      ip-register-limit-hourly: 5 # 同一IP每小时注册限制次数
      phone-register-limit-daily: 3 # 同一手机号每天注册尝试次数（防止恶意注册）
      email-register-limit-daily: 3 # 同一邮箱每天注册尝试次数（防止恶意注册）
      # 验证码发送限制（按场景）
      sms-code-limit-daily: 20      # 短信验证码每天总限制（登录场景）
      email-code-limit-daily: 20    # 邮箱验证码每天总限制（登录场景）
      reset-code-limit-daily: 3     # 重置密码验证码每天限制
  # 用户活跃状态追踪配置 - 生产环境
  user-activity:
    # 基础开关配置
    enabled: false                          # 生产环境初始关闭，待验证后开启
    test-mode: false                        # 生产环境不使用测试模式

    # 缓存配置
    cache-duration: 300                     # 缓存持续时间（秒）- 5分钟
    cache-expire-time: 600                  # Redis缓存过期时间（秒）- 10分钟
    redis-key-prefix: "aigc:prod:user:activity:" # 生产环境Redis键前缀

    # 批量处理配置
    batch-size: 200                         # 生产环境使用较大批量
    batch-interval: 5                       # 生产环境更频繁更新

    # 性能配置
    performance-threshold: 800              # 生产环境更严格的性能要求
    enable-performance-monitoring: true     # 启用性能监控
    performance-monitoring-sample-rate: 5  # 生产环境较低采样率

    # 核心API列表（生产环境更全面）
    critical-apis:
      - "/payment/"
      - "/login"
      - "/logout"
      - "/order/"
      - "/api/auth/"
      - "/api/aigc/verify-apikey"
      - "/api/payment/"

    # 灰度发布配置
    rollout-percentage: 0                   # 生产环境初始0%，逐步放开

    # 重试配置
    max-retry-count: 5                      # 生产环境更多重试
    retry-interval: 800                     # 重试间隔（毫秒）

    # 用户状态管理
    offline-user-cleanup-interval: 120      # 生产环境较长清理间隔
    user-offline-threshold: 15              # 标准离线阈值

    # 异步处理配置
    enable-async-processing: true           # 启用异步处理
    async-thread-pool-size: 8               # 生产环境较大线程池
    queue-capacity: 2000                    # 生产环境较大队列

    # 降级机制配置
    enable-degradation: true                # 启用降级机制
    degradation-recovery-time: 600          # 生产环境较长恢复时间

# 🔐 微信公众号配置（安全模式）
wechat:
  mp:
    appId: wx_your_app_id_here
    secret: your_app_secret_here
    token: AigcView_Secure_Token_2025_Web
    aesKey: W5XxJZ1xfwGtQaGlTz385un90tA0dGKwXzmtlu3LaHv
    # 授权回调地址
    redirectUrl: https://www.aigcview.com/jeecg-boot/api/auth/wechat/callback

# 🔥 火山引擎TOS对象存储配置（华东上海 - 双桶策略）
volcengine:
  tos:
    access-key: AKLTMDlkOTAxNWJjZDYyNGEyYWFkODIzOGM3N2UxMGMyZTM
    secret-key: TXpJeU1UZGhZVEU0TW1WaU5EQTJNR0UzTnpFeE5HTmpaV00wTTJabVpURQ==
    endpoint: tos-cn-shanghai.volces.com
    region: cn-shanghai
    bucket: aigcview-tos                     # 通用文件桶（有CDN）
    # 🆕 CDN配置
    cdn:
      enabled: true                           # CDN开关
      domain: "https://cdn.aigcview.com"      # CDN域名
      fallback-to-tos: true                   # 降级到TOS直接访问
    # 🆕 混合网络访问配置
    internal:
      endpoint: tos-cn-shanghai.ivolces.com    # 内网端点（免费流量）- 注意是ivolces.com
      enabled: true                            # 启用内网访问
    # 通用文件存储配置
    general:
      bucket: aigcview-tos            # 通用文件存储桶（有CDN回源）
      base-path: "uploads"            # 通用文件基础路径
    # 🔒 剪映助手专用配置（独立桶，无CDN）
    jianying:
      bucket: aigcview-jianying       # 剪映专用桶（无CDN回源）
      base-path: "jianying-assistant" # 剪映文件基础路径
      file-retention-days: 5          # 文件保留天数
    # 签名URL配置（私有访问）
    signed-url:
      expire-hours: 24                # 签名URL有效期24小时
    auto-cleanup: true
    cleanup-time: "02:00"
    base-path: "/jianying-assistant/drafts/"
    file-prefix: "zj_draft_"

# 🤖 Coze平台API配置
coze:
  api:
    base-url: https://api.coze.cn
    token: # 请填写您的Coze API Token

# 💰 支付宝支付配置（正式环境）
alipay:
  app-id: 2021005179622178
  private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCHR4vWWL/GcLjUnJ9NbVUSHK71nfmHc65SKdDurAAMI/eAh6UnggBdpRXBf6w00rzBENpMWM0WpYUJVRT5D96kmEptGKEKyus3p0ItUY5QK6WC8dixuipODEK0Bw/oOv2IK+GBzuroM790wSZwJKZZGxCE0Qn2FQnNysqZXKVNuhor/2uE7LY9GvirlUyepFjclZuNvpImS4CCapdABv+genbSWlq4tRPKNiTokuZoUaJkgNrbMIQFe43uanKPdn6HBvf7UyTsdfIswxepWa9uS10KLOAJ9dVlGEthgpaOBOtTUJ8ipZVKvPEyUlKuKttMgoxYn0Hg6J+t0Ky5UDKFAgMBAAECggEAAZAFAHvT80HYELSibm2F3wivyKyDdloSuGY9QJSCB6HeuQBYGwW4ZVyDzNFhttZ8UaqVKu6Xp9xPlhUCggH562LK8UmoioxffSwUapuXjjuC1OlgPGVPGcmwd43hVChiR1FicHJoajg2zO1yOTdjKIJUhYQ4njwN+e+OuYjFbAlmw/Sv20GTtGu85FAjfxgA7xm3cL0PK6GojixzT8ql09f82LLc+sDlqlrckajD8g8BRhNuOfGhJ6Pi0iWYUPiiOtO6ehZaaOnSsaF7iNceCb63BglAZiFeNYEIgn1nbhesw+FdYIeBteNJvU9GKBM9O78uZd7PH3zepP3QjbyhHQKBgQD91W4PqjrHwIaZG6as/k8f/+dpWKD0bfgjXfSx/axMRUKytIg7GC7kDHUyaVwZCmddLQiUhp2cHy4YV5OigiF2QaiuZcg9yS47kr2zaUtWERkAS2bX6rah5oi3sySZs+oBlhFblRG5QdTXHSt2dblmU/LsVXG+xR6o+Fcpd8wpowKBgQCIbxoLub7Lp7dwHZ4zq1LYckXazc1/uhMMCWeHKgTutZLXKRQtSKPN7D5vyNLV4XuS+FSp90kmXwSYaQtpZJlGrYp8X33iUC3OdXAzGvlsWTjdrMHTk1IGONCAkPmTgtHqOlQk0JNwswKbEGmpYDbZE3mYN4Z0d2/2wX5mqZzFtwKBgHX10YgGG/roRaSxbOvH4w2D5w7V2Ta+RCprZ2Ov93oaVbymmQidXG0uPOiIffxFEXQDkZE0XdpHwywcxscmolZKfRFOy5eq/olA0Fen/xKoL24rnEeLFAR5FeAIqGGlcoH6M2bUB8CID1lIfQI4A0jCiom5y3pUMMNlZVwoCADxAoGAHZaiumpFE5vC76Csv53nMKPPvH0nlQzAtrqPZkPZn/9m4hy5kHyRccZLYCjYeKQXDa+nhNjpV1YCBB+75tZ3W1mlkAWRonQrCvxzIdI2x6Wt7H1b58uqXUkTBt7qEXCsn7SYrQdDAo0AAqsTjzUbkAZmN3nLQhTpBkW+aPtd0Y8CgYAMMfTh/W8VIigGPVXRs5qC8CBuPH2+AP2Wwm4RyH03xq1GtZNLM/hYkBKbtXPbeq+Em/BXtiNNCevFaq2IvAaN27wJR70kEYy07/nlv1Qpy3wHTaCbfARI4YHamGpDOdkSAkuuep8GZlTZNqsdaLHI7NrzH+rHBH1cRdTNUAbaog==

  alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyqRCUsAfnioBBMK5lbyRTuPHTu1/cMP94OAX0jDabg3VOaItw6/Cnahu5SdMq7XBU2XKd1hGG/DF7Bx5oqA1fWSKZAP6b7UHfjjsLNmtIl+nVGNJiMI7bjHmzFF/CaKXBgONoBVAjTRza6WkpEY00vqJmbQ7yzd+Zk+px7dPNC0Z4GCTY2YBtp1RF3iDIE7WcL/B8JhZwxqzyibthrYAu0eGYGL97O74z7Mtut3cLDlM9/Gw2UYG0X/dHPoGVJE9kN7T9lRUy8zLTaW47In/O91T6uhBrVTYK8M8eDp5ldXCc1S+FeYAgGEtp8tdY3h2o793oRAxf5PYePJtdezf3QIDAQAB
  sign-type: RSA2
  charset: UTF-8
  gateway-url: https://openapi.alipay.com/gateway.do
  format: json
  notify-url: https://www.aigcview.com/jeecg-boot/api/alipay/notify
  return-url: https://www.aigcview.com/jeecg-boot/api/alipay/return
  sandbox: false