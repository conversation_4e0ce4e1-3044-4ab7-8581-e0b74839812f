{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\aigcview\\agent\\modules\\AigcAgentForm.vue", "mtime": 1753968167936}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n\nimport { getAction, httpAction } from '@/api/manage'\nimport { FormTypes,getRefPromise,VALIDATE_NO_PASSED,validateFormModelAndTables } from '@/utils/JEditableTableUtil'\nimport { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin'\nimport { validateDuplicateValue } from '@/utils/util'\nimport JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'\n\nexport default {\n  name: 'AigcAgentForm',\n  mixins: [JEditableTableModelMixin],\n  components: {\n    JImageUploadDeferred\n  },\n  data() {\n    return {\n      labelCol: {\n        xs: { span: 24 },\n        sm: { span: 6 },\n      },\n      wrapperCol: {\n        xs: { span: 24 },\n        sm: { span: 16 },\n      },\n      labelCol2: {\n        xs: { span: 24 },\n        sm: { span: 3 },\n      },\n      wrapperCol2: {\n        xs: { span: 24 },\n        sm: { span: 20 },\n      },\n      model:{\n        authorType: '1', // 默认作者类型：官方\n        auditStatus: '2' // 默认审核状态：审核通过\n      },\n      // 新增时子表默认添加几行空数据\n      addDefaultRowNum: 1,\n      validatorRules: {\n         authorType: [\n            { required: true, message: '请输入作者类型!'},\n         ],\n         agentId: [\n            { required: true, message: '请输入智能体ID!'},\n         ],\n         agentName: [\n            { required: true, message: '请输入智能体名称!'},\n         ],\n         agentDescription: [\n            { required: true, message: '请输入智能体描述!'},\n         ],\n         agentAvatar: [\n            { required: true, message: '请输入智能体头像!'},\n         ],\n         price: [\n            { required: true, message: '请输入价格（元）!'},\n         ],\n         auditStatus: [\n            { required: true, message: '请输入审核状态!'},\n         ],\n      },\n      refKeys: ['aigcWorkflow', ],\n      tableKeys:['aigcWorkflow', ],\n      activeKey: 'aigcWorkflow',\n      // 工作流表\n      aigcWorkflowTable: {\n        loading: false,\n        dataSource: [],\n        columns: [\n          {\n            title: '智能体ID',\n            key: 'agentId',\n            type: FormTypes.hidden,\n            defaultValue: ''\n          },\n          {\n            title: '工作流ID',\n            key: 'workflowId',\n            type: FormTypes.input,\n            width:\"200px\",\n            placeholder: '请输入${title}',\n            defaultValue:'',\n          },\n          {\n            title: '工作流名称',\n            key: 'workflowName',\n            type: FormTypes.input,\n            width:\"200px\",\n            placeholder: '请输入${title}',\n            defaultValue:'',\n          },\n          {\n            title: '工作流描述',\n            key: 'workflowDescription',\n            type: FormTypes.input,\n            width:\"200px\",\n            placeholder: '请输入${title}',\n            defaultValue:'',\n          },\n          {\n            title: '工作流压缩包',\n            key: 'workflowPackage',\n            type: FormTypes.file,\n            token:true,\n            responseName:\"message\",\n            width:\"200px\",\n            placeholder: '请选择文件',\n            defaultValue:'',\n          },\n        ]\n      },\n      url: {\n        add: \"/aigc_agent/aigcAgent/add\",\n        edit: \"/aigc_agent/aigcAgent/edit\",\n        queryById: \"/aigc_agent/aigcAgent/queryById\",\n        aigcWorkflow: {\n          list: '/aigc_agent/aigcAgent/queryAigcWorkflowByMainId'\n        },\n      }\n    }\n  },\n  props: {\n    //表单禁用\n    disabled: {\n      type: Boolean,\n      default: false,\n      required: false\n    }\n  },\n  computed: {\n    formDisabled(){\n      return this.disabled\n    },\n  },\n  created () {\n  },\n  methods: {\n    addBefore(){\n      this.aigcWorkflowTable.dataSource=[]\n    },\n    getAllTable() {\n      let values = this.tableKeys.map(key => getRefPromise(this, key))\n      return Promise.all(values)\n    },\n    /** 调用完edit()方法之后会自动调用此方法 */\n    editAfter() {\n      this.$nextTick(() => {\n      })\n      // 加载子表数据\n      if (this.model.id) {\n        let params = { id: this.model.id }\n        this.requestSubTableData(this.url.aigcWorkflow.list, params, this.aigcWorkflowTable)\n      }\n    },\n    //校验所有一对一子表表单\n    validateSubForm(allValues){\n        return new Promise((resolve,reject)=>{\n          Promise.all([\n          ]).then(() => {\n            resolve(allValues)\n          }).catch(e => {\n            if (e.error === VALIDATE_NO_PASSED) {\n              // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n              this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n            } else {\n              console.error(e)\n            }\n          })\n        })\n    },\n    /** 整理成formData */\n    classifyIntoFormData(allValues) {\n      let main = Object.assign(this.model, allValues.formValue)\n      return {\n        ...main, // 展开\n        aigcWorkflowList: allValues.tablesValue[0].values,\n      }\n    },\n    validateError(msg){\n      this.$message.error(msg)\n    },\n\n    // 视频上传前验证\n    beforeVideoUpload(file) {\n      // 检查文件类型\n      const isVideo = file.type.indexOf('video/') === 0\n      if (!isVideo) {\n        this.$message.error('只能上传视频文件!')\n        return false\n      }\n\n      // 检查文件大小（100MB）\n      const isLt100M = file.size / 1024 / 1024 < 100\n      if (!isLt100M) {\n        this.$message.error('视频大小不能超过 100MB!')\n        return false\n      }\n\n      console.log('🎯 视频上传验证通过:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB')\n      return true\n    },\n\n    // 🔥 重写handleOk方法，集成延迟上传逻辑\n    async handleOk() {\n      console.log('🎯 AigcAgentForm: 开始保存智能体...')\n\n      /** 先处理头像上传，更新model值 */\n      try {\n        // 🔥 先上传待上传的头像，更新model.agentAvatar\n        await this.uploadPendingImages()\n      } catch (error) {\n        console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n        this.$message.error('头像上传失败: ' + (error.message || '未知错误'))\n        return\n      }\n\n      /** 触发表单验证 */\n      this.getAllTable().then(tables => {\n        // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n        const originalAvatar = this.model.agentAvatar\n        if (!this.model.agentAvatar && this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {\n          console.log('🎯 AigcAgentForm: 检测到待上传头像，临时设置头像值以通过验证')\n          this.model.agentAvatar = 'pending_upload' // 临时值\n        }\n\n        /** 一次性验证主表和所有的次表 */\n        return validateFormModelAndTables(this.$refs.form, this.model, tables).then(result => {\n          // 恢复原始值\n          this.model.agentAvatar = originalAvatar\n          return result\n        }).catch(error => {\n          // 恢复原始值\n          this.model.agentAvatar = originalAvatar\n          throw error\n        })\n      }).then(allValues => {\n        /** 一次性验证一对一的所有子表 */\n        return this.validateSubForm(allValues)\n      }).then(async (allValues) => {\n        try {\n          // 整理表单数据\n          let formData = this.classifyIntoFormData(allValues)\n\n          // 发起保存请求\n          const result = await this.request(formData)\n\n          // 🔥 保存成功后，确认删除被替换的原始头像文件\n          await this.confirmDeleteOriginalFiles()\n\n          return result\n        } catch (error) {\n          console.error('🎯 AigcAgentForm: 保存失败:', error)\n          throw error\n        }\n      }).catch(e => {\n        if (e.error === VALIDATE_NO_PASSED) {\n          // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n          this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n        } else {\n          console.error(e)\n        }\n      })\n    },\n\n    // 🔥 上传待上传的头像\n    async uploadPendingImages() {\n      console.log('🎯 AigcAgentForm: 开始检查待上传头像...')\n\n      if (this.$refs.avatarUpload) {\n        if (this.$refs.avatarUpload.hasPendingFiles()) {\n          console.log('🎯 AigcAgentForm: 发现待上传的头像，开始上传...')\n          try {\n            const uploadedFiles = await this.$refs.avatarUpload.performUpload()\n            console.log('🎯 AigcAgentForm: 头像上传结果:', uploadedFiles)\n\n            // 上传完成后，获取最终的值\n            const finalValue = this.$refs.avatarUpload.getCurrentValue()\n            this.model.agentAvatar = finalValue\n            console.log('🎯 AigcAgentForm: 头像最终值:', this.model.agentAvatar)\n          } catch (error) {\n            console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n            throw error\n          }\n        } else {\n          // 没有待上传文件，检查是否有删除操作\n          const currentValue = this.$refs.avatarUpload.getCurrentValue()\n          this.model.agentAvatar = currentValue\n          console.log('🎯 AigcAgentForm: 头像无新上传，当前值:', this.model.agentAvatar)\n        }\n      }\n\n      console.log('🎯 AigcAgentForm: 头像处理完成')\n    },\n\n    // 🔥 确认删除被替换的原始头像文件\n    async confirmDeleteOriginalFiles() {\n      if (this.$refs.avatarUpload) {\n        try {\n          await this.$refs.avatarUpload.confirmDeleteOriginalFiles()\n          console.log('🎯 AigcAgentForm: 原始头像文件清理完成')\n        } catch (error) {\n          console.warn('🎯 AigcAgentForm: 原始头像文件清理失败:', error)\n          // 删除失败不影响主流程\n        }\n      }\n    },\n\n    // 🔥 关闭表单时回滚头像变更\n    handleClose() {\n      console.log('🎯 AigcAgentForm: 表单关闭，回滚头像变更')\n\n      if (this.$refs.avatarUpload) {\n        this.$refs.avatarUpload.rollbackChanges()\n      }\n\n      // 发出关闭事件\n      this.$emit('close')\n    }\n\n  }\n}\n", {"version": 3, "sources": ["AigcAgentForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA", "file": "AigcAgentForm.vue", "sourceRoot": "src/views/aigcview/agent/modules", "sourcesContent": ["<template>\n  <a-spin :spinning=\"confirmLoading\">\n    <j-form-container :disabled=\"formDisabled\">\n      <!-- 主表单区域 -->\n      <a-form-model ref=\"form\" :model=\"model\" :rules=\"validatorRules\" slot=\"detail\">\n        <a-row>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"作者类型\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"authorType\">\n              <j-dict-select-tag type=\"list\" v-model=\"model.authorType\" dictCode=\"author_type\" placeholder=\"请选择作者类型\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体ID\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentId\">\n              <a-input v-model=\"model.agentId\" placeholder=\"请输入智能体ID\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体名称\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentName\">\n              <a-input v-model=\"model.agentName\" placeholder=\"请输入智能体名称\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"24\">\n            <a-form-model-item label=\"智能体描述\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"agentDescription\">\n              <a-textarea v-model=\"model.agentDescription\" rows=\"4\" placeholder=\"请输入智能体描述\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"智能体头像\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"agentAvatar\">\n              <j-image-upload-deferred\n                ref=\"avatarUpload\"\n                v-model=\"model.agentAvatar\"\n                :isMultiple=\"false\"\n                bizPath=\"agent-avatar\"\n                text=\"上传头像\">\n              </j-image-upload-deferred>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"展示视频\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"demoVideo\">\n              <j-upload v-model=\"model.demoVideo\" :beforeUpload=\"beforeVideoUpload\" text=\"上传视频(最大100MB)\"></j-upload>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"体验链接\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"experienceLink\">\n              <a-input v-model=\"model.experienceLink\" placeholder=\"请输入体验链接\" ></a-input>\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"价格（元）\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"price\">\n              <a-input-number v-model=\"model.price\" placeholder=\"请输入价格（元）\" style=\"width: 100%\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"8\" >\n            <a-form-model-item label=\"审核状态\" :labelCol=\"labelCol\" :wrapperCol=\"wrapperCol\" prop=\"auditStatus\">\n              <j-dict-select-tag type=\"list\" v-model=\"model.auditStatus\" dictCode=\"audit_status\" placeholder=\"请选择审核状态\" />\n            </a-form-model-item>\n          </a-col>\n          <a-col :span=\"24\">\n            <a-form-model-item label=\"审核备注\" :labelCol=\"labelCol2\" :wrapperCol=\"wrapperCol2\" prop=\"auditRemark\">\n              <a-textarea v-model=\"model.auditRemark\" rows=\"4\" placeholder=\"请输入审核备注\" />\n            </a-form-model-item>\n          </a-col>\n        </a-row>\n      </a-form-model>\n    </j-form-container>\n      <!-- 子表单区域 -->\n    <a-tabs v-model=\"activeKey\" @change=\"handleChangeTabs\">\n      <a-tab-pane tab=\"工作流表\" :key=\"refKeys[0]\" :forceRender=\"true\">\n        <j-editable-table\n          :ref=\"refKeys[0]\"\n          :loading=\"aigcWorkflowTable.loading\"\n          :columns=\"aigcWorkflowTable.columns\"\n          :dataSource=\"aigcWorkflowTable.dataSource\"\n          :maxHeight=\"300\"\n          :disabled=\"formDisabled\"\n          :rowNumber=\"true\"\n          :rowSelection=\"true\"\n          :actionButton=\"true\"/>\n      </a-tab-pane>\n    </a-tabs>\n  </a-spin>\n</template>\n\n<script>\n\n  import { getAction, httpAction } from '@/api/manage'\n  import { FormTypes,getRefPromise,VALIDATE_NO_PASSED,validateFormModelAndTables } from '@/utils/JEditableTableUtil'\n  import { JEditableTableModelMixin } from '@/mixins/JEditableTableModelMixin'\n  import { validateDuplicateValue } from '@/utils/util'\n  import JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'\n\n  export default {\n    name: 'AigcAgentForm',\n    mixins: [JEditableTableModelMixin],\n    components: {\n      JImageUploadDeferred\n    },\n    data() {\n      return {\n        labelCol: {\n          xs: { span: 24 },\n          sm: { span: 6 },\n        },\n        wrapperCol: {\n          xs: { span: 24 },\n          sm: { span: 16 },\n        },\n        labelCol2: {\n          xs: { span: 24 },\n          sm: { span: 3 },\n        },\n        wrapperCol2: {\n          xs: { span: 24 },\n          sm: { span: 20 },\n        },\n        model:{\n          authorType: '1', // 默认作者类型：官方\n          auditStatus: '2' // 默认审核状态：审核通过\n        },\n        // 新增时子表默认添加几行空数据\n        addDefaultRowNum: 1,\n        validatorRules: {\n           authorType: [\n              { required: true, message: '请输入作者类型!'},\n           ],\n           agentId: [\n              { required: true, message: '请输入智能体ID!'},\n           ],\n           agentName: [\n              { required: true, message: '请输入智能体名称!'},\n           ],\n           agentDescription: [\n              { required: true, message: '请输入智能体描述!'},\n           ],\n           agentAvatar: [\n              { required: true, message: '请输入智能体头像!'},\n           ],\n           price: [\n              { required: true, message: '请输入价格（元）!'},\n           ],\n           auditStatus: [\n              { required: true, message: '请输入审核状态!'},\n           ],\n        },\n        refKeys: ['aigcWorkflow', ],\n        tableKeys:['aigcWorkflow', ],\n        activeKey: 'aigcWorkflow',\n        // 工作流表\n        aigcWorkflowTable: {\n          loading: false,\n          dataSource: [],\n          columns: [\n            {\n              title: '智能体ID',\n              key: 'agentId',\n              type: FormTypes.hidden,\n              defaultValue: ''\n            },\n            {\n              title: '工作流ID',\n              key: 'workflowId',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流名称',\n              key: 'workflowName',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流描述',\n              key: 'workflowDescription',\n              type: FormTypes.input,\n              width:\"200px\",\n              placeholder: '请输入${title}',\n              defaultValue:'',\n            },\n            {\n              title: '工作流压缩包',\n              key: 'workflowPackage',\n              type: FormTypes.file,\n              token:true,\n              responseName:\"message\",\n              width:\"200px\",\n              placeholder: '请选择文件',\n              defaultValue:'',\n            },\n          ]\n        },\n        url: {\n          add: \"/aigc_agent/aigcAgent/add\",\n          edit: \"/aigc_agent/aigcAgent/edit\",\n          queryById: \"/aigc_agent/aigcAgent/queryById\",\n          aigcWorkflow: {\n            list: '/aigc_agent/aigcAgent/queryAigcWorkflowByMainId'\n          },\n        }\n      }\n    },\n    props: {\n      //表单禁用\n      disabled: {\n        type: Boolean,\n        default: false,\n        required: false\n      }\n    },\n    computed: {\n      formDisabled(){\n        return this.disabled\n      },\n    },\n    created () {\n    },\n    methods: {\n      addBefore(){\n        this.aigcWorkflowTable.dataSource=[]\n      },\n      getAllTable() {\n        let values = this.tableKeys.map(key => getRefPromise(this, key))\n        return Promise.all(values)\n      },\n      /** 调用完edit()方法之后会自动调用此方法 */\n      editAfter() {\n        this.$nextTick(() => {\n        })\n        // 加载子表数据\n        if (this.model.id) {\n          let params = { id: this.model.id }\n          this.requestSubTableData(this.url.aigcWorkflow.list, params, this.aigcWorkflowTable)\n        }\n      },\n      //校验所有一对一子表表单\n      validateSubForm(allValues){\n          return new Promise((resolve,reject)=>{\n            Promise.all([\n            ]).then(() => {\n              resolve(allValues)\n            }).catch(e => {\n              if (e.error === VALIDATE_NO_PASSED) {\n                // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n                this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n              } else {\n                console.error(e)\n              }\n            })\n          })\n      },\n      /** 整理成formData */\n      classifyIntoFormData(allValues) {\n        let main = Object.assign(this.model, allValues.formValue)\n        return {\n          ...main, // 展开\n          aigcWorkflowList: allValues.tablesValue[0].values,\n        }\n      },\n      validateError(msg){\n        this.$message.error(msg)\n      },\n\n      // 视频上传前验证\n      beforeVideoUpload(file) {\n        // 检查文件类型\n        const isVideo = file.type.indexOf('video/') === 0\n        if (!isVideo) {\n          this.$message.error('只能上传视频文件!')\n          return false\n        }\n\n        // 检查文件大小（100MB）\n        const isLt100M = file.size / 1024 / 1024 < 100\n        if (!isLt100M) {\n          this.$message.error('视频大小不能超过 100MB!')\n          return false\n        }\n\n        console.log('🎯 视频上传验证通过:', file.name, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB')\n        return true\n      },\n\n      // 🔥 重写handleOk方法，集成延迟上传逻辑\n      async handleOk() {\n        console.log('🎯 AigcAgentForm: 开始保存智能体...')\n\n        /** 先处理头像上传，更新model值 */\n        try {\n          // 🔥 先上传待上传的头像，更新model.agentAvatar\n          await this.uploadPendingImages()\n        } catch (error) {\n          console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n          this.$message.error('头像上传失败: ' + (error.message || '未知错误'))\n          return\n        }\n\n        /** 触发表单验证 */\n        this.getAllTable().then(tables => {\n          // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n          const originalAvatar = this.model.agentAvatar\n          if (!this.model.agentAvatar && this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {\n            console.log('🎯 AigcAgentForm: 检测到待上传头像，临时设置头像值以通过验证')\n            this.model.agentAvatar = 'pending_upload' // 临时值\n          }\n\n          /** 一次性验证主表和所有的次表 */\n          return validateFormModelAndTables(this.$refs.form, this.model, tables).then(result => {\n            // 恢复原始值\n            this.model.agentAvatar = originalAvatar\n            return result\n          }).catch(error => {\n            // 恢复原始值\n            this.model.agentAvatar = originalAvatar\n            throw error\n          })\n        }).then(allValues => {\n          /** 一次性验证一对一的所有子表 */\n          return this.validateSubForm(allValues)\n        }).then(async (allValues) => {\n          try {\n            // 整理表单数据\n            let formData = this.classifyIntoFormData(allValues)\n\n            // 发起保存请求\n            const result = await this.request(formData)\n\n            // 🔥 保存成功后，确认删除被替换的原始头像文件\n            await this.confirmDeleteOriginalFiles()\n\n            return result\n          } catch (error) {\n            console.error('🎯 AigcAgentForm: 保存失败:', error)\n            throw error\n          }\n        }).catch(e => {\n          if (e.error === VALIDATE_NO_PASSED) {\n            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab\n            this.activeKey = e.index == null ? this.activeKey : this.refKeys[e.index]\n          } else {\n            console.error(e)\n          }\n        })\n      },\n\n      // 🔥 上传待上传的头像\n      async uploadPendingImages() {\n        console.log('🎯 AigcAgentForm: 开始检查待上传头像...')\n\n        if (this.$refs.avatarUpload) {\n          if (this.$refs.avatarUpload.hasPendingFiles()) {\n            console.log('🎯 AigcAgentForm: 发现待上传的头像，开始上传...')\n            try {\n              const uploadedFiles = await this.$refs.avatarUpload.performUpload()\n              console.log('🎯 AigcAgentForm: 头像上传结果:', uploadedFiles)\n\n              // 上传完成后，获取最终的值\n              const finalValue = this.$refs.avatarUpload.getCurrentValue()\n              this.model.agentAvatar = finalValue\n              console.log('🎯 AigcAgentForm: 头像最终值:', this.model.agentAvatar)\n            } catch (error) {\n              console.error('🎯 AigcAgentForm: 头像上传失败:', error)\n              throw error\n            }\n          } else {\n            // 没有待上传文件，检查是否有删除操作\n            const currentValue = this.$refs.avatarUpload.getCurrentValue()\n            this.model.agentAvatar = currentValue\n            console.log('🎯 AigcAgentForm: 头像无新上传，当前值:', this.model.agentAvatar)\n          }\n        }\n\n        console.log('🎯 AigcAgentForm: 头像处理完成')\n      },\n\n      // 🔥 确认删除被替换的原始头像文件\n      async confirmDeleteOriginalFiles() {\n        if (this.$refs.avatarUpload) {\n          try {\n            await this.$refs.avatarUpload.confirmDeleteOriginalFiles()\n            console.log('🎯 AigcAgentForm: 原始头像文件清理完成')\n          } catch (error) {\n            console.warn('🎯 AigcAgentForm: 原始头像文件清理失败:', error)\n            // 删除失败不影响主流程\n          }\n        }\n      },\n\n      // 🔥 关闭表单时回滚头像变更\n      handleClose() {\n        console.log('🎯 AigcAgentForm: 表单关闭，回滚头像变更')\n\n        if (this.$refs.avatarUpload) {\n          this.$refs.avatarUpload.rollbackChanges()\n        }\n\n        // 发出关闭事件\n        this.$emit('close')\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n</style>"]}]}