{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue?vue&type=template&id=496f8674&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\WorkflowCenter.vue", "mtime": 1753987025534}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"WebsitePage\", [\n    _c(\"div\", { staticClass: \"workflow-center\" }, [\n      _c(\"div\", { staticClass: \"simple-header\" }, [\n        _c(\"h1\", { staticClass: \"simple-title\" }, [_vm._v(\"AI工作流中心\")]),\n        _c(\"p\", { staticClass: \"simple-subtitle\" }, [\n          _vm._v(\n            \"发现和使用优质AI智能体，提升您的创作效率，让每个想法都能完美实现\"\n          )\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"workflow-tabs\" }, [\n        _c(\"div\", { staticClass: \"tabs-container\" }, [\n          _c(\"div\", { staticClass: \"tab-nav\" }, [\n            _c(\n              \"div\",\n              {\n                staticClass: \"tab-item\",\n                class: { active: _vm.activeTab === \"market\" },\n                on: {\n                  click: function($event) {\n                    return _vm.switchTab(\"market\")\n                  }\n                }\n              },\n              [\n                _c(\"a-icon\", { attrs: { type: \"shop\" } }),\n                _c(\"span\", [_vm._v(\"智能体市场\")])\n              ],\n              1\n            ),\n            _c(\n              \"div\",\n              {\n                staticClass: \"tab-item\",\n                class: { active: _vm.activeTab === \"creator\" },\n                on: {\n                  click: function($event) {\n                    return _vm.switchTab(\"creator\")\n                  }\n                }\n              },\n              [\n                _c(\"a-icon\", { attrs: { type: \"build\" } }),\n                _c(\"span\", [_vm._v(\"创作者中心\")])\n              ],\n              1\n            )\n          ])\n        ])\n      ]),\n      _c(\"div\", { staticClass: \"workflow-content\" }, [\n        _c(\"div\", { staticClass: \"content-container\" }, [\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.activeTab === \"market\",\n                  expression: \"activeTab === 'market'\"\n                }\n              ],\n              staticClass: \"tab-content\"\n            },\n            [_c(\"AgentMarket\")],\n            1\n          ),\n          _c(\n            \"div\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.activeTab === \"creator\",\n                  expression: \"activeTab === 'creator'\"\n                }\n              ],\n              staticClass: \"tab-content\"\n            },\n            [_c(\"CreatorCenter\")],\n            1\n          )\n        ])\n      ])\n    ])\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}