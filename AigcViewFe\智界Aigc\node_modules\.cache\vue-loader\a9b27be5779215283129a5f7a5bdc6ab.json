{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue?vue&type=template&id=0abacd97&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\PluginCard.vue", "mtime": 1753944273572}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"plugin-card\" :class=\"{ 'no-transition': disableTransition, 'combined-plugin': isCombinedPlugin }\" @click=\"handlePluginClick('card')\">\n  <!-- 插件图片区域 -->\n  <div class=\"plugin-image\">\n    <div class=\"image-overlay\"></div>\n    <img\n      :src=\"getPluginImage(plugin)\"\n      :alt=\"isCombinedPlugin ? plugin.combinedName : plugin.plubname\"\n      @error=\"handleImageError\"\n      @load=\"handleImageLoad\"\n    />\n\n    <!-- 🔥 组合插件标识 -->\n    <div v-if=\"isCombinedPlugin\" class=\"combined-badge\">\n      <span class=\"badge-icon\">🔗</span>\n      组合插件\n    </div>\n\n    <!-- 分类标签 -->\n    <div class=\"plugin-category\" v-if=\"plugin.plubCategory_dictText\">\n      <span class=\"category-icon\">{{ getCategoryIcon(plugin.plubCategory_dictText) }}</span>\n      {{ plugin.plubCategory_dictText }}\n    </div>\n\n    <!-- 状态标识 -->\n    <div\n      v-if=\"plugin.status_dictText && plugin.status_dictText !== '正常'\"\n      class=\"plugin-badge\"\n      :class=\"getBadgeClass(plugin.status_dictText)\"\n    >\n      {{ plugin.status_dictText }}\n    </div>\n\n    <!-- 图片加载状态 -->\n    <div v-if=\"imageLoading\" class=\"image-loading\">\n      <a-spin size=\"small\" />\n    </div>\n  </div>\n  \n  <!-- 插件信息区域 -->\n  <div class=\"plugin-info\">\n    <!-- 🔥 组合插件信息 -->\n    <div v-if=\"isCombinedPlugin\" class=\"combined-plugin-info\">\n      <!-- 组合插件名称和作者 -->\n      <div class=\"plugin-header-info\">\n        <h3 class=\"plugin-name\" :title=\"plugin.combinedName\">\n          {{ plugin.combinedName }}\n        </h3>\n        <span class=\"plugin-author\" v-if=\"plugin.plubwrite_dictText\">\n          <a-icon type=\"user\" />\n          创作者 {{ plugin.plubwrite_dictText }}\n        </span>\n      </div>\n\n      <!-- 组合插件描述 -->\n      <p class=\"plugin-description\" :title=\"plugin.combinedDescription\">\n        {{ truncateText(plugin.combinedDescription, 100) }}\n      </p>\n\n      <!-- 组合插件底部信息 -->\n      <div class=\"plugin-footer\">\n        <div class=\"plugin-price combined-price\">\n          <span class=\"price-hint\">收费请进入详情查看</span>\n        </div>\n        <a-button\n          type=\"primary\"\n          size=\"default\"\n          @click.stop=\"handlePluginClick('button')\"\n          class=\"detail-button combined-detail-btn\"\n        >\n          <a-icon type=\"eye\" />\n          查看详情\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 🔥 普通插件信息 -->\n    <div v-else class=\"normal-plugin-info\">\n      <!-- 插件名称和作者 -->\n      <div class=\"plugin-header-info\">\n        <h3 class=\"plugin-name\" :title=\"plugin.plubname\">\n          {{ plugin.plubname }}\n        </h3>\n        <span class=\"plugin-author\" v-if=\"plugin.plubwrite_dictText\">\n          <a-icon type=\"user\" />\n          创作者 {{ plugin.plubwrite_dictText }}\n        </span>\n      </div>\n\n      <!-- 插件描述 -->\n      <p class=\"plugin-description\" :title=\"plugin.plubinfo\">\n        {{ truncateText(plugin.plubinfo, 100) }}\n      </p>\n\n      <!-- 价格和操作区域 -->\n      <div class=\"plugin-footer\">\n        <div class=\"plugin-price\">\n          <span class=\"price-value\">{{ getPriceText() }}</span>\n        </div>\n        <a-button\n          type=\"primary\"\n          size=\"default\"\n          @click.stop=\"handlePluginClick('button')\"\n          class=\"detail-button\"\n        >\n          <a-icon type=\"eye\" />\n          查看详情\n        </a-button>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}