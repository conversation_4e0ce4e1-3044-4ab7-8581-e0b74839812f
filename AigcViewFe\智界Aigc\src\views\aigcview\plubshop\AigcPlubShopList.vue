<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件名称">
              <a-input placeholder="请输入插件名称" v-model="queryParam.plubname"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件创作者">
              <j-dict-select-tag placeholder="请选择插件创作者" v-model="queryParam.plubwrite" dictCode="aigc_plub_author,authorname,id"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件状态">
              <j-dict-select-tag placeholder="请选择插件状态" v-model="queryParam.status" dictCode="plugin_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否组合插件">
              <j-dict-select-tag placeholder="请选择是否组合插件" v-model="queryParam.isCombined" dictCode="isTrue"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否SVIP免费">
              <j-dict-select-tag placeholder="请选择是否SVIP免费" v-model="queryParam.isSvipFree" dictCode="isTrue"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="组合插件名">
                <a-input placeholder="请输入组合插件名" v-model="queryParam.combinedName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="收益金额">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.income_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.income_end"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="调用次数">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.usernum_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.usernum_end"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="需要金额">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.neednum_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.neednum_end"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('插件商城')">导出</a-button>

      <!-- admin用户才能看到的功能 -->
      <template v-if="isAdmin">
        <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
        </a-upload>
        <!-- 高级查询区域 -->
        <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
        </a-dropdown>
      </template>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <template slot="tutorialLinkSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">暂无</span>
          <a v-else :href="text" target="_blank" style="color: #1890ff;">
            <a-icon type="link" style="margin-right: 4px;" />
            查看教程
          </a>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <!-- 只有admin用户才能看到更多操作 -->
          <template v-if="isAdmin">
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleDetail(record)">详情</a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </span>

      </a-table>
    </div>

    <aigc-plub-shop-modal ref="modalForm" @ok="modalFormOk"></aigc-plub-shop-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcPlubShopModal from './modules/AigcPlubShopModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'AigcPlubShopList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AigcPlubShopModal
    },
    data () {
      return {
        description: '插件商城管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'插件名称',
            align:"center",
            dataIndex: 'plubname'
          },
          {
            title:'图片',
            align:"center",
            dataIndex: 'plubimg',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'插件创作者',
            align:"center",
            dataIndex: 'plubwrite_dictText'
          },
          {
            title:'插件介绍',
            align:"center",
            dataIndex: 'plubinfo'
          },
          {
            title:'插件分类',
            align:"center",
            dataIndex: 'plubCategory_dictText'
          },
          {
            title:'插件状态',
            align:"center",
            dataIndex: 'status_dictText'
          },
          {
            title:'是否组合插件',
            align:"center",
            dataIndex: 'isCombined_dictText',
            width: 120
          },
          {
            title:'组合插件名',
            align:"center",
            dataIndex: 'combinedName',
            width: 150,
            customRender: (text) => {
              return text || '-';
            }
          },
          {
            title:'组合插件图片',
            align:"center",
            dataIndex: 'combinedImage',
            width: 120,
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'插件唯一标识',
            align:"center",
            dataIndex: 'pluginKey'
          },
          {
            title:'教程链接',
            align:"center",
            dataIndex: 'tutorialLink',
            scopedSlots: {customRender: 'tutorialLinkSlot'}
          },
          {
            title:'排序权重',
            align:"center",
            dataIndex: 'sortOrder',
            sorter: true,
            defaultSortOrder: 'ascend'
          },
          {
            title:'插件教程视频',
            align:"center",
            dataIndex: 'plubvideo',
            scopedSlots: {customRender: 'fileSlot'}
          },
          {
            title:'收益金额',
            align:"center",
            dataIndex: 'income',
            customRender: function (text) {
              return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';
            }
          },
          {
            title:'调用次数',
            align:"center",
            dataIndex: 'usernum'
          },
          {
            title:'需要金额',
            align:"center",
            dataIndex: 'neednum',
            customRender: function (text) {
              return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';
            }
          },
          {
            title:'SVIP是否免费',
            align:"center",
            dataIndex: 'isSvipFree_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width: this.isAdmin ? 200 : 80, // admin用户宽度147，非admin用户宽度80
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/plubshop/aigcPlubShop/list",
          delete: "/plubshop/aigcPlubShop/delete",
          deleteBatch: "/plubshop/aigcPlubShop/deleteBatch",
          exportXlsUrl: "/plubshop/aigcPlubShop/exportXls",
          importExcelUrl: "plubshop/aigcPlubShop/importExcel",

        },
        // 🎯 覆盖默认排序：按排序权重升序排列（数字小的在前）
        isorter: {
          column: 'sortOrder',
          order: 'asc',
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
      isAdmin() {
        // 判断当前用户是否为admin角色（基于role_code）
        const userRole = localStorage.getItem('userRole');
        return userRole && userRole.toLowerCase().includes('admin');
      }
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'plubname',text:'插件名称',dictCode:''})
        fieldList.push({type:'string',value:'plubimg',text:'图片',dictCode:''})
        fieldList.push({type:'string',value:'plubwrite',text:'插件创作者',dictCode:'aigc_plub_author,authorname,id'})
        fieldList.push({type:'string',value:'plubinfo',text:'插件介绍',dictCode:''})
        fieldList.push({type:'string',value:'plubContent',text:'插件详细内容',dictCode:''})
        fieldList.push({type:'string',value:'plubCategory',text:'插件分类',dictCode:'plugin_category'})
        fieldList.push({type:'int',value:'status',text:'插件状态',dictCode:'plugin_status'})
        fieldList.push({type:'int',value:'isCombined',text:'是否组合插件',dictCode:'isTrue'})
        fieldList.push({type:'int',value:'isSvipFree',text:'SVIP是否免费',dictCode:'isTrue'})
        fieldList.push({type:'string',value:'combinedName',text:'组合插件名',dictCode:''})
        fieldList.push({type:'string',value:'combinedDescription',text:'组合插件介绍',dictCode:''})
        fieldList.push({type:'string',value:'combinedImage',text:'组合插件图片',dictCode:''})
        fieldList.push({type:'string',value:'pluginKey',text:'插件唯一标识',dictCode:''})
        fieldList.push({type:'int',value:'sortOrder',text:'排序权重',dictCode:''})
        fieldList.push({type:'string',value:'plubvideo',text:'插件教程视频',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'income',text:'收益金额',dictCode:''})
        fieldList.push({type:'int',value:'usernum',text:'调用次数',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'neednum',text:'需要金额',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>