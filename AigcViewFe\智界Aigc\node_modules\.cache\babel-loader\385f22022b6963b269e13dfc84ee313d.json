{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\market\\components\\CombinedPluginModal.vue", "mtime": 1753944358622}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getAction } from '@/api/manage';\nimport { getPluginImageUrl } from '../utils/marketUtils';\nexport default {\n  name: 'CombinedPluginModal',\n  props: {\n    value: {\n      type: Boolean,\n      default: false\n    },\n    combinedPlugin: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      subPlugins: []\n    };\n  },\n  mounted: function mounted() {\n    console.log('🔗 CombinedPluginModal - 组件已挂载');\n    console.log('🔗 CombinedPluginModal - 初始visible:', this.visible);\n    console.log('🔗 CombinedPluginModal - 初始value:', this.value);\n  },\n  computed: {\n    modalTitle: function modalTitle() {\n      return this.combinedPlugin && this.combinedPlugin.combinedName ? \"\\u9009\\u62E9\\u63D2\\u4EF6 - \".concat(this.combinedPlugin.combinedName) : '选择插件';\n    },\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage: function defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';\n    }\n  },\n  watch: {\n    value: {\n      immediate: true,\n      handler: function handler(newVal) {\n        console.log('🔗 CombinedPluginModal - value变化:', newVal);\n        this.visible = newVal;\n\n        if (newVal && this.combinedPlugin && this.combinedPlugin.combinedName) {\n          this.loadSubPlugins();\n        }\n      }\n    },\n    visible: function visible(newVal) {\n      console.log('🔗 CombinedPluginModal - visible变化:', newVal);\n      this.$emit('input', newVal);\n    } // visible 不再需要watch，因为我们直接使用props\n\n  },\n  methods: {\n    // 🔥 加载子插件列表\n    loadSubPlugins: function () {\n      var _loadSubPlugins = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        var response, result;\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                if (this.combinedPlugin.combinedName) {\n                  _context.next = 3;\n                  break;\n                }\n\n                this.$notification.warning({\n                  message: '参数错误',\n                  description: '组合插件名称不能为空',\n                  placement: 'topRight'\n                });\n                return _context.abrupt(\"return\");\n\n              case 3:\n                this.loading = true;\n                _context.prev = 4;\n                console.log('🔍 加载组合插件子插件:', this.combinedPlugin.combinedName); // 使用原有的公开API，通过前端筛选获取子插件\n\n                _context.next = 8;\n                return getAction('/plubshop/aigcPlubShop/list', {\n                  pageNo: 1,\n                  pageSize: 100,\n                  combinedName: this.combinedPlugin.combinedName,\n                  isCombined: 1,\n                  status: 1\n                });\n\n              case 8:\n                response = _context.sent;\n\n                if (response.success) {\n                  result = response.result || response.data;\n                  this.subPlugins = result.records || result || [];\n                  console.log('✅ 子插件加载成功:', this.subPlugins.length);\n                } else {\n                  this.$notification.error({\n                    message: '加载失败',\n                    description: '获取子插件列表失败：' + response.message,\n                    placement: 'topRight'\n                  });\n                  this.subPlugins = [];\n                }\n\n                _context.next = 17;\n                break;\n\n              case 12:\n                _context.prev = 12;\n                _context.t0 = _context[\"catch\"](4);\n                console.error('❌ 获取子插件列表异常:', _context.t0);\n                this.$notification.error({\n                  message: '系统异常',\n                  description: '获取子插件列表异常',\n                  placement: 'topRight'\n                });\n                this.subPlugins = [];\n\n              case 17:\n                _context.prev = 17;\n                this.loading = false;\n                return _context.finish(17);\n\n              case 20:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, this, [[4, 12, 17, 20]]);\n      }));\n\n      function loadSubPlugins() {\n        return _loadSubPlugins.apply(this, arguments);\n      }\n\n      return loadSubPlugins;\n    }(),\n    // 🔥 选择子插件\n    selectSubPlugin: function selectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname);\n      this.$emit('select-sub-plugin', subPlugin);\n    },\n    // 🔥 关闭弹窗\n    handleCancel: function handleCancel() {\n      this.visible = false;\n      this.subPlugins = [];\n    },\n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage: function getPluginImage(pluginOrPath) {\n      // 如果传入的是字符串路径，保持向后兼容\n      if (typeof pluginOrPath === 'string') {\n        return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);\n      } // 如果传入的是插件对象，使用新的优先级逻辑\n\n\n      return getPluginImageUrl(pluginOrPath, this.defaultPluginImage);\n    },\n    // 🔥 处理图片加载错误\n    handleImageError: function handleImageError(event) {\n      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';\n    },\n    handleSubImageError: function handleSubImageError(event) {\n      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg';\n    },\n    // 🔥 获取分类文本\n    getCategoryText: function getCategoryText(categoryValue) {\n      if (this.$categoryService) {\n        return this.$categoryService.getCategoryText(categoryValue);\n      }\n\n      return categoryValue || '未知分类';\n    },\n    // 🔥 截断文本\n    truncateText: function truncateText(text, maxLength) {\n      if (!text) return '';\n      if (text.length <= maxLength) return text;\n      return text.substring(0, maxLength) + '...';\n    },\n    // 🔥 格式化价格\n    formatPrice: function formatPrice(price) {\n      if (price === null || price === undefined) return '0';\n      return parseFloat(price).toFixed(2);\n    },\n    // 🔥 获取子插件价格显示文本\n    getSubPluginPriceText: function getSubPluginPriceText(subPlugin) {\n      var price = subPlugin.neednum;\n      var isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1';\n\n      if (!price || price <= 0) {\n        return '免费';\n      }\n\n      if (isSvipFree) {\n        return \"SVIP\\u514D\\u8D39\\uFF0C\\u4F4E\\u81F3\\xA5\".concat(this.formatPrice(price), \"/\\u6B21\");\n      } else {\n        return \"\\u4F4E\\u81F3\\xA5\".concat(this.formatPrice(price), \"/\\u6B21\");\n      }\n    }\n  }\n};", {"version": 3, "sources": ["CombinedPluginModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA,SAAA,SAAA,QAAA,cAAA;AACA,SAAA,iBAAA,QAAA,sBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,qBADA;AAGA,EAAA,KAAA,EAAA;AACA,IAAA,KAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,cAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAAA,eAAA,EAAA;AAAA;AAFA;AALA,GAHA;AAcA,EAAA,IAdA,kBAcA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAGA,MAAA,UAAA,EAAA;AAHA,KAAA;AAKA,GApBA;AAsBA,EAAA,OAtBA,qBAsBA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,KAAA,OAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,KAAA,KAAA;AACA,GA1BA;AA4BA,EAAA,QAAA,EAAA;AACA,IAAA,UADA,wBACA;AACA,aAAA,KAAA,cAAA,IAAA,KAAA,cAAA,CAAA,YAAA,wCACA,KAAA,cAAA,CAAA,YADA,IAEA,MAFA;AAGA,KALA;AAOA;AACA,IAAA,kBARA,gCAQA;AACA,aAAA,2DAAA;AACA;AAVA,GA5BA;AA2CA,EAAA,KAAA,EAAA;AACA,IAAA,KAAA,EAAA;AACA,MAAA,SAAA,EAAA,IADA;AAEA,MAAA,OAFA,mBAEA,MAFA,EAEA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,MAAA;AACA,aAAA,OAAA,GAAA,MAAA;;AACA,YAAA,MAAA,IAAA,KAAA,cAAA,IAAA,KAAA,cAAA,CAAA,YAAA,EAAA;AACA,eAAA,cAAA;AACA;AACA;AARA,KADA;AAYA,IAAA,OAZA,mBAYA,MAZA,EAYA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,MAAA;AACA,WAAA,KAAA,CAAA,OAAA,EAAA,MAAA;AACA,KAfA,CAiBA;;AAjBA,GA3CA;AA+DA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,cAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAGA,KAAA,cAAA,CAAA,YAHA;AAAA;AAAA;AAAA;;AAIA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,YAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AAJA;;AAAA;AAYA,qBAAA,OAAA,GAAA,IAAA;AAZA;AAcA,gBAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,KAAA,cAAA,CAAA,YAAA,EAdA,CAgBA;;AAhBA;AAAA,uBAiBA,SAAA,CAAA,6BAAA,EAAA;AACA,kBAAA,MAAA,EAAA,CADA;AAEA,kBAAA,QAAA,EAAA,GAFA;AAGA,kBAAA,YAAA,EAAA,KAAA,cAAA,CAAA,YAHA;AAIA,kBAAA,UAAA,EAAA,CAJA;AAKA,kBAAA,MAAA,EAAA;AALA,iBAAA,CAjBA;;AAAA;AAiBA,gBAAA,QAjBA;;AAyBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MADA,GACA,QAAA,CAAA,MAAA,IAAA,QAAA,CAAA,IADA;AAEA,uBAAA,UAAA,GAAA,MAAA,CAAA,OAAA,IAAA,MAAA,IAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,UAAA,CAAA,MAAA;AACA,iBAJA,MAIA;AACA,uBAAA,aAAA,CAAA,KAAA,CAAA;AACA,oBAAA,OAAA,EAAA,MADA;AAEA,oBAAA,WAAA,EAAA,eAAA,QAAA,CAAA,OAFA;AAGA,oBAAA,SAAA,EAAA;AAHA,mBAAA;AAKA,uBAAA,UAAA,GAAA,EAAA;AACA;;AApCA;AAAA;;AAAA;AAAA;AAAA;AAsCA,gBAAA,OAAA,CAAA,KAAA,CAAA,cAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,MADA;AAEA,kBAAA,WAAA,EAAA,WAFA;AAGA,kBAAA,SAAA,EAAA;AAHA,iBAAA;AAKA,qBAAA,UAAA,GAAA,EAAA;;AA5CA;AAAA;AA8CA,qBAAA,OAAA,GAAA,KAAA;AA9CA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkDA;AACA,IAAA,eAnDA,2BAmDA,SAnDA,EAmDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,SAAA,CAAA,QAAA;AACA,WAAA,KAAA,CAAA,mBAAA,EAAA,SAAA;AACA,KAtDA;AAwDA;AACA,IAAA,YAzDA,0BAyDA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,EAAA;AACA,KA5DA;AA8DA;AACA,IAAA,cA/DA,0BA+DA,YA/DA,EA+DA;AACA;AACA,UAAA,OAAA,YAAA,KAAA,QAAA,EAAA;AACA,eAAA,iBAAA,CAAA,YAAA,EAAA,KAAA,kBAAA,CAAA;AACA,OAJA,CAMA;;;AACA,aAAA,iBAAA,CAAA,YAAA,EAAA,KAAA,kBAAA,CAAA;AACA,KAvEA;AAyEA;AACA,IAAA,gBA1EA,4BA0EA,KA1EA,EA0EA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,GAAA,GAAA,2DAAA;AACA,KA5EA;AA8EA,IAAA,mBA9EA,+BA8EA,KA9EA,EA8EA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,GAAA,GAAA,2DAAA;AACA,KAhFA;AAkFA;AACA,IAAA,eAnFA,2BAmFA,aAnFA,EAmFA;AACA,UAAA,KAAA,gBAAA,EAAA;AACA,eAAA,KAAA,gBAAA,CAAA,eAAA,CAAA,aAAA,CAAA;AACA;;AACA,aAAA,aAAA,IAAA,MAAA;AACA,KAxFA;AA0FA;AACA,IAAA,YA3FA,wBA2FA,IA3FA,EA2FA,SA3FA,EA2FA;AACA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA;AACA,UAAA,IAAA,CAAA,MAAA,IAAA,SAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,KAAA;AACA,KA/FA;AAiGA;AACA,IAAA,WAlGA,uBAkGA,KAlGA,EAkGA;AACA,UAAA,KAAA,KAAA,IAAA,IAAA,KAAA,KAAA,SAAA,EAAA,OAAA,GAAA;AACA,aAAA,UAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KArGA;AAuGA;AACA,IAAA,qBAxGA,iCAwGA,SAxGA,EAwGA;AACA,UAAA,KAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,UAAA,GAAA,SAAA,CAAA,UAAA,KAAA,CAAA,IAAA,SAAA,CAAA,UAAA,KAAA,GAAA;;AAEA,UAAA,CAAA,KAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,eAAA,IAAA;AACA;;AAEA,UAAA,UAAA,EAAA;AACA,+DAAA,KAAA,WAAA,CAAA,KAAA,CAAA;AACA,OAFA,MAEA;AACA,yCAAA,KAAA,WAAA,CAAA,KAAA,CAAA;AACA;AACA;AArHA;AA/DA,CAAA", "sourcesContent": ["<template>\n  <a-modal\n    :visible=\"visible\"\n    :title=\"modalTitle\"\n    width=\"1200px\"\n    :footer=\"null\"\n    @cancel=\"handleCancel\"\n    :z-index=\"2000\"\n    :mask-closable=\"true\"\n    :destroy-on-close=\"false\"\n    class=\"combined-plugin-modal\"\n    :get-container=\"() => document.body\">\n    \n    <!-- 🔥 组合插件头部信息 -->\n    <div class=\"combined-plugin-header\">\n      <div class=\"combined-info\">\n        <div class=\"combined-image\">\n          <img\n            :src=\"getPluginImage(combinedPlugin)\"\n            :alt=\"combinedPlugin && combinedPlugin.combinedName\"\n            @error=\"handleImageError\" />\n        </div>\n        <div class=\"combined-details\">\n          <h2 class=\"combined-title\">{{ combinedPlugin && combinedPlugin.combinedName }}</h2>\n          <p class=\"combined-description\">{{ combinedPlugin && combinedPlugin.combinedDescription }}</p>\n          <div class=\"combined-meta\">\n            <span class=\"meta-item\">\n              <a-icon type=\"user\" />\n              创作者：{{ (combinedPlugin && combinedPlugin.plubwrite_dictText) || '未知' }}\n            </span>\n            <span class=\"meta-item\">\n              <a-icon type=\"tag\" />\n              分类：{{ getCategoryText(combinedPlugin && combinedPlugin.plubCategory) }}\n            </span>\n            <span class=\"meta-item\">\n              <a-icon type=\"link\" />\n              组合插件\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <a-divider>\n      <span class=\"divider-text\">\n        <a-icon type=\"appstore\" />\n        包含的插件 ({{ subPlugins.length }})\n      </span>\n    </a-divider>\n\n    <!-- 🔥 子插件网格展示 -->\n    <div class=\"sub-plugins-container\" v-loading=\"loading\">\n      <div v-if=\"loading\" class=\"loading-container\">\n        <a-spin size=\"large\">\n          <span slot=\"tip\">正在加载插件列表...</span>\n        </a-spin>\n      </div>\n      \n      <div v-else-if=\"subPlugins.length === 0\" class=\"empty-container\">\n        <a-empty description=\"暂无子插件\" />\n      </div>\n      \n      <div v-else class=\"sub-plugins-grid\">\n        <div \n          v-for=\"subPlugin in subPlugins\" \n          :key=\"subPlugin.id\"\n          class=\"sub-plugin-card\"\n          @click=\"selectSubPlugin(subPlugin)\">\n          \n          <div class=\"sub-plugin-image\">\n            <img\n              :src=\"getPluginImage(subPlugin)\"\n              :alt=\"subPlugin.plubname\"\n              @error=\"handleSubImageError\" />\n            <div class=\"plugin-overlay\">\n              <a-icon type=\"eye\" class=\"view-icon\" />\n            </div>\n          </div>\n          \n          <div class=\"sub-plugin-info\">\n            <h4 class=\"sub-plugin-title\" :title=\"subPlugin.plubname\">\n              {{ subPlugin.plubname }}\n            </h4>\n            <p class=\"sub-plugin-description\" :title=\"subPlugin.plubinfo\">\n              {{ truncateText(subPlugin.plubinfo, 60) }}\n            </p>\n            <div class=\"sub-plugin-meta\">\n              <span class=\"category-tag\">\n                {{ getCategoryText(subPlugin.plubCategory) }}\n              </span>\n              <span class=\"price-tag\">\n                {{ getSubPluginPriceText(subPlugin) }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"sub-plugin-actions\">\n            <a-button type=\"primary\" size=\"small\" @click.stop=\"selectSubPlugin(subPlugin)\">\n              <a-icon type=\"eye\" />\n              查看详情\n            </a-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script>\nimport { getAction } from '@/api/manage'\nimport { getPluginImageUrl } from '../utils/marketUtils'\n\nexport default {\n  name: 'CombinedPluginModal',\n  \n  props: {\n    value: {\n      type: Boolean,\n      default: false\n    },\n    combinedPlugin: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      visible: false,\n      loading: false,\n      subPlugins: []\n    }\n  },\n\n  mounted() {\n    console.log('🔗 CombinedPluginModal - 组件已挂载')\n    console.log('🔗 CombinedPluginModal - 初始visible:', this.visible)\n    console.log('🔗 CombinedPluginModal - 初始value:', this.value)\n  },\n  \n  computed: {\n    modalTitle() {\n      return (this.combinedPlugin && this.combinedPlugin.combinedName) ?\n        `选择插件 - ${this.combinedPlugin.combinedName}` :\n        '选择插件'\n    },\n\n    // 🔥 默认插件图片（通过统一接口获取，支持TOS重定向）\n    defaultPluginImage() {\n      return '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    }\n  },\n\n\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        console.log('🔗 CombinedPluginModal - value变化:', newVal)\n        this.visible = newVal\n        if (newVal && this.combinedPlugin && this.combinedPlugin.combinedName) {\n          this.loadSubPlugins()\n        }\n      }\n    },\n\n    visible(newVal) {\n      console.log('🔗 CombinedPluginModal - visible变化:', newVal)\n      this.$emit('input', newVal)\n    }\n\n    // visible 不再需要watch，因为我们直接使用props\n  },\n  \n  methods: {\n    // 🔥 加载子插件列表\n    async loadSubPlugins() {\n      if (!this.combinedPlugin.combinedName) {\n        this.$notification.warning({\n          message: '参数错误',\n          description: '组合插件名称不能为空',\n          placement: 'topRight'\n        })\n        return\n      }\n\n      this.loading = true\n      try {\n        console.log('🔍 加载组合插件子插件:', this.combinedPlugin.combinedName)\n\n        // 使用原有的公开API，通过前端筛选获取子插件\n        const response = await getAction('/plubshop/aigcPlubShop/list', {\n          pageNo: 1,\n          pageSize: 100,\n          combinedName: this.combinedPlugin.combinedName,\n          isCombined: 1,\n          status: 1\n        })\n\n        if (response.success) {\n          const result = response.result || response.data\n          this.subPlugins = result.records || result || []\n          console.log('✅ 子插件加载成功:', this.subPlugins.length)\n        } else {\n          this.$notification.error({\n            message: '加载失败',\n            description: '获取子插件列表失败：' + response.message,\n            placement: 'topRight'\n          })\n          this.subPlugins = []\n        }\n      } catch (error) {\n        console.error('❌ 获取子插件列表异常:', error)\n        this.$notification.error({\n          message: '系统异常',\n          description: '获取子插件列表异常',\n          placement: 'topRight'\n        })\n        this.subPlugins = []\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 🔥 选择子插件\n    selectSubPlugin(subPlugin) {\n      console.log('🎯 选择子插件:', subPlugin.plubname)\n      this.$emit('select-sub-plugin', subPlugin)\n    },\n    \n    // 🔥 关闭弹窗\n    handleCancel() {\n      this.visible = false\n      this.subPlugins = []\n    },\n    \n    // 🔥 获取插件图片（支持组合插件优先级处理）\n    getPluginImage(pluginOrPath) {\n      // 如果传入的是字符串路径，保持向后兼容\n      if (typeof pluginOrPath === 'string') {\n        return getPluginImageUrl(pluginOrPath, this.defaultPluginImage)\n      }\n\n      // 如果传入的是插件对象，使用新的优先级逻辑\n      return getPluginImageUrl(pluginOrPath, this.defaultPluginImage)\n    },\n\n    // 🔥 处理图片加载错误\n    handleImageError(event) {\n      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    },\n\n    handleSubImageError(event) {\n      event.target.src = '/jeecg-boot/sys/common/static/defaults/plugin-default.jpg'\n    },\n    \n    // 🔥 获取分类文本\n    getCategoryText(categoryValue) {\n      if (this.$categoryService) {\n        return this.$categoryService.getCategoryText(categoryValue)\n      }\n      return categoryValue || '未知分类'\n    },\n    \n    // 🔥 截断文本\n    truncateText(text, maxLength) {\n      if (!text) return ''\n      if (text.length <= maxLength) return text\n      return text.substring(0, maxLength) + '...'\n    },\n    \n    // 🔥 格式化价格\n    formatPrice(price) {\n      if (price === null || price === undefined) return '0'\n      return parseFloat(price).toFixed(2)\n    },\n\n    // 🔥 获取子插件价格显示文本\n    getSubPluginPriceText(subPlugin) {\n      const price = subPlugin.neednum\n      const isSvipFree = subPlugin.isSvipFree === 1 || subPlugin.isSvipFree === '1'\n\n      if (!price || price <= 0) {\n        return '免费'\n      }\n\n      if (isSvipFree) {\n        return `SVIP免费，低至¥${this.formatPrice(price)}/次`\n      } else {\n        return `低至¥${this.formatPrice(price)}/次`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 🔥 强制显示Modal */\n.combined-plugin-modal {\n  z-index: 2000 !important;\n}\n\n.combined-plugin-modal .ant-modal {\n  z-index: 2000 !important;\n}\n\n.combined-plugin-modal .ant-modal-mask {\n  z-index: 1999 !important;\n  background-color: rgba(0, 0, 0, 0.45) !important;\n}\n\n.combined-plugin-modal .ant-modal-wrap {\n  z-index: 2000 !important;\n}\n\n.combined-plugin-modal .ant-modal-body {\n  padding: 24px;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n/* 🔥 组合插件头部 */\n.combined-plugin-header {\n  margin-bottom: 24px;\n}\n\n.combined-info {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n.combined-image {\n  flex-shrink: 0;\n  width: 120px;\n  height: 120px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.combined-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.combined-details {\n  flex: 1;\n}\n\n.combined-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 12px 0;\n}\n\n.combined-description {\n  color: #64748b;\n  font-size: 1rem;\n  line-height: 1.6;\n  margin: 0 0 16px 0;\n}\n\n.combined-meta {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  color: #64748b;\n  font-size: 0.9rem;\n}\n\n.meta-item .anticon {\n  color: #3b82f6;\n}\n\n/* 🔥 分割线 */\n.divider-text {\n  color: #64748b;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 🔥 子插件容器 */\n.sub-plugins-container {\n  min-height: 200px;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n}\n\n.empty-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n}\n\n/* 🔥 子插件网格 */\n.sub-plugins-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n}\n\n.sub-plugin-card {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.sub-plugin-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n  border-color: #3b82f6;\n}\n\n.sub-plugin-image {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  overflow: hidden;\n}\n\n.sub-plugin-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.sub-plugin-card:hover .sub-plugin-image img {\n  transform: scale(1.05);\n}\n\n.plugin-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(59, 130, 246, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.sub-plugin-card:hover .plugin-overlay {\n  opacity: 1;\n}\n\n.view-icon {\n  color: white;\n  font-size: 2rem;\n}\n\n.sub-plugin-info {\n  padding: 16px;\n}\n\n.sub-plugin-title {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 8px 0;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.sub-plugin-description {\n  color: #64748b;\n  font-size: 0.9rem;\n  line-height: 1.5;\n  margin: 0 0 12px 0;\n  height: 2.7rem;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.sub-plugin-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.category-tag {\n  background: #f1f5f9;\n  color: #475569;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 0.8rem;\n  font-weight: 500;\n}\n\n.price-tag {\n  color: #3b82f6;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.sub-plugin-actions {\n  padding: 0 16px 16px 16px;\n}\n\n.sub-plugin-actions .ant-btn {\n  width: 100%;\n  border-radius: 8px;\n  font-weight: 500;\n}\n\n/* 🔥 响应式设计 */\n@media (max-width: 768px) {\n  .combined-info {\n    flex-direction: column;\n    text-align: center;\n  }\n  \n  .combined-image {\n    align-self: center;\n  }\n  \n  .sub-plugins-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .combined-plugin-modal .ant-modal {\n    width: 95% !important;\n    margin: 10px;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/market/components"}]}