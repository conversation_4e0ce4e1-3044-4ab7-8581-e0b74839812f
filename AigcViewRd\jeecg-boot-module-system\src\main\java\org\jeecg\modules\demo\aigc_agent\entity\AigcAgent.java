package org.jeecg.modules.demo.aigc_agent.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 智能体表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@ApiModel(value="aigc_agent对象", description="智能体表")
@Data
@TableName("aigc_agent")
public class AigcAgent implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**作者类型*/
	@Excel(name = "作者类型", width = 15, dicCode = "author_type")
    @Dict(dicCode = "author_type")
    @ApiModelProperty(value = "作者类型")
    private java.lang.String authorType;
	/**智能体ID*/
	@Excel(name = "智能体ID", width = 15)
    @ApiModelProperty(value = "智能体ID")
    private java.lang.String agentId;
	/**智能体名称*/
	@Excel(name = "智能体名称", width = 15)
    @ApiModelProperty(value = "智能体名称")
    private java.lang.String agentName;
	/**智能体描述*/
	@Excel(name = "智能体描述", width = 15)
    @ApiModelProperty(value = "智能体描述")
    private java.lang.String agentDescription;
	/**智能体头像*/
	@Excel(name = "智能体头像", width = 15)
    @ApiModelProperty(value = "智能体头像")
    private java.lang.String agentAvatar;
	/**展示视频*/
	@Excel(name = "展示视频", width = 15)
    @ApiModelProperty(value = "展示视频")
    private java.lang.String demoVideo;
	/**体验链接*/
	@Excel(name = "体验链接", width = 15)
    @ApiModelProperty(value = "体验链接")
    private java.lang.String experienceLink;
	/**价格（元）*/
	@Excel(name = "价格（元）", width = 15)
    @ApiModelProperty(value = "价格（元）")
    private java.math.BigDecimal price;
	/**审核状态*/
	@Excel(name = "审核状态", width = 15, dicCode = "audit_status")
    @Dict(dicCode = "audit_status")
    @ApiModelProperty(value = "审核状态")
    private java.lang.String auditStatus;
	/**审核备注*/
	@Excel(name = "审核备注", width = 15)
    @ApiModelProperty(value = "审核备注")
    private java.lang.String auditRemark;
}
